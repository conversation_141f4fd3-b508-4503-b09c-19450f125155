"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { usePreloadedQuery } from "convex/react";
import { usePreloadedData } from "@/hooks/use-preloaded-data";

const ALLOWED_PATHS = [
  "/onboarding",
  "/onboarding/categories",
  "/onboarding/subcategories",
  "/onboarding/loading",
  "/login",
  "/privacy-policy",
  "/legal",
  "/legal/",
  "/legal/terms-of-service",
  "/legal/privacy-policy",
];

export function OnboardingChecker() {
  const router = useRouter();
  const pathname = usePathname();
  
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);

  useEffect(() => {
    if (!user) return;

    if (
      user && 
      !user.finishedSignUp && 
      !ALLOWED_PATHS.some(path => pathname === path || pathname.startsWith(path))
    ) {
      router.push("/onboarding");
    }
  }, [user, pathname, router]);

  return null;
} 