"use client";

import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { toast } from "sonner";

export function useUploadFiles() {
  const [isUploading, setIsUploading] = useState(false);
  const generateUploadUrl = useMutation(api.products.generateUploadUrl);

  const uploadFiles = async (files: File[]): Promise<Id<"_storage">[]> => {
    try {
      setIsUploading(true);
      const uploadPromises = files.map(async (file) => {
        const postUrl = await generateUploadUrl();
        const result = await fetch(postUrl, {
          method: "POST",
          headers: { "Content-Type": file.type },
          body: file,
        });

        const { storageId } = await result.json();
        return storageId;
      });

      const storageIds = await Promise.all(uploadPromises);
      return storageIds;
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Failed to upload files");
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  return { uploadFiles, isUploading };
} 