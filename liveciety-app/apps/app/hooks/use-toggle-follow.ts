import { useState, useEffect, useCallback } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { toast } from "sonner";
import { Id } from "@workspace/backend/convex/_generated/dataModel"

interface UseToggleFollowOptions {
  userId?: Id<"users">;
  username?: string;
  skip?: boolean;
}

export function useToggleFollow({ userId, username, skip }: UseToggleFollowOptions) {
  const [isFollowing, setIsFollowing] = useState(false);
  const [loading, setLoading] = useState(false);

  // Only run if userId is provided and not skipped
  const followStatus = useQuery(
    api.users.isFollowing,
    userId && !skip ? { targetUserId: userId } : "skip"
  );

  const followUser = useMutation(api.users.followUser);
  const unfollowUser = useMutation(api.users.unfollowUser);

  useEffect(() => {
    if (followStatus !== undefined) {
      setIsFollowing(followStatus);
    }
  }, [followStatus]);

  const toggleFollow = useCallback(async () => {
    if (!userId) return;
    setLoading(true);
    try {
      setIsFollowing((prev) => !prev);
      if (isFollowing) {
        await unfollowUser({ targetUserId: userId });
        toast.success(`Unfollowed ${username || "user"}`);
      } else {
        await followUser({ targetUserId: userId });
        toast.success(`Following ${username || "user"}`);
      }
    } catch (error) {
      setIsFollowing(isFollowing); // revert
      toast.error("Failed to update follow status");
      // eslint-disable-next-line no-console
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [userId, username, isFollowing, followUser, unfollowUser]);

  return { isFollowing, toggleFollow, loading };
} 