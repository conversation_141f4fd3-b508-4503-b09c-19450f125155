"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { toast } from "sonner";

interface ImageState {
  isLoading: boolean;
  error: string | null;
}

export function useProfileImages() {
  // State for tracking upload/delete operations
  const [profileImageState, setProfileImageState] = useState<ImageState>({
    isLoading: false,
    error: null
  });
  
  const [coverImageState, setCoverImageState] = useState<ImageState>({
    isLoading: false,
    error: null
  });

  // Mutations for profile image operations
  const updateProfileImage = useMutation(api.files.updateProfileImage);
  const updateCoverImage = useMutation(api.files.updateCoverImage);
  const removeProfileImage = useMutation(api.files.removeProfileImage);
  const removeCoverImage = useMutation(api.files.removeCoverImage);

  /**
   * Upload a profile image
   */
  const uploadProfileImage = async (
    storageId: Id<"_storage">,
    userId: Id<"users">
  ): Promise<string | undefined> => {
    try {
      setProfileImageState({ isLoading: true, error: null });
      
      // Update the profile image in the database
      await updateProfileImage({ userId, storageId });
      
      toast.success("Profile image updated");
      return storageId as unknown as string;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update profile image";
      setProfileImageState({ isLoading: false, error: errorMessage });
      toast.error(errorMessage);
      return undefined;
    } finally {
      setProfileImageState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Upload a cover image
   */
  const uploadCoverImage = async (
    storageId: Id<"_storage">,
    userId: Id<"users">
  ): Promise<Id<"_storage"> | undefined> => {
    try {
      setCoverImageState({ isLoading: true, error: null });
      
      // Update the cover image in the database
      await updateCoverImage({ userId, storageId });
      
      toast.success("Cover image updated");
      return storageId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update cover image";
      setCoverImageState({ isLoading: false, error: errorMessage });
      toast.error(errorMessage);
      return undefined;
    } finally {
      setCoverImageState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Remove the profile image
   */
  const handleRemoveProfileImage = async (userId: Id<"users">): Promise<boolean> => {
    try {
      setProfileImageState({ isLoading: true, error: null });
      
      // Remove the profile image from storage and update the user record
      await removeProfileImage({ userId });
      
      toast.success("Profile image removed");
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to remove profile image";
      setProfileImageState({ isLoading: false, error: errorMessage });
      toast.error(errorMessage);
      return false;
    } finally {
      setProfileImageState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Remove the cover image
   */
  const handleRemoveCoverImage = async (userId: Id<"users">): Promise<boolean> => {
    try {
      setCoverImageState({ isLoading: true, error: null });
      
      // Remove the cover image from storage and update the user record
      await removeCoverImage({ userId });
      
      toast.success("Cover image removed");
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to remove cover image";
      setCoverImageState({ isLoading: false, error: errorMessage });
      toast.error(errorMessage);
      return false;
    } finally {
      setCoverImageState(prev => ({ ...prev, isLoading: false }));
    }
  };

  return {
    // Upload functions
    uploadProfileImage,
    uploadCoverImage,
    
    // Remove functions
    removeProfileImage: handleRemoveProfileImage,
    removeCoverImage: handleRemoveCoverImage,
    
    // Loading states
    isProfileImageLoading: profileImageState.isLoading,
    isCoverImageLoading: coverImageState.isLoading,
    
    // Combined loading state for backward compatibility
    isUploading: profileImageState.isLoading || coverImageState.isLoading,
    
    // Error states
    profileImageError: profileImageState.error,
    coverImageError: coverImageState.error
  };
} 