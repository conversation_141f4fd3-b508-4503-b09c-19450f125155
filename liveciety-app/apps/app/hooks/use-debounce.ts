import * as React from "react";

export function useDebounce<T>(value: T, delay?: number): T;

export function useDebounce<T extends (...args: any[]) => any>(
  func: T,
  delay?: number,
): (...args: Parameters<T>) => void;

export function useDebounce<T>(
  valueOrFunc: T | ((...args: any[]) => any),
  delay: number = 500,
) {
  if (typeof valueOrFunc === "function") {
    const timeoutRef = React.useRef<NodeJS.Timeout | undefined>(undefined);

    return React.useCallback(
      (...args: any[]) => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => {
          (valueOrFunc as Function)(...args);
        }, delay);
      },
      [valueOrFunc, delay],
    );
  }

  const [debouncedValue, setDebouncedValue] = React.useState<T>(valueOrFunc);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(valueOrFunc);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [valueOrFunc, delay]);

  return debouncedValue;
}
