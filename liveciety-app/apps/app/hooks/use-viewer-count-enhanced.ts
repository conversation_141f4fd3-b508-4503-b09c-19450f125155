import { useEffect, useState, useRef } from "react";
import { useAction, useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

interface UseEnhancedViewerCountOptions {
  streamId: Id<"streams">;
  isHost?: boolean;
  enabled?: boolean;
}

interface UseEnhancedViewerCountReturn {
  viewerCount: number;
  isTracking: boolean;
  sessionId: string | null;
  error: string | null;
  peak: number;
  totalUnique: number;
}

/**
 * Enhanced viewer count hook that provides accurate, real-time viewer tracking
 * 
 * Features:
 * - Automatic session management (join/leave tracking)
 * - Real-time viewer count updates
 * - Peak and unique viewer statistics  
 * - Handles connection interruptions gracefully
 * - Excludes host from viewer count
 */
export function useEnhancedViewerCount({
  streamId,
  isHost = false,
  enabled = true,
}: UseEnhancedViewerCountOptions): UseEnhancedViewerCountReturn {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const sessionIdRef = useRef<string | null>(null);
  
  // Mutations for session management
  const incrementViewer = useMutation(api.viewerCount.incrementViewerCount);
  const decrementViewer = useMutation(api.viewerCount.decrementViewerCount);
  const updateActivity = useMutation(api.viewerCount.updateViewerActivity);
  
  // Queries for real-time data
  const viewerStats = useQuery(
    api.viewerCount.getViewerStats, 
    enabled ? { streamId } : "skip"
  );
  
  // Generate unique session ID
  useEffect(() => {
    if (enabled && !sessionIdRef.current) {
      const newSessionId = `session-${Date.now()}-${Math.random().toString(36).substring(7)}`;
      setSessionId(newSessionId);
      sessionIdRef.current = newSessionId;
    }
  }, [enabled]);

  // Join stream when enabled and not host
  useEffect(() => {
    if (!enabled || isHost || !sessionId || isTracking) return;
    
    const joinStream = async () => {
      try {
        setError(null);
        const result = await incrementViewer({ streamId, sessionId });
        
        if (result.success) {
          setIsTracking(true);
          console.log("✅ Joined stream tracking:", result.reason);
        } else {
          console.warn("⚠️ Failed to join stream tracking:", result.reason);
          setError(result.reason);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to join stream";
        setError(errorMessage);
        console.error("❌ Error joining stream:", err);
      }
    };

    joinStream();
  }, [enabled, isHost, streamId, sessionId, isTracking, incrementViewer]);

  // Send periodic heartbeats to keep session alive
  useEffect(() => {
    if (!enabled || isHost || !sessionId || !isTracking) return;

    const heartbeatInterval = setInterval(async () => {
      try {
        await updateActivity({ streamId, sessionId });
      } catch (err) {
        console.warn("Heartbeat failed:", err);
      }
    }, 30000); // Every 30 seconds

    return () => clearInterval(heartbeatInterval);
  }, [enabled, isHost, streamId, sessionId, isTracking, updateActivity]);

  // Leave stream on unmount or when disabled
  useEffect(() => {
    return () => {
      if (sessionIdRef.current && isTracking && !isHost) {
        // Use the ref value to ensure we have the latest sessionId
        decrementViewer({ streamId, sessionId: sessionIdRef.current })
          .then((result) => {
            console.log("✅ Left stream tracking:", result.success ? result.reason : "Unknown");
          })
          .catch((err) => {
            console.warn("⚠️ Error leaving stream:", err);
          });
      }
    };
  }, [streamId, decrementViewer, isHost]); // Don't include isTracking to avoid recreation

  // Handle browser events (page unload, etc.)
  useEffect(() => {
    if (!enabled || isHost || !sessionId) return;

    const handleBeforeUnload = () => {
      if (sessionIdRef.current && isTracking) {
        // Use navigator.sendBeacon for reliable cleanup on page unload
        const data = JSON.stringify({
          streamId,
          sessionId: sessionIdRef.current,
          action: "leave"
        });
        
        // This is a best-effort attempt - the mutation cleanup in useEffect above
        // will handle most cases more reliably
        navigator.sendBeacon?.("/api/viewer-cleanup", data);
      }
    };

    const handleVisibilityChange = async () => {
      if (document.visibilityState === "visible" && sessionIdRef.current && isTracking) {
        // Page became visible again - send heartbeat
        try {
          await updateActivity({ streamId, sessionId: sessionIdRef.current });
        } catch (err) {
          console.warn("Visibility heartbeat failed:", err);
        }
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [enabled, isHost, sessionId, isTracking, streamId, updateActivity]);

  // Reset tracking state when stream changes or host status changes
  useEffect(() => {
    if (isHost && isTracking) {
      setIsTracking(false);
      setError(null);
    }
  }, [isHost, isTracking]);

  return {
    viewerCount: viewerStats?.current || 0,
    isTracking: !isHost && isTracking,
    sessionId,
    error,
    peak: viewerStats?.peak || 0,
    totalUnique: viewerStats?.totalUnique || 0,
  };
} 