"use client";

import { usePathname } from "next/navigation";

interface StepIndicatorProps {
  totalSteps?: number;
}

export function StepIndicator({ totalSteps = 2 }: StepIndicatorProps) {
  const pathname = usePathname();
  
  const getCurrentStep = () => {
    if (pathname.includes("categories")) {
      return 1;
    } else if (pathname.includes("subcategories")) {
      return 2;
    }
    return 1;
  };
  
  const currentStep = getCurrentStep();
  
  return (
    <div className="flex items-center justify-center w-full pt-1 pb-5">
      <div className="relative w-full max-w-48">
        <div className="absolute h-1 w-full bg-muted rounded-full"></div>
        
        <div 
          className="absolute h-1 bg-primary rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${(currentStep / totalSteps) * 100}%` }}
        ></div>
        
        <div className="relative flex justify-between">
          {Array.from({ length: totalSteps }).map((_, index) => {
            const isCompleted = index + 1 <= currentStep;
            const isCurrent = index + 1 === currentStep;
            
            return (
              <div key={index} className="flex flex-col items-center">
                <div 
                  className={`w-3 h-3 rounded-full z-10 transition-colors ${
                    isCompleted 
                      ? "bg-primary" 
                      : "bg-muted"
                  } ${
                    isCurrent 
                      ? "ring-2 ring-primary ring-opacity-50"
                      : ""
                  }`}
                ></div>
                <span className="text-xs text-muted-foreground pt-1">
                  {index === 0 ? "Categories" : "Preferences"}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
} 