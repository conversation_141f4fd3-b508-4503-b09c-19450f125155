"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation, usePreloadedQuery, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Button } from "@workspace/ui/components/button";
import { categories, Category } from "@workspace/lib/constants/categories";
import Image from "next/image";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Check } from "lucide-react";
import { StepIndicator } from "../components/step-indicator";
import { usePreloadedData } from "@/hooks/use-preloaded-data";

export default function CategoriesPage() {
  const router = useRouter();
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);
  const updateUser = useMutation(api.users.update);
  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(
    new Set(user?.preferences?.categories || [])
  );
  const [isLoading, setIsLoading] = useState(false);

  const toggleCategory = (categoryId: string) => {
    setSelectedCategories((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const handleContinue = async () => {
    if (selectedCategories.size === 0) return;

    setIsLoading(true);
    try {
      if (!user) return;
      
      await updateUser({
        id: user._id,
        preferences: {
          ...user.preferences,
          categories: Array.from(selectedCategories),
        },
      });
      router.push("/onboarding/subcategories");
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="space-y-6 w-full max-w-7xl mx-auto px-4">
        <StepIndicator />
        <div>
          <h2 className="text-xl font-semibold">What are you interested in?</h2>
          <p className="text-sm text-muted-foreground">
            Select the categories that interest you
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-6 gap-4 max-h-[70vh] overflow-y-auto pr-2">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="flex flex-col border rounded-lg animate-pulse p-3 h-full">
              <Skeleton className="h-24 w-full rounded-md mb-3" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-3 w-full" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full mx-auto px-4">
      <StepIndicator />
      <div>
        <h2 className="text-xl font-semibold">What are you interested in?</h2>
        <p className="text-sm text-muted-foreground">
          Select the categories that interest you
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-6 gap-4 max-h-[50vh] overflow-y-auto pr-2">
        {categories.map((category: Category) => (
          <div
            key={category.id}
            className={`flex flex-col p-4 border rounded-lg cursor-pointer transition-all h-full ${
              selectedCategories.has(category.id)
                ? "border-primary bg-primary/5 ring-1 ring-primary"
                : "border-border hover:border-muted-foreground/30"
            }`}
            onClick={() => toggleCategory(category.id)}
          >
            <div className="w-full aspect-square relative overflow-hidden rounded-md mb-3">
              <div className="absolute inset-0 bg-gradient-to-br from-slate-700 to-slate-900 animate-pulse" />
              {category.image && (
                <Image
                  src={category.image.default || category.image}
                  alt={category.title}
                  className="h-full w-full object-cover"
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 33vw, 16vw"
                />
              )}
              {selectedCategories.has(category.id) && (
                <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center">
                  <Check className="h-4 w-4" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-center">{category.title}</h3>
              <p className="text-xs text-muted-foreground text-center mt-1">{category.description}</p>
            </div>
          </div>
        ))}
      </div>

      <Button
        className="w-full max-w-md mx-auto"
        onClick={handleContinue}
        disabled={selectedCategories.size === 0 || isLoading}
      >
        {isLoading ? "Saving..." : "Continue"}
      </Button>
      <p className="text-xs text-center text-muted-foreground">
        You can always change these preferences later in your settings
      </p>
    </div>
  );
} 