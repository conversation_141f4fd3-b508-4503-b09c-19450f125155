"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useMutation, useQuery, useConvex } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import Image from "next/image";
import { usePara<PERSON>, notFound, useRouter } from "next/navigation";
import { Button } from "@workspace/ui/components/button";
import { 
  StarIcon,
  Share2Icon,
  ShieldAlertIcon,
  BanIcon
} from "lucide-react";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { getAvatarImageUrl } from "@/lib/utils";
import { Badge } from "@workspace/ui/components/badge";
import { formatDistanceToNow, format } from "date-fns";
import { UserAvatar } from "@/components/user-avatar";
import { FollowerSheet } from "@/components/follower-sheet";
import { ShareModal } from "@/components/share-modal";
import { IconDotsVertical } from "@tabler/icons-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { ReportModal } from "@/components/report-modal";
import { toast } from "sonner";
import { FriendStatus } from "@/components/friend-status";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { usePreloadedQuery } from "convex/react";
import { User } from "@workspace/backend/convex/lib/types";
import { useToggleFollow } from "@/hooks/use-toggle-follow";
import ProductCard from "./_components/product";

const formatScheduledTime = (timestamp: number) => {
  return format(new Date(timestamp), "MMM d, yyyy 'at' h:mm a");
};

const formatReviewDate = (timestamp: number) => {
  return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
};

const getCategoryName = (categoryId: string) => {
  return categoryId || "General";
};

const getSubcategoryName = (categoryId: string, subcategoryId: string) => {
  return subcategoryId || "General";
};

export default function UserProfilePage() {
  const router = useRouter();
  const { username } = useParams<{ username: string }>();
  const convex = useConvex();
  
  const { preloadedUser } = usePreloadedData();
  const currentUser = usePreloadedQuery(preloadedUser);
  const user = useQuery(api.users.getUserByUsername, { username });
  
  const followUser = useMutation(api.users.followUser);
  const unfollowUser = useMutation(api.users.unfollowUser);
  const blockUser = useMutation(api.users.blockUser);
  const unblockUser = useMutation(api.users.unblockUser);
  
  const isOwnProfile = useMemo(() => 
    currentUser && user && currentUser._id === user._id, 
    [currentUser, user]
  );
  
  const [isBlockedByUser, setIsBlockedByUser] = useState(false);
  const [followersOpen, setFollowersOpen] = useState(false);
  const [followingOpen, setFollowingOpen] = useState(false);
  const [shareOpen, setShareOpen] = useState(false);
  const [reportModalOpen, setReportModalOpen] = useState(false);
  
  const {
    isFollowing,
    toggleFollow,
    loading: followLoading
  } = useToggleFollow({
    userId: user && !isOwnProfile ? user._id : undefined,
    username: user && !isOwnProfile ? user.username : undefined,
    skip: !user || !!isOwnProfile
  });
  
  const hasBlockedCheck = useQuery(
    api.users.hasBlocked,
    user?._id ? { targetUserId: user._id } : "skip"
  );
  
  useEffect(() => {
    if (hasBlockedCheck !== undefined) {
      setIsBlockedByUser(hasBlockedCheck);
    }
  }, [hasBlockedCheck]);
  
  const followCounts = useQuery(
    api.users.getFollowCounts, 
    user?._id ? { userId: user._id } : "skip"
  );
  
  const userStreams = useQuery(
    api.streams.listByUser, 
    user?._id ? {} : "skip"
  );
  
  const streams = useMemo(() => {
    if (!userStreams) return [];
    return Array.isArray(userStreams) ? userStreams : userStreams.page || [];
  }, [userStreams]);
  
  const userProducts = useQuery(
    api.users.getUserProducts, 
    user?._id ? { userId: user._id, status: "active" } : "skip"
  );
  
  const userReviews = useQuery(
    api.users.getUserReviews, 
    user?._id ? { reviewedUserId: user._id, limit: 10 } : "skip"
  );

  const [followersCursor, setFollowersCursor] = useState<string | null>(null);
  const [followingCursor, setFollowingCursor] = useState<string | null>(null);
  
  const userFollowers = useQuery(
    api.users.getUserFollowers,
    user?._id ? { 
      userId: user._id,
      paginationOpts: { 
        numItems: 10, 
        cursor: followersCursor 
      } 
    } : "skip"
  );
  
  const userFollowing = useQuery(
    api.users.getFollowing,
    user?._id ? { 
      userId: user._id,
      paginationOpts: { 
        numItems: 10, 
        cursor: followingCursor 
      } 
    } : "skip"
  );

  const [accumulatedFollowers, setAccumulatedFollowers] = useState<unknown[]>([]);
  const [accumulatedFollowing, setAccumulatedFollowing] = useState<unknown[]>([]);

  useEffect(() => {
    if (userFollowers && userFollowers.page) {
      if (!followersCursor) {
        setAccumulatedFollowers(userFollowers.page);
      } else {
        setAccumulatedFollowers(prev => [...prev, ...userFollowers.page]);
      }
    }
  }, [userFollowers, followersCursor]);

  useEffect(() => {
    if (userFollowing && userFollowing.users) {
      if (!followingCursor) {
        setAccumulatedFollowing(userFollowing.users);
      } else {
        setAccumulatedFollowing(prev => [...prev, ...userFollowing.users]);
      }
    }
  }, [userFollowing, followingCursor]);

  const loadMoreFollowers = async () => {
    if (userFollowers && !userFollowers.isDone && userFollowers.continueCursor) {
      setFollowersCursor(userFollowers.continueCursor);
    }
  };

  const loadMoreFollowing = async () => {
    if (userFollowing && !userFollowing.isDone && userFollowing.cursor) {
      setFollowingCursor(userFollowing.cursor);
    }
  };

  const handleToggleBlock = async () => {
    if (!user) return;
    
    try {
      if (isBlockedByUser) {
        await unblockUser({ targetUserId: user._id });
        toast.success(`Unblocked ${user.username}`);
      } else {
        await blockUser({ targetUserId: user._id });
        toast.success(`Blocked ${user.username}`);
      }
      setIsBlockedByUser(!isBlockedByUser);
    } catch (error) {
      toast.error("Failed to update block status");
      console.error(error);
    }
  };
  
  const handleMessage = async () => {
    if (!user?._id) return;
    
    try {
      const existingChats = await convex.query(api.chat.listChats, {});
      
      const existingChat = existingChats.find(
        (chat: { isGroup: boolean; participants: { _id: string }[] }) => 
          !chat.isGroup && 
          chat.participants.length === 2 && 
          chat.participants.some((p: { _id: string }) => p._id === user._id)
      );
      
      if (existingChat) {
        document.dispatchEvent(new CustomEvent('open-activity', { 
          detail: { 
            tab: 'messages',
            chatId: existingChat._id,
            fromProfile: true
          } 
        }));
      } else {
        const chatId = await convex.mutation(api.chat.createChat, {
          participantIds: [user._id],
          isGroup: false
        });
        
        document.dispatchEvent(new CustomEvent('open-activity', { 
          detail: { 
            tab: 'messages',
            chatId,
            fromProfile: true
          } 
        }));
      }
    } catch (error) {
      console.error("Error handling chat:", error);
      toast.error("Failed to open chat");
    }
  };

  const handleOpenMessages = () => {
    document.dispatchEvent(new CustomEvent('open-activity', { 
      detail: { tab: 'messages' } 
    }));
  };

  const handleEditProfile = () => {
    router.push('/settings/account');
  };
  
  const getProfileUrl = () => {
    if (typeof window !== 'undefined') {
      return window.location.href;
    }
    return '';
  };
  
  useEffect(() => {
    if (user && user.username && user.username !== username) {
      router.replace(`/user/${user.username}`);
    }
  }, [user, username, router]);
  
  if (user === undefined || currentUser === undefined) {
    return (
      <div className="p-4">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-40 bg-gray-200 dark:bg-gray-800 rounded-lg mb-4"></div>
            <div className="flex items-start gap-4 mb-6">
              <div className="w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-800"></div>
              <div className="flex-1">
                <div className="h-8 bg-gray-200 dark:bg-gray-800 rounded w-48 mb-2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-32 mb-4"></div>
                <div className="h-20 bg-gray-200 dark:bg-gray-800 rounded mb-4"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  if (user === null) {
    return notFound();
  }

  const isSeller = !!user.sellerProfile;

  return (
    <div>
      <div className="">
        <div className="mx-auto">
          <div className="w-full h-72 relative bg-gradient-to-r from-blue-800 to-purple-800">
            {user?.coverImageUrl && (
              <Image 
                src={getAvatarImageUrl(user.coverImageUrl) as string} 
                alt={`${user?.username}'s cover image`}
                fill
                className="object-cover"
                priority
              />
            )}
            <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
          </div>

          <div className="container mx-auto flex flex-col md:flex-row items-start md:items-end p-4 relative -top-6">
            <div className="relative -top-8 md:mr-6 z-10">
              <div className="border-4 border-black rounded-full shadow-xl">
                <UserAvatar user={user as unknown as User} height={110} width={110} />
              </div>
            </div>
            
            <div className="flex-1 mt-2 md:mt-0">
              <div className="flex items-center gap-2">
                <h1 className="text-lg font-semibold">{user?.username}</h1>
                {user?.sellerProfile?.verified && (
                  <div className="text-blue-500">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                      <path fillRule="evenodd" d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
                {user && !isOwnProfile && (
                  <FriendStatus userId={user._id} />
                )}
              </div>
              {user?.name && <p className="text-gray-500 text-sm">{user.name}</p>}
              
              <div className="flex flex-wrap mt-2 gap-6">
                <div className="group flex items-center cursor-pointer hover:text-white transition-colors" onClick={() => setFollowingOpen(true)}>
                  <p className="font-medium">{followCounts?.following || 0}</p>
                  <p className="text-gray-500 text-sm ml-1 group-hover:text-white">following</p>
                </div>
                <div className="group flex items-center cursor-pointer hover:text-white transition-colors" onClick={() => setFollowersOpen(true)}>
                  <p className="font-medium">{followCounts?.followers || 0}</p>
                  <p className="text-gray-500 text-sm ml-1 group-hover:text-white">followers</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2 mt-4 md:mt-0">
              <Button 
                variant="ghost" 
                size="icon" 
                className="rounded-full"
                onClick={() => setShareOpen(true)}
              >
                <Share2Icon className="h-5 w-5" />
              </Button>
              
              {!isOwnProfile && (
                <Button 
                  variant="outline" 
                  className="rounded-full text-sm font-medium"
                  onClick={handleMessage}
                >
                  Message
                </Button>
              )}

              {isOwnProfile && (
                <Button 
                  variant="outline" 
                  className="rounded-full text-sm font-medium"
                  onClick={handleOpenMessages}
                >
                  Messages
                </Button>
              )}
              
              <Button 
                className={`rounded-full px-5 py-1 h-9 ${isFollowing || isOwnProfile ? 'bg-gray-100 text-black hover:bg-gray-200' : 'bg-blue-400 text-white hover:bg-blue-500'}`}
                onClick={isOwnProfile ? handleEditProfile : toggleFollow}
                disabled={followLoading}
              >
                {isOwnProfile ? 'Edit Profile' : (isFollowing ? 'Following' : 'Follow')}
              </Button>
              
              {!isOwnProfile && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="rounded-full">
                      <IconDotsVertical className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="min-w-[160px]">
                    <DropdownMenuItem variant="destructive" onClick={handleToggleBlock}>
                      <BanIcon className="h-4 w-4 mr-2" />
                      {isBlockedByUser ? "Unblock" : "Block"}
                    </DropdownMenuItem>
                    <DropdownMenuItem variant="destructive" onClick={() => setReportModalOpen(true)}>
                      <ShieldAlertIcon className="h-4 w-4 mr-2" />
                      Report user
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
          
          {isSeller && (
            <Tabs defaultValue="shop" className="p-4">
              <TabsList className="w-fit flex bg-transparent p-0 mb-6">
                  <>
                    <TabsTrigger value="shop" className="flex-1 px-3 py-2 bg-transparent rounded-md data-[state=active]:bg-muted data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-black">
                      Shop
                    </TabsTrigger>
                    <TabsTrigger value="streams" className="flex-1 px-3 py-2 bg-transparent rounded-md data-[state=active]:bg-muted data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-black">
                      Streams
                    </TabsTrigger>
                    <TabsTrigger value="reviews" className="flex-1 px-3 py-2 bg-transparent rounded-md data-[state=active]:bg-muted data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-black">
                      Reviews
                    </TabsTrigger>
                  </>
              </TabsList>
              
              <>
                <TabsContent value="shop" className="space-y-6">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {userProducts && userProducts.length > 0 ? (
                      userProducts.map((product) => (
                          <ProductCard key={product._id} product={product} user={user} />
                      ))
                    ) : (
                      <div className="col-span-4 text-center py-10">
                        <p className="text-gray-500">
                          {isOwnProfile 
                            ? "You don't have any products listed yet." 
                            : "No products available"}
                        </p>
                        {isOwnProfile && (
                          <Button 
                            variant="outline" 
                            className="mt-4"
                            onClick={() => router.push('/products/create')}
                          >
                            Add a Product
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </TabsContent>
                
                <TabsContent value="streams" className="space-y-6">
                  <div>
                    <h2 className="text-xl font-bold mb-4">
                      {isOwnProfile ? "Your Streams" : "Upcoming Streams"}
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {streams && streams.length > 0 ? (
                        streams.map((stream: any) => (
                          <div key={stream._id} className="rounded-lg border border-gray-200 overflow-hidden">
                            <div className="aspect-video bg-gray-100 relative">
                              {stream.thumbnailUrl && (
                                <Image 
                                  src={stream.thumbnailUrl as string}
                                  alt={stream.title || "Stream"} 
                                  fill 
                                  className="object-cover"
                                />
                              )}
                              {stream.isLive && (
                                <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded-sm flex items-center">
                                  <span className="mr-1">Live</span>
                                  <span className="mx-1">•</span>
                                  <span>{stream.viewerCount || 0}</span>
                                </div>
                              )}
                              {!stream.isLive && stream.scheduledStartTime && (
                                <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-0.5 rounded-sm">
                                  {formatScheduledTime(stream.scheduledStartTime)}
                                </div>
                              )}
                              <button className="absolute top-2 right-2 p-1 rounded-full">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                                </svg>
                              </button>
                            </div>
                            <div className="p-3">
                              <p className="font-medium line-clamp-1">{stream.title}</p>
                              <p className="text-sm text-gray-500 mt-1">
                                {stream.category && getCategoryName(stream.category)}
                                {stream.subcategory && stream.category && ` • ${getSubcategoryName(stream.category, stream.subcategory)}`}
                              </p>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="col-span-3 text-center py-10">
                          <p className="text-gray-500">
                            {isOwnProfile 
                              ? "You haven't scheduled any streams yet." 
                              : "No streams available"}
                          </p>
                          {isOwnProfile && (
                            <Button 
                              variant="outline" 
                              className="mt-4"
                              onClick={() => router.push('/streams/create')}
                            >
                              Schedule a Stream
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="reviews" className="space-y-4">
                  <div className="flex items-center mb-4">
                    <h2 className="text-xl font-bold">Reviews</h2>
                    {/* <div className="ml-4 flex items-center">
                      <StarIcon className="w-5 h-5 text-yellow-500" />
                      <span className="ml-1 font-bold">{user.sellerProfile?.rating?.toFixed(1) || "4.9"}</span>
                      <span className="text-gray-500 mx-1">•</span>
                      <span className="text-gray-500">{user.sellerProfile?.totalSales || 0} reviews</span>
                    </div> */}
                  </div>
                  <div className="space-y-4">
                    {userReviews && userReviews.length > 0 ? (
                      userReviews.map((review) => {
                        const enrichedReview = review as any;
                        return (
                        <div key={enrichedReview._id} className="rounded-lg border border-gray-200 p-4">
                          <div className="flex items-center gap-3 mb-3">
                            <UserAvatar user={enrichedReview.reviewer} />
                            <div>
                              <p className="font-medium">{enrichedReview.reviewer?.username || "User"}</p>
                              <div className="flex items-center text-sm text-gray-500">
                                <div className="flex">
                                  {Array(5).fill(null).map((_, j) => (
                                    <StarIcon key={j} className={`w-3 h-3 ${j < enrichedReview.overallRating ? "text-yellow-500" : "text-gray-300"}`} />
                                  ))}
                                </div>
                                <span className="mx-2">•</span>
                                <span>{formatReviewDate(enrichedReview._creationTime)}</span>
                              </div>
                            </div>
                          </div>
                          <p className="text-gray-700">{enrichedReview.reviewText}</p>
                          
                          {enrichedReview.replyText && (
                            <div className="mt-3 pl-4 border-l-2 border-gray-200">
                              <p className="text-sm text-gray-500">
                                <span className="font-medium">{user.username} replied:</span> {enrichedReview.replyText}
                              </p>
                            </div>
                          )}
                          
                          {isOwnProfile && !enrichedReview.replyText && (
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="mt-2 text-xs"
                              onClick={() => {/* Add reply functionality */}}
                            >
                              Reply to review
                            </Button>
                          )}
                        </div>
                        );
                      })
                    ) : (
                      <div className="text-center py-10">
                        <p className="text-gray-500">No reviews available</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </>
            </Tabs>
          )}
          
          {/* Followers Sheet */}
          <FollowerSheet
            open={followersOpen}
            onOpenChange={setFollowersOpen}
            title="Followers"
            description={`People who follow ${user?.username || ""}`}
            initialUsers={accumulatedFollowers}
            currentUser={currentUser}
            emptyMessage="No followers yet"
            context="followers"
            hasMore={!!userFollowers && !userFollowers.isDone}
            loadMore={loadMoreFollowers}
          />
          
          {/* Following Sheet */}
          <FollowerSheet
            open={followingOpen}
            onOpenChange={setFollowingOpen}
            title="Following"
            description={`People ${user?.username || ""} follows`}
            initialUsers={accumulatedFollowing}
            currentUser={currentUser}
            emptyMessage="Not following anyone yet"
            context="following"
            hasMore={!!userFollowing && !userFollowing.isDone}
            loadMore={loadMoreFollowing}
          />

          {/* Share Modal */}
          {user && (
            <ShareModal
              open={shareOpen}
              onOpenChange={setShareOpen}
              username={user.username || ""}
              profileUrl={getProfileUrl()}
            />
          )}
          
          {/* Report Modal */}
          {user && (
            <ReportModal
              isOpen={reportModalOpen}
              onClose={() => setReportModalOpen(false)}
              contentType="user"
              contentId={user._id}
              reportedUserId={user._id}
              username={user.username}
            />
          )}
        </div>
      </div>
    </div>
  );
}