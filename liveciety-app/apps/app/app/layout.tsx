import type { Metada<PERSON>, Viewport } from "next";
import { ConvexAuthNextjsServerProvider } from "@convex-dev/auth/nextjs/server";
import { ConvexClientProvider } from "@/providers/convex-provider";
import "@workspace/ui/styles/globals.css";
import { siteConfig } from "../lib/site-config";
import { ActiveThemeProvider } from "@/components/active-theme";
import { cn } from "@workspace/ui/lib/utils";
import { cookies } from "next/headers";
import { fontVariables } from "@/lib/fonts";
import { ThemeProvider } from "next-themes";
import { Toaster } from "sonner";
import { TooltipProvider } from "@workspace/ui/components/tooltip";
import AppWrapper from "@/context/app-wrapper";
import LayoutClient from "@/components/layout-client";

const META_THEME_COLORS = {
  light: "#ffffff",
  dark: "#09090b",
};

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`,
  },
  applicationName: siteConfig.name,
  metadataBase: new URL("https://liveciety.com"),
  description: siteConfig.description,
  keywords: [
    "Next.js",
    "React",
    "Tailwind CSS",
    "Server Components",
    "Radix UI",
  ],
  authors: [
    {
      name: "Bobby Alv",
      url: "https://devwithbobby.com",
    },
  ],
  creator: "Bobby Alv",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://liveciety.com",
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: "/opengraph-image.png",
        width: 1200,
        height: 630,
        alt: siteConfig.name,
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: siteConfig.name,
    description: siteConfig.description,
    images: ["/opengraph-image.png"],
    creator: "@liveciety",
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: `/site.webmanifest`,
};

export const viewport: Viewport = {
  themeColor: META_THEME_COLORS.light,
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}): Promise<React.ReactElement> {
  const cookieStore = await cookies();
  const activeThemeValue = cookieStore.get("active_theme")?.value;
  const isScaled = activeThemeValue?.endsWith("-scaled");

  return (
    <ConvexAuthNextjsServerProvider>
      <ActiveThemeProvider initialTheme={activeThemeValue}>
        <html lang="en" suppressHydrationWarning className="dark">
          <head>
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  try {
                    if (localStorage.theme === 'dark' || ((!('theme' in localStorage) || localStorage.theme === 'system') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                      document.querySelector('meta[name="theme-color"]').setAttribute('content', '${META_THEME_COLORS.dark}')
                    }
                  } catch (_) {}
                `,
              }}
            />
          </head>
          <body
            className={cn(
              "bg-background overscroll-none font-sans antialiased",
              activeThemeValue ? `theme-${activeThemeValue}` : "",
              isScaled ? "theme-scaled" : "",
              fontVariables,
            )}
          >
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
              enableColorScheme
            >
              <ConvexClientProvider>
                <TooltipProvider delayDuration={0}>
                  <AppWrapper>
                    <LayoutClient>{children}</LayoutClient>
                  </AppWrapper>
                </TooltipProvider>
                <Toaster />
              </ConvexClientProvider>
            </ThemeProvider>
          </body>
        </html>
      </ActiveThemeProvider>
    </ConvexAuthNextjsServerProvider>
  );
}
