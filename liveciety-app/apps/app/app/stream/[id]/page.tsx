"use client";

import { But<PERSON> } from "@workspace/ui/components/button"
import { useRouter } from "next/navigation"
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"

export default function StreamPage() {
  const router = useRouter()
  const params = useParams()
  const streamId = params?.id as string
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Check if we're already in dialog mode by looking at the current path
  useEffect(() => {
    // If we're on this page, the dialog should be open via StreamProvider
    setIsDialogOpen(true)
  }, [])

  const goBack = () => {
    router.back()
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <div className="text-center space-y-4">
        <h1 className="text-2xl font-bold">Stream Viewer</h1>
        <p className="text-muted-foreground">
          {isDialogOpen 
            ? "The stream dialog should be open. If you don't see it, click the button below."
            : "Click the button below to open the stream in a dialog"
          }
        </p>
        <div className="space-x-2">
          <Button onClick={goBack} variant="outline">
            Go Back
          </Button>
          <Button onClick={() => window.location.reload()} size="lg">
            Refresh Page
          </Button>
        </div>
      </div>
    </div>
  )
}