"use client"

import { useQuery } from "convex/react"
import { api } from "@workspace/backend/convex/_generated/api"
import { Id } from "@workspace/backend/convex/_generated/dataModel"
import Image from "next/image"
import { getAvatarImageUrl } from "@/lib/utils"
import { cn } from "@workspace/ui/lib/utils"
import { useParams } from "next/navigation"

export function PinnedProducts() {
  const params = useParams()
  const streamId = params.id as Id<"streams">
  const stream = useQuery(api.streams.getStreamById, { streamId })
  const products = useQuery(api.sellers.getProductsForStream, { streamId })

  const pinnedProducts = products?.filter(product => 
    stream?.pinnedProducts?.includes(product._id as Id<"products">)
  ) || []

  if (pinnedProducts.length === 0) return null

  return (
    <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
      <div className="flex gap-4 overflow-x-auto pb-2">
        {pinnedProducts.map((product) => (
          <div
            key={product._id}
            className="flex-none group relative"
            onClick={() => window.open(`/products/${product._id}`, '_blank')}
          >
            <div className="w-48 h-48 relative rounded-lg overflow-hidden cursor-pointer">
              {product.images?.[0] && (
                <Image
                  src={getAvatarImageUrl(product.images[0]) ?? ""}
                  alt={product.name}
                  fill
                  className="object-cover transition-transform group-hover:scale-110"
                />
              )}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div className="absolute bottom-0 left-0 right-0 p-2 text-white">
                <h3 className="font-semibold truncate">{product.name}</h3>
                <p className="text-sm opacity-90">
                  {product.price
                    ? new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: product.currency || 'USD'
                      }).format(product.price / 100)
                    : 'Price not set'}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
