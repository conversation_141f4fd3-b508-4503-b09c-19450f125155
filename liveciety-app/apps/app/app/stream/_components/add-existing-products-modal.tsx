"use client";

import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { toast } from "sonner";
import Image from "next/image";
import { getAvatarImageUrl } from "@/lib/utils";

interface AddExistingProductsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  streamId: Id<"streams">;
}

export function AddExistingProductsModal({
  open,
  onOpenChange,
  streamId,
}: AddExistingProductsModalProps) {
  const [selectedProducts, setSelectedProducts] = useState<Id<"products">[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const products = useQuery(api.sellers.listProducts) || [];
  const addProductsToStream = useMutation(api.sellers.addProductsToStream);

  const handleSubmit = async () => {
    if (selectedProducts.length === 0) {
      toast.error("Please select at least one product");
      return;
    }

    setIsLoading(true);
    try {
      await addProductsToStream({
        streamId,
        productIds: selectedProducts,
      });
      toast.success("Products added to stream successfully");
      onOpenChange(false);
      setSelectedProducts([]);
    } catch (error: any) {
      toast.error(error.message || "Failed to add products to stream");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleProduct = (productId: Id<"products">) => {
    setSelectedProducts((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Existing Products</DialogTitle>
          <DialogDescription>
            Select products from your inventory to add to this stream.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {products.map((product) => (
              <div
                key={product._id}
                className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-accent/5"
              >
                <Checkbox
                  checked={selectedProducts.includes(product._id)}
                  onCheckedChange={() => toggleProduct(product._id)}
                />
                <div className="h-12 w-12 rounded-md overflow-hidden bg-accent/20 flex items-center justify-center">
                  {product.images?.[0] ? (
                    <Image
                      src={getAvatarImageUrl(product.images[0]) || ""}
                      alt={product.name}
                      width={48}
                      height={48}
                      className="object-cover"
                    />
                  ) : (
                    <div className="text-2xl">📦</div>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">{product.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    ${product.price?.toFixed(2) || "0.00"}
                  </p>
                </div>
              </div>
            ))}
            {products.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No products found in your inventory
              </div>
            )}
          </div>
        </ScrollArea>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? "Adding..." : "Add Selected Products"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 