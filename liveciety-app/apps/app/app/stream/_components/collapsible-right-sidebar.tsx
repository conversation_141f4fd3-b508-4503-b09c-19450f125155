"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { Input } from "@workspace/ui/components/input"
import { ScrollArea } from "@workspace/ui/components/scroll-area"
import { useDualSidebar } from "./dual-sidebar-provider"
import { cn } from "@workspace/ui/lib/utils"
import { FormEvent, useState, useEffect } from "react"
import { Id } from "@workspace/backend/convex/_generated/dataModel"
import { StreamChat } from "../../../components/stream-chat"
import { ModeratorSelect } from "../../../components/moderator-select"

type CollapsibleRightSidebarProps = {
  showModerationPanel: boolean
  setShowModerationPanel: (show: boolean) => void
  moderationData: any
  handleAddModerator: (e: FormEvent) => Promise<void>
  handleRemoveModerator: (userIdToRemove: Id<"users">) => Promise<void>
  handleUnblockUser: (userIdToUnblock: Id<"users">) => Promise<void>
  newModeratorId: string
  setNewModeratorId: (id: string) => void
  showObsInfo: boolean
  setShowObsInfo: (show: boolean) => void
  ingestUrl: string | null
  streamKey: string | null
  copyToClipboard: (text: string | null) => void
  setError: (error: string | undefined) => void
  setAssumeObsMode: (mode: boolean) => void
  isHost: boolean
  isMod: boolean
  streamDetails: any
}

export function CollapsibleRightSidebar({ 
  showModerationPanel, 
  setShowModerationPanel, 
  moderationData, 
  handleAddModerator, 
  handleRemoveModerator,
  handleUnblockUser,
  newModeratorId,
  setNewModeratorId,
  showObsInfo,
  setShowObsInfo,
  ingestUrl,
  streamKey,
  copyToClipboard,
  setError,
  setAssumeObsMode,
  isHost,
  isMod,
  streamDetails
}: CollapsibleRightSidebarProps) {
  const { rightSidebarOpen, toggleRightSidebar } = useDualSidebar()
  const [activeTab, setActiveTab] = useState<'chat' | 'obs' | 'moderation'>('chat')

  useEffect(() => {
    if (showObsInfo) {
      setActiveTab('obs')
    }
  }, [showObsInfo])

  useEffect(() => {
    if (showModerationPanel) {
      setActiveTab('moderation')
    }
  }, [showModerationPanel])

  useEffect(() => {
    if (!showObsInfo && !showModerationPanel && activeTab !== 'chat') {
      setActiveTab('chat')
    }
  }, [showObsInfo, showModerationPanel, activeTab])

  const handleObsTabClick = () => {
    setActiveTab('obs')
    if (!showObsInfo) {
      setShowObsInfo(true)
    }
  }

  const handleModerationTabClick = () => {
    setActiveTab('moderation')
    if (!showModerationPanel) {
      setShowModerationPanel(true)
    }
  }

  const moderatorIds = Array.isArray(moderationData?.moderators)
    ? moderationData.moderators.map((mod: any) => mod._id).filter((id: string | undefined): id is string => typeof id === 'string')
    : [];

  return (
    <div
      className={cn(
        "relative h-full bg-sidebar border-l transition-all duration-300 ease-in-out flex flex-col",
        rightSidebarOpen ? "w-80" : "w-16",
      )}
    >
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleRightSidebar}
        className="absolute -left-3 top-6 z-10 h-6 w-6 rounded-full border dark:bg-background bg-white shadow-md hover:bg-accent"
      >
        <ChevronRight className={cn("h-4 w-4 transition-transform dark:text-white text-black", !rightSidebarOpen && "rotate-180")} />
      </Button>

      {rightSidebarOpen ? (
        <>
          {/* Header with Tabs */}
          <div className="border-b">
            <div className="flex items-center justify-between p-4 pb-2">
              <h3 className="font-semibold dark:text-white text-black">
                {activeTab === 'chat' && 'Live Chat'}
                {activeTab === 'obs' && 'OBS Setup'}
                {activeTab === 'moderation' && 'Moderation'}
              </h3>
            </div>
            
            {/* Tab Navigation */}
            <div className="flex border-b">
              <button
                onClick={() => setActiveTab('chat')}
                className={cn(
                  "flex-1 px-4 py-2 text-sm font-medium border-b-1 transition-colors",
                  activeTab === 'chat' 
                    ? "border-primary text-primary" 
                    : "border-transparent text-muted-foreground hover:text-foreground",
                  !isHost && "hidden"
                )}
              >
                Chat
              </button>
              {isHost && (
                <button
                  onClick={handleObsTabClick}
                  className={cn(
                    "flex-1 px-4 py-2 text-sm font-medium border-b-1 transition-colors",
                    activeTab === 'obs' 
                      ? "border-primary text-primary" 
                      : "border-transparent text-muted-foreground hover:text-foreground"
                  )}
                >
                  OBS
                </button>
              )}
              {isHost && (
                <button
                  onClick={handleModerationTabClick}
                  className={cn(
                    "flex-1 px-4 py-2 text-sm font-medium border-b-1 transition-colors",
                    activeTab === 'moderation' 
                      ? "border-primary text-primary" 
                      : "border-transparent text-muted-foreground hover:text-foreground"
                  )}
                >
                  Moderation
                </button>
              )}
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 flex flex-col min-h-0">
            {activeTab === 'chat' && (
              <div className="flex-1 flex flex-col min-h-0">
                <StreamChat streamId={streamDetails?._id} isMod={isMod} isHost={isHost} streamDetails={streamDetails} />
              </div>
            )}

            {activeTab === 'obs' && isHost && (
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  
                  {streamDetails?.status === "live" ? (
                    <>
                      {ingestUrl && streamKey ? (
                        <>
                          <p className="text-sm text-muted-foreground">Use these details in OBS:</p>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-sm font-medium mb-1">Server (Ingest URL):</label>
                              <div className="flex items-center gap-2">
                                <Input
                                  type="text"
                                  readOnly
                                  value={ingestUrl}
                                  className="flex-1 text-xs"
                                />
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => copyToClipboard(ingestUrl)}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-medium mb-1">Stream Key:</label>
                              <div className="flex items-center gap-2">
                                <Input
                                  type="text"
                                  readOnly
                                  value={streamKey}
                                  className="flex-1 text-xs"
                                />
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => copyToClipboard(streamKey)}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </>
                      ) : (
                        <p className="text-sm text-muted-foreground">Generating OBS info...</p>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-sm text-muted-foreground mb-4">
                        Stream must be live to generate OBS credentials
                      </p>
                      {streamDetails?.status === "live" && (
                        <Button onClick={() => setShowObsInfo(true)}>
                          Generate OBS Info
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </ScrollArea>
            )}

            {activeTab === 'moderation' && isHost && (
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  
                  {showModerationPanel && moderationData ? (
                    <>                      
                      <div>
                        <h5 className="text-sm font-medium mb-2">Current Moderators:</h5>
                          <ModeratorSelect
                            selected={moderatorIds}
                            onChange={async (ids: Id<"users">[]) => {
                              const prevIds = moderatorIds;
                              // Detect removed
                              const removed = prevIds.filter((id: Id<"users">) => !ids.includes(id));
                              if (removed.length > 0 && typeof removed[0] === 'string') {
                                await handleRemoveModerator(removed[0] as Id<'users'>);
                                return;
                              }
                              // Detect added
                              const added = ids.filter((id: Id<"users">) => !prevIds.includes(id));
                              if (added.length > 0) {
                                setNewModeratorId(added[0] as Id<"users">);
                                await handleAddModerator({ preventDefault: () => {} } as any);
                              }
                            }}
                            placeholder="Select moderators"
                          />
                      </div>
                      
                      <div>
                        <h5 className="text-sm font-medium mb-2">Blocked Users:</h5>
                        {moderationData.blockedUsers && moderationData.blockedUsers.length > 0 ? (
                          <div className="space-y-1">
                            {moderationData.blockedUsers.map(
                              (user: any) =>
                                user && (
                                  <div
                                    key={user._id}
                                    className="flex justify-between items-center p-2 bg-muted rounded text-sm"
                                  >
                                    <span className="truncate" title={user.username} style={{ color: user.color }}>
                                      {user.username}
                                    </span>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleUnblockUser(user._id)}
                                      className="h-6 w-6 p-0 text-green-500 hover:text-green-700"
                                    >
                                      <ShieldCheck className="h-3 w-3" />
                                    </Button>
                                  </div>
                                )
                            )}
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground">No users blocked from chat.</p>
                        )}
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-sm text-muted-foreground mb-4">
                        Moderation panel is not active
                      </p>
                      <Button onClick={() => setShowModerationPanel(true)}>
                        Activate Moderation
                      </Button>
                    </div>
                  )}
                </div>
              </ScrollArea>
            )}
          </div>
        </>
      ) : (
        /* Collapsed State */
        <div className="flex flex-col items-center p-2 space-y-2">
          <div className="mt-8">
            <Users className="h-6 w-6 text-muted-foreground" />
          </div>
          <div className="text-xs text-muted-foreground writing-mode-vertical-rl text-orientation-mixed">
            {activeTab === 'chat' && 'Chat'}
            {activeTab === 'obs' && 'OBS'}
            {activeTab === 'moderation' && 'Mod'}
          </div>
        </div>
      )}
    </div>
  )
}
