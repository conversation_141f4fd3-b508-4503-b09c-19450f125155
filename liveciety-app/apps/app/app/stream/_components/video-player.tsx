"use client"

import { Play, Pause, Volume2, Maximize, } from "lucide-react"
import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { Badge } from "@workspace/ui/components/badge"
import { IconMinimize, IconVolume3, IconClock, IconPlayerPlay } from "@tabler/icons-react"
import { useState } from "react"
import { useViewerCount, formatViewerCount } from "@/hooks/use-viewer-count"
import { UserAvatar } from "@/components/user-avatar"
import { isValidStorageId } from "@workspace/backend/convex/helpers/utils"
import { useQuery } from "convex/react"
import { api } from "@workspace/backend/convex/_generated/api"
import Image from "next/image"

type VideoPlayerProps = {
  videoContainerRef: React.RefObject<HTMLDivElement | null>
  videoRef: React.RefObject<HTMLVideoElement | null>
  audioRef: React.RefObject<HTMLAudioElement | null>
  isHost: boolean
  isConnected: boolean
  isLive: boolean
  isMuted: boolean
  volume: number
  toggleMute: () => void
  streamDetails: any
  handleVolumeChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  toggleFullscreen: () => void
  isFullscreen: boolean
}

export function VideoPlayer({ 
  videoContainerRef, 
  videoRef, 
  audioRef, 
  isHost, 
  isConnected, 
  isLive, 
  isMuted, 
  volume, 
  toggleMute, 
  streamDetails, 
  handleVolumeChange, 
  isFullscreen,
  toggleFullscreen
 }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(true)

  const handlePlayPause = () => {
    const video = videoRef.current
    if (!video) return
    if (video.paused) {
      video.play()
      setIsPlaying(true)
    } else {
      video.pause()
      setIsPlaying(false)
    }
  }

  const { viewerCount, isLoading } = useViewerCount({
    streamId: streamDetails?._id,
    isLive,
    updateInterval: 15000,
    enabled: Boolean(streamDetails?._id && isLive),
  })

  // Get stream details with URLs
  const streamWithUrls = useQuery(api.streams.getStreamWithUrls, { 
    streamId: streamDetails?._id 
  })

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  };

  return (
    <div ref={videoContainerRef} className="relative w-full aspect-video bg-black overflow-hidden group">
      {/* Show thumbnail or user image when stream is scheduled or ended */}
      {streamDetails?.status === "scheduled" || streamDetails?.status === "ended" ? (
        <div className="absolute inset-0 flex items-center justify-center bg-black">
          {streamWithUrls?.thumbnailUrl ? (
            <div className="relative w-full h-full">
              <Image
                src={streamWithUrls.thumbnailUrl}
                alt="Stream thumbnail"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-black/50" />
              <div className="absolute inset-0 flex flex-col items-center justify-center text-white space-y-6">
                <div className="text-center space-y-2">
                  {streamDetails?.status === "ended" ? (
                    <div className="flex items-center justify-center gap-2 text-red-500">
                      <IconPlayerPlay className="h-6 w-6" />
                      <span className="text-xl font-semibold">Stream Ended</span>
                    </div>
                  ) : (
                    <>
                      <div className="flex items-center justify-center gap-2 text-primary">
                        <IconClock className="h-6 w-6" />
                        <span className="text-xl font-semibold">Scheduled Stream</span>
                      </div>
                      {streamDetails.scheduledStartTime && (
                        <p className="text-lg">
                          {formatDateTime(streamDetails.scheduledStartTime)}
                        </p>
                      )}
                    </>
                  )}
                </div>
                <UserAvatar
                  user={streamWithUrls.streamer}
                  size="xl"
                />
                <div className="text-center">
                  <h2 className="text-2xl font-bold mb-2">{streamDetails.title}</h2>
                  <p className="text-lg opacity-90">
                    with {streamWithUrls.streamer?.username || streamDetails.hostName}
                  </p>
                </div>
              </div>
            </div>
          ) : streamWithUrls?.streamer?.image ? (
            <div className="relative w-full h-full">
              <Image
                src={streamWithUrls.streamer.image}
                alt="Streamer profile"
                fill
                className="object-cover blur-sm opacity-30"
              />
              <div className="absolute inset-0 flex flex-col items-center justify-center text-white space-y-6">
                <div className="text-center space-y-2">
                  {streamDetails?.status === "ended" ? (
                    <div className="flex items-center justify-center gap-2 text-red-500">
                      <IconPlayerPlay className="h-6 w-6" />
                      <span className="text-xl font-semibold">Stream Ended</span>
                    </div>
                  ) : (
                    <>
                      <div className="flex items-center justify-center gap-2 text-primary">
                        <IconClock className="h-6 w-6" />
                        <span className="text-xl font-semibold">Scheduled Stream</span>
                      </div>
                      {streamDetails.scheduledStartTime && (
                        <p className="text-lg">
                          {formatDateTime(streamDetails.scheduledStartTime)}
                        </p>
                      )}
                    </>
                  )}
                </div>
                <UserAvatar
                  user={streamWithUrls.streamer}
                  size="xl"
                />
                <div className="text-center">
                  <h2 className="text-2xl font-bold mb-2">{streamDetails.title}</h2>
                  <p className="text-lg opacity-90">
                    with {streamWithUrls.streamer?.username || streamDetails.hostName}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center text-white space-y-6">
              <div className="text-center space-y-2">
                {streamDetails?.status === "ended" ? (
                  <div className="flex items-center justify-center gap-2 text-red-500">
                    <IconPlayerPlay className="h-6 w-6" />
                    <span className="text-xl font-semibold">Stream Ended</span>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center justify-center gap-2 text-primary">
                      <IconClock className="h-6 w-6" />
                      <span className="text-xl font-semibold">Scheduled Stream</span>
                    </div>
                    {streamDetails.scheduledStartTime && (
                      <p className="text-lg">
                        {formatDateTime(streamDetails.scheduledStartTime)}
                      </p>
                    )}
                  </>
                )}
              </div>
              <UserAvatar
                user={streamDetails?.streamer}
                size="xl"
              />
              <div className="text-center">
                <h2 className="text-2xl font-bold mb-2">{streamDetails.title}</h2>
                <p className="text-lg opacity-90">
                  with {streamDetails?.streamer?.username || streamDetails.hostName}
                </p>
              </div>
            </div>
          )}
        </div>
      ) : (
        <>
          {/* Video placeholder */}
          <video ref={videoRef} autoPlay playsInline muted={false} className="w-full h-full object-contain"></video>
          {/* Audio element for better audio handling */}
          <audio ref={audioRef} autoPlay playsInline className="hidden"></audio>
        </>
      )}

      {/* Live indicator */}
      {isLive && (
        <div className="absolute top-4 left-4">
          <Badge variant="destructive" className="bg-red-600 hover:bg-red-600">
            🔴 LIVE
          </Badge>
        </div>
      )}

      {/* Viewer count */}
      {isLive && (
        <div className="absolute top-4 right-4">
          <Badge variant="secondary" className="bg-black/50 text-white">
            {isLoading ? "..." : formatViewerCount(viewerCount)} viewers
          </Badge>
        </div>
      )}

      {/* Video controls - only show for live streams */}
      {streamDetails?.status === "live" && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none group-hover:pointer-events-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" className="text-white hover:bg-white/20" onClick={handlePlayPause}>
                {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
              </Button>
              <Button variant="ghost" size="icon" className="text-white hover:bg-white/20" onClick={toggleMute}>
                {isMuted ? <IconVolume3 size={20} /> : <Volume2 className="h-5 w-5" />}
              </Button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.05"
                value={isMuted ? 0 : volume}
                onChange={handleVolumeChange}
                className="w-20 h-1.5 accent-primary cursor-pointer"
              />
            </div>

            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" className="text-white hover:bg-white/20" onClick={toggleFullscreen}>
                {isFullscreen ? <IconMinimize size={20} /> : <Maximize className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
