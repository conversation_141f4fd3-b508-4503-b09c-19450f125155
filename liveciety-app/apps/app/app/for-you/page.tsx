"use client";

import React from "react";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Doc } from "@workspace/backend/convex/_generated/dataModel";
import {
  PremierShopsSection,
  PopularShowsSection,
} from "@/components/stream-sections";
import { StreamCard } from "@/components/stream-card";
import { useCurrentUser } from "@/hooks/use-current-user";
import { getCategoryTitleById, getSubcategoryTitleById } from "@workspace/lib/constants/category-utils";

interface EnrichedStream extends Doc<"streams"> {
  streamer: Doc<"users"> | null;
  viewerCount: number;
  viewers: Doc<"users">[];
  metrics: {
    totalViews: number;
    peakViewerCount: number;
  };
}

export default function ForYouPage() {
  const { user } = useCurrentUser();
  
  const followedUsersResult = useQuery(
    api.users.getFollowing,
    user?._id
      ? {
          userId: user._id,
          paginationOpts: {
            numItems: 20,
            cursor: null,
          },
        }
      : "skip"
  );
  
  const streams = useQuery(api.streams.getStreams, {
    paginationOpts: {
      numItems: 30,
      cursor: null,
    },
  });
  
  if (!streams || !user) {
    return (
      <div className="p-4">
        <div className="text-center">Loading personalized content...</div>
      </div>
    );
  }
  
  const preferences = user.preferences || {};
  const followedCategories = preferences.categories || [];
  const followedSubcategories = preferences.subcategories || [];
  const followedUsers = followedUsersResult?.users || [];
  const followedHostIds = followedUsers.map(user => user._id) || [];
  
  const typedStreams = streams.page.map(stream => {
    
    return {
      ...stream,
    };
  }) as EnrichedStream[];
  
  const categoryStreams = typedStreams.filter(stream => 
    stream.category && followedCategories.includes(stream.category)
  );
  
  const subcategoryStreams = typedStreams.filter(stream => 
    stream.subcategory && followedSubcategories.includes(stream.subcategory)
  );
  
  const followedHostStreams = typedStreams.filter(stream => {
    if (!stream.streamer || !stream.streamer._id) return false;
    return followedHostIds.includes(stream.streamer._id);
  });
  
  const allPersonalizedStreamIds = new Set([
    ...categoryStreams.map(s => s._id),
    ...subcategoryStreams.map(s => s._id),
    ...followedHostStreams.map(s => s._id),
  ]);
  
  const personalizedStreams = typedStreams
    .filter(stream => allPersonalizedStreamIds.has(stream._id));
  
  const sortedByViewers = [...personalizedStreams].sort(
    (a, b) => b.viewerCount - a.viewerCount
  );
  
  const premierStreams = personalizedStreams
    .filter(
      (stream) => {
        const isSeller = stream.streamer?.role === "seller";
        const isVerified = stream.streamer?.sellerProfile?.verified === true;
        
        return isSeller && isVerified;
      }
    )
    .slice(0, 5);
  
  const popularStreams = sortedByViewers
    .filter((stream) => !premierStreams.includes(stream))
    .slice(0, 5);
  
  const followedCategoryStreams = Object.entries(
    personalizedStreams.reduce<Record<string, EnrichedStream[]>>((acc, stream) => {
      if (stream.category && followedCategories.includes(stream.category)) {
        if (!acc[stream.category]) {
          acc[stream.category] = [];
        }
        acc[stream.category]?.push(stream);
      }
      return acc;
    }, {})
  );
  
  const followedSubcategoryStreams = Object.entries(
    personalizedStreams.reduce<Record<string, Record<string, EnrichedStream[]>>>((acc, stream) => {
      if (
        stream.category &&
        stream.subcategory &&
        followedSubcategories.includes(stream.subcategory)
      ) {
        if (!acc[stream.category]) {
          acc[stream.category] = {};
        }
        if (!acc[stream.category]![stream.subcategory]) {
          acc[stream.category]![stream.subcategory] = [];
        }
        acc[stream.category]![stream.subcategory]!.push(stream);
      }
      return acc;
    }, {})
  );
  
  const hasPersonalizedContent = personalizedStreams.length > 0;
  
  const safeStreamDisplay = (stream: EnrichedStream) => {
    return stream as unknown as Parameters<typeof StreamCard>[0]['stream'];
  };
  
  return (
    <div className="space-y-8 p-4">
      <h1 className="text-3xl font-bold mb-6">For You</h1>
      
      {!hasPersonalizedContent && (
        <div className="text-center p-8 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Personalize Your Feed</h2>
          <p className="text-muted-foreground mb-4">
            Follow categories, subcategories, or hosts to see personalized content here.
          </p>
        </div>
      )}
      
      {followedHostStreams.length > 0 && (
        <section className="bg-card rounded-lg border">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold text-foreground">From Hosts You Follow</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
            {followedHostStreams.slice(0, 5).map((stream) => (
              <StreamCard key={stream._id} stream={safeStreamDisplay(stream)} />
            ))}
          </div>
        </section>
      )}
      
      {popularStreams.length > 0 && (
        <PopularShowsSection streams={popularStreams.map(safeStreamDisplay)} />
      )}
      
      {premierStreams.length > 0 && (
        <PremierShopsSection streams={premierStreams.map(safeStreamDisplay)} />
      )}
      
      {followedCategoryStreams.map(([categoryId, streams]) => (
        <section key={categoryId} className="bg-card p-6 rounded-lg border">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold text-foreground">{getCategoryTitleById(categoryId)}</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {streams.slice(0, 5).map((stream) => (
              <StreamCard key={stream._id} stream={safeStreamDisplay(stream)} />
            ))}
          </div>
        </section>
      ))}
      
      {followedSubcategoryStreams.map(([categoryId, subMap]) =>
        Object.entries(subMap).map(([subcategoryId, streams]) => (
          <section key={subcategoryId} className="bg-card p-6 rounded-lg border">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-bold text-foreground">
                {getSubcategoryTitleById(categoryId, subcategoryId)}
              </h2>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {streams.slice(0, 5).map((stream) => (
                <StreamCard key={stream._id} stream={safeStreamDisplay(stream)} />
              ))}
            </div>
          </section>
        ))
      )}
    </div>
  );
}