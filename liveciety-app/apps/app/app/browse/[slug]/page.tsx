"use client";

import Link from "next/link";
import Image from "next/image";
import { notFound, useRouter, useSearchParams } from "next/navigation";
import { categories, subcategories } from "@workspace/lib/constants/categories";
import { X } from "lucide-react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useQuery, useMutation } from "convex/react";
import React, { useState, useEffect } from 'react';
import { Button } from "@workspace/ui/components/button";
import { UserAvatar } from "@/components/user-avatar";
import { getAvatarImageUrl } from "@/lib/utils";
import { useCurrentUser } from "@/hooks/use-current-user";

interface CategoryPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const unwrappedParams = React.use(params);
  const { slug } = unwrappedParams;
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useCurrentUser();
  
  const toggleCategoryFollow = useMutation(api.users.toggleCategoryFollow);
  const toggleSubcategoryFollow = useMutation(api.users.toggleSubcategoryFollow);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followedSubcategories, setFollowedSubcategories] = useState<string[]>([]);
  
  const initialSubcategory = searchParams.get('subcategory');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(initialSubcategory);
  
  useEffect(() => {
    const subcategoryParam = searchParams.get('subcategory');
    setSelectedSubcategory(subcategoryParam);
  }, [searchParams]);
  
  const category = categories.find((cat) => cat.id === slug);
  
  const categorySubcategories = subcategories[slug] || [];
  
  const selectedSubcategoryObject = selectedSubcategory 
    ? categorySubcategories.find(subcat => subcat.id === selectedSubcategory)
    : null;
  
  const streams = useQuery(api.streams.listByCategory, {
    category: slug,
    subcategory: selectedSubcategory || undefined,
    paginationOpts: { numItems: 12, cursor: null }
  });
  
  useEffect(() => {
    if (user && user.preferences) {
      setIsFollowing(user.preferences.categories?.includes(slug) || false);
      
      const userSubcats = user.preferences.subcategories || [];
      setFollowedSubcategories(userSubcats);
    }
  }, [user, slug]);
  
  if (!category) {
    notFound();
  }
  
  const handleFollowToggle = async () => {
    try {
      const result = await toggleCategoryFollow({ categoryId: slug });
      setIsFollowing(result.isFollowed);
    } catch (error) {
      console.error("Error toggling category follow:", error);
    }
  };
  
  const handleSubcategoryFollowToggle = async (subcategoryId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      const result = await toggleSubcategoryFollow({ subcategoryId });
      
      if (result.isFollowed) {
        setFollowedSubcategories(prev => [...prev, subcategoryId]);
      } else {
        setFollowedSubcategories(prev => prev.filter(id => id !== subcategoryId));
      }
    } catch (error) {
      console.error("Error toggling subcategory follow:", error);
    }
  };
  
  const handleSubcategorySelect = (subcategoryId: string) => {
    if (selectedSubcategory === subcategoryId) {
      setSelectedSubcategory(null);
      router.push(`/browse/${slug}`);
    } else {
      setSelectedSubcategory(subcategoryId);
      router.push(`/browse/${slug}?subcategory=${subcategoryId}`);
    }
  };

  return (
    <div>
      <div className="sticky top-0 z-10 bg-background border-b">
        <div className="mx-auto px-4 py-4">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">{category.title}</h1>
              {selectedSubcategoryObject && (
                <div className="flex items-center ml-4">
                  <span className="font-medium">{selectedSubcategoryObject.title}</span>
                  <Button 
                    size="icon"
                    variant="outline"
                    onClick={() => handleSubcategorySelect(selectedSubcategory!)}
                    className="ml-2"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
            <Button 
              onClick={selectedSubcategoryObject 
                ? (e) => handleSubcategoryFollowToggle(selectedSubcategory!, e) 
                : handleFollowToggle}
              className={`px-4 py-1 rounded-full text-sm font-semibold border ${
                selectedSubcategoryObject
                  ? followedSubcategories.includes(selectedSubcategory!)
                    ? 'bg-primary/10 border-primary text-primary'
                    : 'bg-primary text-primary-foreground border-primary'
                  : isFollowing
                    ? 'bg-primary/10 border-primary text-primary'
                    : 'bg-primary text-primary-foreground border-primary'
              }`}
            >
              {selectedSubcategoryObject
                ? followedSubcategories.includes(selectedSubcategory!)
                  ? 'Following'
                  : 'Follow'
                : isFollowing
                  ? 'Following'
                  : 'Follow'
              }
            </Button>
          </div>
          
          <div className="flex gap-2 overflow-x-auto py-2 px-1 hide-scrollbar relative">
            {categorySubcategories.map((subcategory) => {
              const isSubcategoryFollowed = followedSubcategories.includes(subcategory.id);
              const isSelected = selectedSubcategory === subcategory.id;
              return (
                <div 
                  key={subcategory.id}
                  className="flex-shrink-0 relative group"
                  style={{ width: 160, height: 64 }}
                >
                  <div
                    onClick={() => handleSubcategorySelect(subcategory.id)}
                    className={`relative overflow-hidden cursor-pointer rounded-xl border transition-all duration-200 flex items-center justify-center h-16 w-40 ${
                      isSelected
                        ? 'ring-2 ring-primary bg-primary text-primary-foreground'
                        : isSubcategoryFollowed 
                          ? 'bg-primary/10 text-primary border-primary' 
                          : 'bg-muted hover:bg-muted/80 text-foreground'
                    }`}
                  >
                    {subcategory.image && (
                      <Image
                        src={subcategory.image}
                        alt={subcategory.title}
                        fill
                        className="object-cover z-0"
                        style={{ opacity: 0.35 }}
                        sizes="160px"
                        priority={false}
                      />
                    )}
                    <span className="relative z-10 text-base font-semibold text-shadow-md text-center px-2">
                      {subcategory.title}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      <div className="mx-auto p-4">
        {!streams && (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="flex flex-col">
                <div className="aspect-video bg-muted animate-pulse rounded-lg" />
                <div className="mt-2 space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full bg-muted animate-pulse" />
                    <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                  </div>
                  <div className="h-4 w-full bg-muted animate-pulse rounded" />
                </div>
              </div>
            ))}
          </div>
        )}
        
        {streams && streams.page && (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {streams.page.length > 0 ? (
              streams.page.map((stream) => {
                if (!stream) return null;
                return (
                  <Link key={stream._id} href={`/stream/${stream._id}`} className="flex flex-col">
                    <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
                      {stream.status === "live" && (
                        <div className="absolute top-2 left-2 bg-red-600 text-white text-xs px-2 py-0.5 rounded-sm z-20">
                          Live · {stream.viewerCount || 0}
                        </div>
                      )}
                      <Image 
                        src={getAvatarImageUrl(stream.thumbnailUrl) as string}
                        alt={stream.title || "Stream"}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    </div>
                    <div className="mt-2">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-muted overflow-hidden relative">
                          {stream.streamer && (
                            <UserAvatar
                              user={stream.streamer}
                              size="default"
                            />
                          )}
                        </div>
                        <span className="text-sm font-medium">{stream.streamer?.username || "Anonymous"}</span>
                      </div>
                      <p className="text-sm mt-1 line-clamp-2">{stream.title || "Untitled Stream"}</p>
                    </div>
                  </Link>
                );
              })
            ) : (
              <div className="col-span-full py-8 text-center text-muted-foreground">
                {selectedSubcategory 
                  ? `No streams found in the ${selectedSubcategoryObject?.title} subcategory.` 
                  : `No streams found in the ${category.title} category.`}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
} 