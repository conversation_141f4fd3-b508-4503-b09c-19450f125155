"use client";

import React from "react";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Doc } from "@workspace/backend/convex/_generated/dataModel";
import { StreamCard } from "@/components/stream-card";

interface EnrichedStream extends Doc<"streams"> {
  title: string;
  isLive: boolean;
  isChatEnabled: boolean;
  isChatDelayed: boolean;
  isChatFollowersOnly: boolean;
  updatedAt: number;
  streamer: Doc<"users"> | null;
  viewerCount: number;
  viewers: Doc<"users">[];
  metrics: {
    totalViews: number;
    peakViewerCount: number;
  };
}

interface StreamsByHost {
  [hostId: string]: {
    streams: EnrichedStream[];
    hostName: string;
  };
}

export default function FollowedHostsPage() {
  const user = useQuery(api.users.viewer);
  const followedUsersResult = useQuery(api.users.getFollowing, { 
    userId: user?._id || null as any,
    paginationOpts: {
      numItems: 20,
      cursor: null
    }
  });
  const streams = useQuery(api.streams.getStreams, {
    paginationOpts: {
      numItems: 30,
      cursor: null,
    },
  });
  
  if (!streams || !user || !followedUsersResult) {
    return (
      <div className="p-4">
        <div className="text-center">Loading followed hosts content...</div>
      </div>
    );
  }
  
  const followedUsers = followedUsersResult.users || [];
  
  const followedHostIds = followedUsers.map(user => user._id) || [];
  
  const typedStreams = streams.page.map(stream => {
  
    return {
      ...stream,
    };
  }) as EnrichedStream[];
  
  const followedHostStreams = typedStreams.filter(stream => {
    if (!stream.streamer || !stream.streamer._id) return false;
    return followedHostIds.includes(stream.streamer._id);
  });
  
  const streamsByHost: StreamsByHost = {};
  
  followedHostStreams.forEach(stream => {
    if (stream.streamer && stream.streamer._id) {
      const hostId = stream.streamer._id;
      const hostName = stream.streamer?.name ?? 'Unknown Host';
      
      if (!streamsByHost[hostId]) {
        streamsByHost[hostId] = {
          streams: [],
          hostName: hostName
        };
      }
      
      streamsByHost[hostId].streams.push(stream);
    }
  });
  
  const sortedStreamsByHost = Object.entries(streamsByHost).reduce((acc, [hostId, hostData]) => {
    const sortedStreams = [...hostData.streams].sort((a, b) => {
      if (a.isLive && !b.isLive) return -1;
      if (!a.isLive && b.isLive) return 1;
      return b.viewerCount - a.viewerCount;
    });
    
    acc[hostId] = {
      ...hostData,
      streams: sortedStreams
    };
    
    return acc;
  }, {} as StreamsByHost);
  
  const safeStreamDisplay = (stream: EnrichedStream) => {
    return stream as unknown as Parameters<typeof StreamCard>[0]['stream'];
  };
  
  const hasFollowedHosts = followedUsers.length > 0;
  const hasActiveStreams = followedHostStreams.length > 0;
  
  return (
    <div className="space-y-8 p-4">
      <h1 className="text-3xl font-bold mb-6">Followed Hosts</h1>
      
      {!hasFollowedHosts && (
        <div className="text-center p-8 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">You're Not Following Any Hosts Yet</h2>
          <p className="text-muted-foreground mb-4">
            Follow hosts to see their streams and content here.
          </p>
        </div>
      )}
      
      {hasFollowedHosts && !hasActiveStreams && (
        <div className="text-center p-8 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">No Active Streams</h2>
          <p className="text-muted-foreground mb-4">
            Hosts you follow don't have any active streams right now.
          </p>
        </div>
      )}
      
      {followedHostStreams.filter(stream => stream.isLive).length > 0 && (
        <section className="bg-card p-6 rounded-lg border">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold text-foreground">Live Now</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {followedHostStreams
              .filter(stream => stream.isLive)
              .slice(0, 5)
              .map((stream) => (
                <StreamCard key={stream._id} stream={safeStreamDisplay(stream)} />
              ))}
          </div>
        </section>
      )}
      
      {Object.entries(sortedStreamsByHost).map(([hostId, hostData]) => (
        <section key={hostId} className="bg-card p-6 rounded-lg border">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold text-foreground">{hostData.hostName}</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {hostData.streams.slice(0, 5).map((stream) => (
              <StreamCard key={stream._id} stream={safeStreamDisplay(stream)} />
            ))}
          </div>
        </section>
      ))}
    </div>
  );
}
