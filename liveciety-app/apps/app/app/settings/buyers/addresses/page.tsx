"use client";

import { useEffect, useState, useRef } from "react";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { Card } from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Select, SelectItem, SelectTrigger, SelectValue, SelectContent } from "@workspace/ui/components/select";
import { useMutation, usePreloadedQuery, useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { toast } from "sonner";
import { useJsApiLoader } from "@react-google-maps/api";

type AddressType = {
  country: string;
  fullName: string;
  street: string;
  address2?: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
  isReturn: boolean;
};

const autocompleteStyles = `
  .pac-container {
    z-index: 99999 !important;
    background-color: #e4e4e7 !important;
    border-radius: 0.5rem !important;
    border: 1px solid rgba(255, 255, 255, 0.08) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    margin-top: 0.25rem !important;
    padding: 0.25rem !important;
    font-family: inherit !important;
    font-size: 0.875rem !important;
    pointer-events: auto !important;
    position: absolute !important;
  }
  
  .pac-item {
    padding: 0.375rem 0.5rem !important;
    color: #27272a !important;
    border-radius: 0.25rem !important;
    cursor: pointer !important;
    border-top: none !important;
    pointer-events: auto !important;
  }
  
  .pac-item:hover {
    background-color: rgba(113, 113, 122, 0.5) !important;
  }
  
  .pac-item-selected {
    background-color: rgba(113, 113, 122, 0.5) !important;
  }
  
  .pac-icon {
    margin-right: 0.5rem !important;
  }
  
  .pac-item-query {
    font-size: 0.875rem !important;
    color: #27272a !important;
  }
  
  @media (prefers-color-scheme: dark) {
    .pac-container {
      background-color: #18181b !important;
      border: 1px solid rgba(255, 255, 255, 0.08) !important;
    }
    
    .pac-item, .pac-item-query {
      color: #f4f4f5 !important;
    }
    
    .pac-item:hover, .pac-item-selected {
      background-color: rgba(63, 63, 70, 0.5) !important;
    }
  }
`;

const US_STATES = [
  "AL","AK","AZ","AR","CA","CO","CT","DE","FL","GA","HI","ID","IL","IN","IA","KS","KY","LA","ME","MD","MA","MI","MN","MS","MO","MT","NE","NV","NH","NJ","NM","NY","NC","ND","OH","OK","OR","PA","RI","SC","SD","TN","TX","UT","VT","VA","WA","WV","WI","WY"
];

function AddressModal({ open, onClose, onSave, initial, isEdit }: { open: boolean, onClose: () => void, onSave: (address: AddressType) => Promise<void>, initial?: AddressType, isEdit?: boolean }) {
  const [country, setCountry] = useState(initial?.country || "United States");
  const [fullName, setFullName] = useState(initial?.fullName || "");
  const [address1, setAddress1] = useState(initial?.street || "");
  const [address2, setAddress2] = useState(initial?.address2 || "");
  const [city, setCity] = useState(initial?.city || "");
  const [state, setState] = useState(initial?.state || "");
  const [postalCode, setPostalCode] = useState(initial?.zipCode || "");
  const [isDefault, setIsDefault] = useState(initial?.isDefault || false);
  const [isReturn, setIsReturn] = useState(initial?.isReturn || false);
  const [loading, setLoading] = useState(false);
  const addressInputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete>(null);

  const { isLoaded } = useJsApiLoader({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!,
    libraries: ["places"],
  });

  useEffect(() => {
    if (isLoaded && addressInputRef.current) {
      autocompleteRef.current = new window.google.maps.places.Autocomplete(addressInputRef.current!, {
        fields: ["address_components", "geometry"],
      });
      autocompleteRef.current.addListener("place_changed", onPlaceChanged);
    }
    return () => {
      if (autocompleteRef.current) {
        window.google.maps.event.clearInstanceListeners(autocompleteRef.current);
        autocompleteRef.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoaded, addressInputRef.current]);

  const onPlaceChanged = () => {
    if (autocompleteRef.current) {
      const place = autocompleteRef.current.getPlace() as google.maps.places.PlaceResult;
      if (!place.address_components) {
        return;
      }
      
      const get = (type: string) =>
        place.address_components?.find((c) => c.types.includes(type))?.long_name || "";
      
      const stateShort = place.address_components?.find((c) => 
        c.types.includes("administrative_area_level_1"))?.short_name || "";
      
      setAddress1(`${get("street_number")} ${get("route")}`.trim());
      setCity(get("locality") || get("sublocality") || get("administrative_area_level_2"));
      
      if (stateShort && US_STATES.includes(stateShort)) {
        setState(stateShort);
      } else {
        setState(get("administrative_area_level_1"));
      }
      
      setPostalCode(get("postal_code"));
      
      const countryName = get("country");
      if (countryName === "United States") {
        setCountry(countryName);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onSave({
        country,
        fullName,
        street: address1,
        address2,
        city,
        state,
        zipCode: postalCode,
        isDefault,
        isReturn,
      });
      onClose();
    } catch (err: unknown) {
      toast.error(err instanceof Error ? err.message : "Failed to save address");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      const pacContainer = document.querySelector('.pac-container');
      if (pacContainer && (e.target instanceof Node) && pacContainer.contains(e.target)) {
        e.stopPropagation();
        e.preventDefault();
      }
    };

    document.addEventListener('click', handleDocumentClick, true);
    document.addEventListener('mousedown', handleDocumentClick, true);

    return () => {
      document.removeEventListener('click', handleDocumentClick, true);
      document.removeEventListener('mousedown', handleDocumentClick, true);
    };
  }, []);

  useEffect(() => {
    if (!open) return;
    
    const styleElement = document.createElement('style');
    styleElement.type = 'text/css';
    styleElement.appendChild(document.createTextNode(autocompleteStyles));
    document.head.appendChild(styleElement);
    
    return () => {
      if (styleElement.parentNode) {
        document.head.removeChild(styleElement);
      }
    };
  }, [open]);

  return (
    <Dialog 
      open={open} 
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          if (!document.querySelector('.pac-container:hover')) {
            onClose();
          }
        }
      }}
    >
      <DialogContent className="overflow-visible">
        <DialogHeader>
          <DialogTitle>{isEdit ? "Edit Shipping Address" : "Add Shipping Address"}</DialogTitle>
        </DialogHeader>
        <form 
          onSubmit={handleSubmit} 
          className="space-y-4"
        >
          <div>
            <label className="block text-sm font-medium mb-1">Country or region *</label>
            <Select value={country} onValueChange={setCountry}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="United States">United States</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Full name *</label>
            <Input value={fullName} onChange={e => setFullName(e.target.value)} required autoComplete="name" />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Address Line 1 *</label>
            <Input
              ref={addressInputRef}
              value={address1}
              onChange={e => setAddress1(e.target.value)}
              required
              autoComplete="address-line1"
              name="address-line1"
              className="focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Address Line 2</label>
            <Input value={address2} onChange={e => setAddress2(e.target.value)} autoComplete="address-line2" />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">City *</label>
            <Input value={city} onChange={e => setCity(e.target.value)} required autoComplete="address-level2" />
          </div>
          <div className="flex gap-2">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">State *</label>
              <Select value={state} onValueChange={setState}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="State" />
                </SelectTrigger>
                <SelectContent>
                  {US_STATES.map(s => (
                    <SelectItem key={s} value={s}>{s}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Postal Code *</label>
              <Input value={postalCode} onChange={e => setPostalCode(e.target.value)} required autoComplete="postal-code" />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" checked={isDefault} onChange={e => setIsDefault(e.target.checked)} id="default" />
            <label htmlFor="default" className="text-sm">Set as default address</label>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" checked={isReturn} onChange={e => setIsReturn(e.target.checked)} id="return" />
            <label htmlFor="return" className="text-sm">Set as return address</label>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={loading}>Cancel</Button>
            <Button type="submit" disabled={loading || !fullName || !address1 || !city || !state || !postalCode}>
              {loading ? "Saving..." : isEdit ? "Update address" : "Add address"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default function AddressesPage() {
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);
  const [addresses, setAddresses] = useState<AddressType[]>(
    (user?.preferences?.shippingAddresses || []).map((a) => ({
      country: a.country ?? "",
      fullName: a.fullName ?? "",
      street: a.street ?? "",
      address2: a.address2 ?? "",
      city: a.city ?? "",
      state: a.state ?? "",
      zipCode: a.zipCode ?? "",
      isDefault: a.isDefault ?? false,
      isReturn: a.isReturn ?? false,
    }))
  );
  const [showModal, setShowModal] = useState(false);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [, setLoading] = useState(false);

  const updateUser = useMutation(api.users.update);
  const updateStripeCustomer = useAction(api.integration.stripe.updateCustomerBillingAddress);

  useEffect(() => {
    setAddresses(
      (user?.preferences?.shippingAddresses || []).map((a) => ({
        country: a.country ?? "",
        fullName: a.fullName ?? "",
        street: a.street ?? "",
        address2: a.address2 ?? "",
        city: a.city ?? "",
        state: a.state ?? "",
        zipCode: a.zipCode ?? "",
        isDefault: a.isDefault ?? false,
        isReturn: a.isReturn ?? false,
      }))
    );
  }, [user]);

  const handleSave = async (address: AddressType) => {
    setLoading(true);
    try {
      if (!user) {
        toast.error("User not loaded");
        setLoading(false);
        return;
      }
      let newAddresses = [...addresses];
      if (editIndex !== null) {
        newAddresses[editIndex] = address;
      } else {
        if (address.isDefault) {
          newAddresses = newAddresses.map(a => ({ ...a, isDefault: false }));
        }
        newAddresses.push(address);
      }
      await updateUser({
        id: user._id,
        preferences: {
          ...user.preferences,
          shippingAddresses: newAddresses,
        },
      });
      if (address.isDefault) {
        await updateStripeCustomer({
          address: {
            line1: address.street,
            line2: address.address2,
            city: address.city,
            state: address.state,
            country: address.country,
            postal_code: address.zipCode,
            name: address.fullName,
          },
        });
      }
      setShowModal(false);
      setEditIndex(null);
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast.error(err.message || "Failed to save address");
      } else {
        toast.error("Failed to save address");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRemove = async (idx: number) => {
    if (!user) {
      toast.error("User not loaded");
      return;
    }
    const newAddresses = addresses.filter((_, i) => i !== idx);
    await updateUser({
      id: user._id,
      preferences: {
        ...user.preferences,
        shippingAddresses: newAddresses,
      },
    });
    setAddresses(newAddresses);
  };

  return (
    <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
      <h1 className="text-2xl font-semibold mb-2">Addresses</h1>
      <p className="text-gray-400 mb-6 text-md">Manage your shipping address details</p>
      <div className="space-y-8">
        <Card className="p-6 border-none">
          <div className="flex items-center justify-between mb-4 gap-4">
            <div>
              <p className="text-gray-500 text-sm mt-1">
                Add and manage your shipping addresses for orders and deliveries. Your default address will be used for billing and shipping.
              </p>
            </div>
            <Button onClick={() => { setShowModal(true); setEditIndex(null); }} size="default">
              Add
            </Button>
          </div>
          {addresses.length > 0 ? (
            <div className="space-y-4 mt-4">
              {addresses.map((address, idx) => (
                <div key={idx}>
                  <Card className="p-4 dark:!bg-muted/50 dark:hover:!bg-muted/70 transition-all duration-200">
                  <div className="flex justify-between items-center">
                    <div className="flex flex-col">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-medium border rounded-md p-1">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M17.657 16.657L13.414 20.9a2 2 0 01-2.828 0l-4.243-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                        </span>
                        <span className="font-semibold">{address.fullName}</span>
                        {address.isDefault && (
                          <span className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-0.5 rounded-full">
                            Default
                          </span>
                        )}
                        {address.isReturn && (
                          <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-0.5 rounded-full">
                            Return
                          </span>
                        )}
                      </div>
                      <span className="text-sm text-gray-500 mt-1">
                        {address.street}
                        {address.address2 ? `, ${address.address2}` : ""}
                        , {address.city}, {address.state} {address.zipCode}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      {!address.isDefault && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleSave({...address, isDefault: true})}
                        >
                          Set Default
                        </Button>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="text-red-500 hover:text-red-700"
                        onClick={() => handleRemove(idx)}
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                  </Card>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-gray-500">
              <p>You don&apos;t have any shipping addresses saved yet.</p>
              <p className="mt-2">Add an address to use for orders and billing.</p>
            </div>
          )}
        </Card>
        {(showModal || editIndex !== null) && (
          <AddressModal
            open={showModal}
            onClose={() => { setShowModal(false); setEditIndex(null); }}
            onSave={handleSave}
            initial={editIndex !== null ? addresses[editIndex] : undefined}
            isEdit={editIndex !== null}
          />
        )}
      </div>
    </div>
  );
} 