"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Edit2, Trash2 } from "lucide-react";
import Image from "next/image";
import { getAvatarImageUrl } from "@/lib/utils";
import { getCategoryTitleById } from "@workspace/lib/constants/category-utils";
import { EditProductModal } from "./edit-product-modal";
import { ConfirmDialog } from "@workspace/ui/components/confirm-dialog";
import { toast } from "sonner";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

export function ProductsTable() {
  const products = useQuery(api.sellers.listProducts) || [];
  const deleteProduct = useMutation(api.sellers.deleteProduct);
  const [editingProduct, setEditingProduct] = useState<typeof products[0] | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Id<"products"> | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!productToDelete) return;
    
    setIsDeleting(true);
    try {
      await deleteProduct({ productId: productToDelete });
      toast.success("Product deleted successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to delete product");
    } finally {
      setIsDeleting(false);
      setProductToDelete(null);
    }
  };

  const getStatusVariant = (status: string | undefined) => {
    switch (status) {
      case "active":
        return "primary";
      case "draft":
        return "secondary";
      case "archived":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Inventory</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.map((product) => (
              <TableRow key={product._id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="h-12 w-12 rounded-md overflow-hidden bg-accent/20 flex items-center justify-center">
                      {product.images?.[0] ? (
                        <Image
                          src={getAvatarImageUrl(product.images[0]) || ""}
                          alt={product.name}
                          width={48}
                          height={48}
                          className="object-cover"
                        />
                      ) : (
                        <div className="text-2xl">📦</div>
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{product.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {product.description?.slice(0, 50)}
                        {product.description && product.description.length > 50 ? "..." : ""}
                      </p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {product.category ? (
                    <span>{getCategoryTitleById(product.category)}</span>
                  ) : (
                    <span className="text-muted-foreground">—</span>
                  )}
                </TableCell>
                <TableCell>
                  ${product.price?.toFixed(2) || "0.00"}
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusVariant(product.status)}>
                    {product.status || "draft"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {product.inventory || 0} available
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => {
                        setEditingProduct(product);
                        setIsEditModalOpen(true);
                      }}
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-destructive hover:text-destructive"
                      onClick={() => setProductToDelete(product._id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {products.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <p className="text-muted-foreground">No products found</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Add your first product to get started
                  </p>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <EditProductModal
        open={isEditModalOpen}
        onOpenChange={(open) => {
          setIsEditModalOpen(open);
          if (!open) setEditingProduct(null);
        }}
        product={editingProduct as any}
      />

      <ConfirmDialog
        open={!!productToDelete}
        onOpenChange={(open) => !open && setProductToDelete(null)}
        title="Delete Product"
        description="Are you sure you want to delete this product? This action cannot be undone."
        confirmLabel="Delete"
        cancelLabel="Cancel"
        confirmVariant="destructive"
        loading={isDeleting}
        onConfirm={handleDelete}
      />
    </>
  );
} 