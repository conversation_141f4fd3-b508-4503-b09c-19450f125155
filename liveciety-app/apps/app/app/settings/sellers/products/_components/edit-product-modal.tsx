"use client";

import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Switch } from "@workspace/ui/components/switch";
import { categories, Subcategory, subcategories } from "@workspace/lib/constants/categories";
import { toast } from "sonner";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useUploadFiles } from "@/hooks/use-upload-files";
import { X, Upload } from "lucide-react";
import Image from "next/image";
import { Category } from "@workspace/lib/constants/categories";

function ImagePreview({ imageId, index, onRemove }: { 
  imageId: Id<"_storage">; 
  index: number; 
  onRemove: () => void; 
}) {
  const getImageUrl = useQuery(api.users.getImageUrl, { storageId: imageId });
  
  if (!getImageUrl) {
    return (
      <div className="relative w-24 h-24 bg-muted rounded-md animate-pulse">
        <button
          onClick={onRemove}
          className="absolute -top-2 -right-2 p-1 bg-destructive text-destructive-foreground rounded-full hover:bg-destructive/90"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    );
  }

  return (
    <div className="relative w-24 h-24">
      <Image
        src={getImageUrl}
        alt={`Product image ${index + 1}`}
        fill
        className="object-cover rounded-md"
      />
      <button
        onClick={onRemove}
        className="absolute -top-2 -right-2 p-1 bg-destructive text-destructive-foreground rounded-full hover:bg-destructive/90"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  );
}

interface EditProductModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  product: {
    _id: Id<"products">;
    name: string;
    description?: string;
    price: number;
    category?: string;
    subcategory?: string;
    inventory: number;
    acceptOffers: boolean;
    reserveForLive: boolean;
    status: string;
    images?: Id<"_storage">[];
  } | null;
}

export function EditProductModal({ open, onOpenChange, product }: EditProductModalProps) {
  const [name, setName] = useState(product?.name ?? "");
  const [description, setDescription] = useState(product?.description ?? "");
  const [price, setPrice] = useState(product?.price ?? 0);
  const [category, setCategory] = useState(product?.category ?? "");
  const [subcategory, setSubcategory] = useState(product?.subcategory ?? "");
  const [inventory, setInventory] = useState(product?.inventory ?? 0);
  const [acceptOffers, setAcceptOffers] = useState(product?.acceptOffers ?? false);
  const [reserveForLive, setReserveForLive] = useState(product?.reserveForLive ?? false);
  const [status, setStatus] = useState(product?.status ?? "draft");
  const [images, setImages] = useState<Id<"_storage">[]>(product?.images ?? []);

  const { uploadFiles, isUploading } = useUploadFiles();
  const updateProduct = useMutation(api.sellers.updateProduct);

  useEffect(() => {
    if (product) {
      setName(product.name);
      setDescription(product.description ?? "");
      setPrice(product.price);
      setCategory(product.category ?? "");
      setSubcategory(product.subcategory ?? "");
      setInventory(product.inventory);
      setAcceptOffers(product.acceptOffers);
      setReserveForLive(product.reserveForLive);
      setStatus(product.status);
      setImages(product.images ?? []);
    }
  }, [product]);

  const handleSubmit = async () => {
    if (!product) return;

    try {
      await updateProduct({
        productId: product._id,
        name,
        description,
        price,
        category,
        subcategory,
        inventory,
        acceptOffers,
        reserveForLive,
        status: status as "draft" | "archived" | "active",
        images,
      });
      toast.success("Product updated successfully");
      onOpenChange(false);
    } catch (error) {
      console.error("Error updating product:", error);
      toast.error("Failed to update product");
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    try {
      const newStorageIds = await uploadFiles(files);
      setImages([...images, ...newStorageIds]);
      toast.success("Images uploaded successfully");
    } catch (error) {
      console.error("Error uploading images:", error);
      toast.error("Failed to upload images");
    }
  };

  const handleRemoveImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  const selectedCategory = categories.find((c: Category) => c.id === category);
  const categorySubcategories = selectedCategory ? subcategories[selectedCategory.id] : [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Product</DialogTitle>
          <DialogDescription>
            Make changes to your product here. Click save when you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="images">Product Images</Label>
            <div className="flex flex-wrap gap-4">
              {images.map((imageId, index) => (
                <ImagePreview 
                  key={imageId} 
                  imageId={imageId} 
                  index={index} 
                  onRemove={() => handleRemoveImage(index)} 
                />
              ))}
              <label
                htmlFor="image-upload"
                className="flex items-center justify-center w-24 h-24 border-2 border-dashed border-muted-foreground rounded-md cursor-pointer hover:border-primary"
              >
                <div className="flex flex-col items-center gap-1">
                  <Upload className="w-6 h-6" />
                  <span className="text-xs">Upload</span>
                </div>
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  multiple
                  className="hidden"
                  onChange={handleImageUpload}
                  disabled={isUploading}
                />
              </label>
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Product name"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Product description"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="price">Price (in cents)</Label>
            <Input
              id="price"
              type="number"
              value={price}
              onChange={(e) => setPrice(Number(e.target.value))}
              placeholder="1000"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="category">Category</Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category: Category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedCategory && (
            <div className="grid gap-2">
              <Label htmlFor="subcategory">Subcategory</Label>
              <Select value={subcategory} onValueChange={setSubcategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a subcategory" />
                </SelectTrigger>
                <SelectContent> 
                  {categorySubcategories?.map((sub: Subcategory) => (
                    <SelectItem key={sub.id} value={sub.id}>
                      {sub.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="grid gap-2">
            <Label htmlFor="inventory">Inventory</Label>
            <Input
              id="inventory"
              type="number"
              value={inventory}
              onChange={(e) => setInventory(Number(e.target.value))}
              placeholder="0"
            />
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="acceptOffers"
                checked={acceptOffers}
                onCheckedChange={setAcceptOffers}
              />
              <Label htmlFor="acceptOffers">Accept Offers</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="reserveForLive"
                checked={reserveForLive}
                onCheckedChange={setReserveForLive}
              />
              <Label htmlFor="reserveForLive">Reserve for Live</Label>
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="status">Status</Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select a status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isUploading}>
            {isUploading ? "Uploading..." : "Save changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 