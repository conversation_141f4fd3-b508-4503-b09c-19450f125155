"use client";

import { Card } from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function StreamsPage() {
  const router = useRouter()
  const streamsData = useQuery(api.streams.listByUser, {});

  const streams = Array.isArray(streamsData)
    ? streamsData
    : streamsData?.page ?? [];

  const grouped = {
    live: [] as typeof streams,
    scheduled: [] as typeof streams,
    ended: [] as typeof streams,
  };

  for (const stream of streams) {
    if (stream.status === "live") grouped.live.push(stream);
    else if (stream.status === "scheduled") grouped.scheduled.push(stream);
    else if (stream.status === "ended") grouped.ended.push(stream);
  }

  const handleOpenStream = (streamId: string) => {
    window.open(`/stream/${streamId}`, '_blank');
  };

  const handleCopyLink = (streamId: string) => {
    navigator.clipboard.writeText(`https://liveciety.com/stream/${streamId}`);
  };

  return (
    <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
      <h1 className="text-2xl font-semibold mb-2">Your Shows</h1>
      <div className="space-y-8">
        {(["live", "scheduled", "ended"] as const).map((status) => (
          <div key={status}>
            <h2 className="text-xl font-bold mb-4 capitalize">{status} shows</h2>
            {grouped[status].length === 0 ? (
              <p className="text-zinc-400 dark:text-zinc-600 text-sm">No {status} shows.</p>
            ) : (
              grouped[status].map((stream) => (
                <Card key={stream._id} className="p-4 dark:!bg-muted/50 dark:hover:!bg-muted/70 transition-all duration-200 mb-4">
                  <div className="flex flex-col gap-2">
                    <div className="flex justify-between items-center w-full">
                      <div className="flex items-center gap-2">
                        <span className={`text-xs font-semibold px-2 py-0.5 rounded-full
                          ${stream.status === "live" ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                            : stream.status === "scheduled" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                            : stream.status === "ended" ? "bg-blue-200 text-blue-700 dark:bg-blue-800 dark:text-blue-300"
                            : "bg-blue-100 text-blue-500 dark:bg-blue-800 dark:text-blue-400"}`}>
                          {(stream.status ?? "Unknown").charAt(0).toUpperCase() + (stream.status ?? "Unknown").slice(1)}
                        </span>
                        <span className="font-semibold">{stream.title}</span>
                      </div>
                      {stream.scheduledStartTime && (
                        <span className="text-sm text-gray-500 ml-4 whitespace-nowrap">
                          {new Date(stream.scheduledStartTime).toLocaleString()}
                        </span>
                      )}
                    </div>
                    <div className="flex gap-2 justify-end">
                      <Button variant="outline" size="sm" onClick={() => handleOpenStream(stream._id)}>Open stream</Button>
                      <Button variant="outline" size="sm" onClick={() => handleCopyLink(stream._id)}>Copy link</Button>
                      <Link href={`/streams/${stream._id}/items`} target="_blank">
                        <Button variant="outline" size="sm">View and Clone Stream</Button>
                      </Link>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
