"use client";

import { Card } from "@workspace/ui/components/card";
import { But<PERSON> } from "@workspace/ui/components/button";
import { IconBrandStripe, IconDotsVertical } from "@tabler/icons-react";
import { toast } from "sonner";
import { usePreloadedQuery } from "convex/react";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { User } from "@workspace/backend/convex/lib/types";
import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@workspace/ui/components/dropdown-menu";
import { ConfirmDialog } from "@workspace/ui/components/confirm-dialog";

type StripeAccountStatus = {
  exists: boolean;
  details_submitted?: boolean;
  requirements?: {
    currently_due?: string[];
  };
  charges_enabled?: boolean;
  payouts_enabled?: boolean;
  email?: string | null;
  type?: string;
  status?: Record<string, unknown>;
  error?: string;
};

export default function PayoutsPage() {
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser) as unknown as User;
  const createConnectAccountLink = useAction(api.integration.stripe.createConnectAccountLink);
  const getConnectAccountStatus = useAction(api.integration.stripe.getConnectAccountStatus);
  const disconnectStripeAccount = useAction(api.integration.stripe.disconnectStripeAccount);
  const [isStripeLoading, setIsStripeLoading] = useState(false);
  const [isStatusLoading, setIsStatusLoading] = useState(false);
  const [stripeStatus, setStripeStatus] = useState<StripeAccountStatus | null>(null);
  const [statusError, setStatusError] = useState<string | null>(null);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  
  useEffect(() => {
    async function fetchStatus() {
      if (!user?.stripeAccountId) return;
      setIsStatusLoading(true);
      setStatusError(null);
      try {
        const status = await getConnectAccountStatus({});
        if (status.error) {
          setStatusError(status.error);
          return;
        }
        const normalizedStatus = {
          ...status,
          requirements: status.requirements
            ? {
                ...status.requirements,
                currently_due: status.requirements.currently_due ?? undefined,
              }
            : undefined,
          status: status.status as Record<string, unknown> | undefined,
        };
        setStripeStatus(normalizedStatus);
      } catch (error: any) {
        setStatusError(error?.message || "Could not fetch Stripe status");
      } finally {
        setIsStatusLoading(false);
      }
    }
    fetchStatus();
  }, [user?.stripeAccountId]);

  return (
    <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
      <h1 className="text-2xl font-semibold mb-2">Payouts</h1>
      <p className="text-gray-400 mb-6 text-md">Manage your payout settings and connect your Stripe account to receive payments.</p>
      <div className="space-y-8">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex flex-col gap-1">
              <span className="text-md font-semibold flex items-center gap-2">
                <IconBrandStripe className="w-5 h-5 text-[#635bff]" />
                Stripe Payouts
              </span>
              <span className="text-xs text-muted-foreground">
                Connect your Stripe account to receive payouts for sales.
              </span>
            </div>
            {user?.stripeAccountId ? (
              isStatusLoading ? (
                <span className="text-xs text-muted-foreground">Checking status...</span>
              ) : statusError ? (
                <div className="flex flex-col items-end gap-2">
                  <span className="text-xs text-red-500">{statusError}</span>
                  <Button
                    onClick={async () => {
                      setIsStripeLoading(true);
                      try {
                        const res = await createConnectAccountLink({});
                        if (res?.url) {
                          window.open(res.url, "_blank");
                        } else {
                          toast.error("Could not get Stripe onboarding link.");
                        }
                      } catch (e: unknown) {
                        console.error(e);
                        toast.error("Could not connect to Stripe.");
                      } finally {
                        setIsStripeLoading(false);
                      }
                    }}
                    disabled={isStripeLoading}
                    className="bg-[#635bff] hover:bg-[#7a6fff] text-white font-bold px-4 py-2 rounded"
                  >
                    {isStripeLoading ? "Connecting..." : "Reconnect Stripe"}
                  </Button>
                </div>
              ) : stripeStatus && stripeStatus.exists && (!stripeStatus.details_submitted || (stripeStatus.requirements && stripeStatus.requirements.currently_due && stripeStatus.requirements.currently_due.length > 0)) ? (
                <Button
                  onClick={async () => {
                    setIsStripeLoading(true);
                    try {
                      const res = await createConnectAccountLink({});
                      if (res?.url) {
                        window.open(res.url, "_blank");
                      } else {
                        toast.error("Could not get Stripe onboarding link.");
                      }
                    } catch (e: unknown) {
                      console.error(e);
                      toast.error("Could not connect to Stripe.");
                    } finally {
                      setIsStripeLoading(false);
                    }
                  }}
                  disabled={isStripeLoading}
                  className="bg-[#635bff] hover:bg-[#7a6fff] text-white font-bold px-4 py-2 rounded"
                >
                  {isStripeLoading ? "Connecting..." : "Complete your connection"}
                </Button>
              ) : (
                <div className="flex items-center gap-2">
                  <IconBrandStripe className="w-5 h-5 text-[#4cd964]" />
                  <span className="text-green-500 font-medium">Connected</span>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="ml-2 px-2 py-1 text-xl font-bold"
                        disabled={isStripeLoading || isDisconnecting}
                        aria-label="More actions"
                      >
                        <IconDotsVertical />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onSelect={async (e) => {
                          e.preventDefault();
                          setIsStripeLoading(true);
                          try {
                            const res = await createConnectAccountLink({});
                            if (res?.url) {
                              window.open(res.url, "_blank");
                            } else {
                              toast.error("Could not get Stripe onboarding link.");
                            }
                          } catch (e: unknown) {
                            console.error(e);
                            toast.error("Could not connect to Stripe.");
                          } finally {
                            setIsStripeLoading(false);
                          }
                        }}
                        disabled={isStripeLoading || isDisconnecting}
                      >
                        {isStripeLoading ? "Loading..." : "Edit"}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={async (e) => {
                          e.preventDefault();
                          setShowDisconnectDialog(true);
                        }}
                        disabled={isDisconnecting || isStripeLoading}
                        variant="destructive"
                      >
                        {isDisconnecting ? "Disconnecting..." : "Disconnect"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )
            ) : (
              <Button
                onClick={async () => {
                  setIsStripeLoading(true);
                  try {
                    const res = await createConnectAccountLink({});
                    if (res?.url) {
                      window.open(res.url, "_blank");
                    } else {
                      toast.error("Could not get Stripe onboarding link.");
                    }
                  } catch (e: unknown) {
                    console.error(e);
                    toast.error("Could not connect to Stripe.");
                  } finally {
                    setIsStripeLoading(false);
                  }
                }}
                disabled={isStripeLoading}
                className="bg-[#635bff] hover:bg-[#7a6fff] text-white font-bold px-4 py-2 rounded"
              >
                {isStripeLoading ? "Connecting..." : "Connect Stripe"}
              </Button>
            )}
          </div>
        </Card>
      </div>
      <ConfirmDialog
        open={showDisconnectDialog}
        onOpenChange={setShowDisconnectDialog}
        title="Disconnect Stripe Account"
        description="Are you sure you want to disconnect your Stripe account? This will prevent you from receiving payouts until you reconnect."
        confirmLabel="Disconnect"
        cancelLabel="Cancel"
        confirmVariant="destructive"
        loading={isDisconnecting}
        onConfirm={async () => {
          setIsDisconnecting(true);
          try {
            await disconnectStripeAccount({});
            toast.success("Disconnected from Stripe.");
            setStripeStatus(null);
            setShowDisconnectDialog(false);
          } catch (e: unknown) {
            console.error(e);
            toast.error("Could not disconnect from Stripe.");
          } finally {
            setIsDisconnecting(false);
          }
        }}
      />
    </div>
  )
}