'use client';

import React, { useState, useEffect } from "react";
import { UploadButton } from "@/components/upload-button";
import { api } from "@workspace/backend/convex/_generated/api";
import { useAction, useMutation, usePreloadedQuery } from "convex/react";
import { IconInfoCircle, IconEye, IconEyeOff, IconTrash, IconDeviceFloppy, IconPhotoUp } from "@tabler/icons-react";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Button } from "@workspace/ui/components/button";
import { Separator } from "@workspace/ui/components/separator";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@workspace/ui/components/accordion"
import {
  Alert,
  AlertTitle,
} from "@workspace/ui/components/alert"
import { toast } from "sonner";
import { Card } from "@workspace/ui/components/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@workspace/ui/components/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@workspace/ui/components/alert-dialog";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { getAvatarImageUrl } from "@/lib/utils";
import { UserAvatar } from "@/components/user-avatar";
import Image from "next/image";
import { useProfileImages } from "@/hooks/use-profile-images";

const formSchema = z.object({
  firstName: z.string().min(2, {
    message: "First name must be at least 2 characters.",
  }).max(50),
  lastName: z.string().min(2, {
    message: "Last name must be at least 2 characters.",
  }).max(50),
  email: z.string().email(),
  bio: z.string().max(500, {
    message: "Bio must be less than 500 characters."
  }).optional(),
  image: z.string().optional(),
  coverImage: z.string().optional(),
  subscribeToUpdates: z.boolean(),
});

type FormValues = z.infer<typeof formSchema>;

const passwordFormSchema = z.object({
  oldPassword: z.string().optional(),
  newPassword: z.string().min(8, {
    message: "Password must be at least 8 characters.",
  }),
  confirmPassword: z.string()
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type PasswordFormValues = z.infer<typeof passwordFormSchema>;

interface Account {
  provider: string;
}

interface UserType {
  _id: Id<"users">;
  firstName?: string;
  lastName?: string;
  email?: string;
  bio?: string;
  image?: string;
  coverImage?: string;
  hashedPassword?: string;
  subscribeToUpdates?: boolean;
  accounts?: Account[];
  lastLoginType?: string;
}

interface UploadResponse {
  storageId: string;
}

const AccountSettingsPageContent = () => {
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser) as unknown as UserType;

  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const deleteImage = useAction(api.files.deleteById);
  const update = useMutation(api.users.update);
  const updatePassword = useMutation(api.users.updatePassword);
  const updateEmail = useMutation(api.users.updateEmail);
  
  // Local state for form values
  const [image, setImage] = useState<string>("");
  const [coverImage, setCoverImage] = useState<Id<"_storage"> | string | undefined>(undefined);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [isSubmittingPassword, setIsSubmittingPassword] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [showGoogleWarning, setShowGoogleWarning] = useState(false);
  const [newEmail, setNewEmail] = useState("");
  const [hasChanges, setHasChanges] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use our improved profile images hook
  const { 
    uploadProfileImage, 
    uploadCoverImage, 
    removeProfileImage, 
    removeCoverImage,
    isProfileImageLoading,
    isCoverImageLoading
  } = useProfileImages();

  useEffect(() => {
    if (user) {
      setImage(user.image || "");
      if (user.coverImage) {
        setCoverImage(user.coverImage);
      } else {
        setCoverImage(undefined);
      }
    }
  }, [user]);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
      bio: user?.bio || "",
      image: "",
      coverImage: "",
      subscribeToUpdates: user?.subscribeToUpdates || false,
    }
  });

  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    const subscription = form.watch((value) => {
      const isDifferent = 
        value.firstName !== user?.firstName || 
        value.lastName !== user?.lastName || 
        value.bio !== user?.bio ||
        image !== user?.image ||
        coverImage !== user?.coverImage;
      
      setHasChanges(isDifferent);
    });
    
    return () => subscription.unsubscribe();
  }, [form, user, image, coverImage]);

  async function onSubmit(values: FormValues): Promise<void> {
    if (!hasChanges) return;
    
    setIsSubmitting(true);
    try {
      const updateData: any = {
        id: user?._id,
        firstName: values.firstName,
        lastName: values.lastName,
        bio: values.bio,
      };
      
      if (image) {
        updateData.image = image;
      }
      
      if (coverImage) {
        updateData.coverImage = coverImage;
      }
      
      await update(updateData);
      
      setHasChanges(false);
      toast.success("Profile updated successfully");
    } catch (error) {
      console.error("Update error:", error);
      toast.error("Failed to update profile");
    } finally {
      setIsSubmitting(false);
    }
  }

  async function handlePasswordSubmit(e: React.FormEvent<HTMLFormElement>): Promise<void> {
    e.preventDefault();
    if (isSubmittingPassword) return;

    try {
      setIsSubmittingPassword(true);
      const values = passwordForm.getValues();
      
      if (passwordForm.formState.errors.newPassword || passwordForm.formState.errors.confirmPassword) {
        throw new Error("Please fix the form errors before submitting");
      }

      if (values.newPassword !== values.confirmPassword) {
        throw new Error("Passwords don't match");
      }

      await updatePassword({
        oldPassword: user?.hashedPassword ? values.oldPassword : undefined,
        newPassword: values.newPassword,
      });
      
      toast.success("Password updated successfully");
      passwordForm.reset();
      setShowPassword(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update password");
    } finally {
      setIsSubmittingPassword(false);
    }
  }

  const handleEmailChange = async (): Promise<void> => {
    try {
      const hasGoogleAuth = user?.accounts?.some((account: Account) => account.provider === "google");

      if (hasGoogleAuth) {
        setShowGoogleWarning(true);
        return;
      }

      await proceedWithEmailChange();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update email");
    }
  };

  const proceedWithEmailChange = async () => {
    try {
      await updateEmail({
        newEmail,
      });
      toast.success("Email updated successfully");
      setShowEmailDialog(false);
      setNewEmail("");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update email");
    }
  };

  const handleCoverImageUpload = async (uploaded: any) => {
    try {
      if (!user?._id) {
        throw new Error("User not found");
      }
      
      const uploadedImage = (uploaded[0]?.response as UploadResponse).storageId as Id<"_storage">;
      
      const result = await uploadCoverImage(uploadedImage, user._id);
      
      if (result) {
        setCoverImage(result);
        setHasChanges(true);
      }
    } catch (error) {
      console.error("Error processing upload:", error);
      toast.error("Failed to process cover image upload");
    }
  };

  const handleProfileImageUpload = async (uploaded: any) => {
    try {
      if (!user?._id) {
        throw new Error("User not found");
      }
      
      const uploadedImage = (uploaded[0]?.response as UploadResponse).storageId as Id<"_storage">;
      
      const result = await uploadProfileImage(uploadedImage, user._id);
      
      if (result) {
        setImage(result);
        setHasChanges(true);
      }
    } catch (error) {
      console.error("Error processing upload:", error);
      toast.error("Failed to process profile picture upload");
    }
  };

  const handleCoverImageRemove = async () => {
    try {
      if (!user?._id || !coverImage) {
        return;
      }
      
      const success = await removeCoverImage(user._id);
      
      if (success) {
        setCoverImage(undefined);
        setHasChanges(true);
      }
    } catch (error) {
      console.error("Error removing cover image:", error);
      toast.error("Failed to remove cover image");
    }
  };

  const handleProfileImageRemove = async () => {
    try {
      if (!user?._id) {
        return;
      }
      
      const success = await removeProfileImage(user._id);
      
      if (success) {
        setImage("");
        setHasChanges(true);
      }
    } catch (error) {
      console.error("Error removing profile image:", error);
      toast.error("Failed to remove profile image");
    }
  };

  return (
      <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
        <h1 className="text-2xl font-semibold mb-2">Profile</h1>
        <p className="text-gray-400 mb-6 text-md">Manage your personal details</p>

        <div className="space-y-8">
          <Alert className="bg-muted p-2 mb-8 border border-zinc-200 dark:border-zinc-700 flex" variant="default">
            <AlertTitle className="flex items-center gap-2">
              <IconInfoCircle className="w-4 h-4" /> 
              Make your changes and click save when you&apos;re done.
            </AlertTitle>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Cover Image */}
              <div className="mb-8">
                <FormField
                  control={form.control}
                  name="coverImage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cover Image</FormLabel>
                      <FormControl>
                        <div className="flex flex-col">
                          <div className="relative w-full h-48 rounded-2xl bg-muted mb-3 overflow-hidden border border-zinc-200 dark:border-zinc-800">
                            {coverImage ? (
                              <Image 
                                src={typeof coverImage === 'string' && coverImage.includes('http') 
                                  ? coverImage 
                                  : getAvatarImageUrl(coverImage as Id<"_storage">) as string}
                                alt="Cover" 
                                height={192}
                                width={768}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  console.error("Image failed to load:", coverImage);
                                  e.currentTarget.src = "";
                                  e.currentTarget.classList.add("bg-gray-100", "dark:bg-gray-800");
                                }}
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800">
                                <IconPhotoUp className="h-12 w-12 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div className="flex items-center">
                            <UploadButton
                              {...field}
                              generateUploadUrl={generateUploadUrl}
                              onUploadComplete={handleCoverImageUpload}
                              onUploadError={(error) => {
                                toast.error(`Upload error: ${error}`);
                              }}
                              onRemove={handleCoverImageRemove}
                              isUploading={isCoverImageLoading}
                            />
                            <p className="text-xs ml-2 opacity-50">
                              Recommended size: 1500 x 500px
                            </p>
                          </div>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              {/* Profile Image */}
              <div className="mb-12">
                <FormField
                  control={form.control}
                  name="image"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Profile Picture</FormLabel>
                      <FormControl>
                        <div className="grid grid-cols-1 w-full">
                          <div className="flex items-center">
                            <div className="mr-4 flex items-center">
                              <UserAvatar
                                user={{
                                  ...user,
                                  image: image || undefined
                                } as UserType}
                                size="lg"
                              />
                            </div>
                            <div className="flex flex-col">
                              <UploadButton
                                {...field}
                                generateUploadUrl={generateUploadUrl}
                                onUploadComplete={handleProfileImageUpload}
                                onUploadError={(error) => {
                                  toast.error(`Upload error: ${error}`);
                                }}
                                onRemove={handleProfileImageRemove}
                                isUploading={isProfileImageLoading}
                              />
                              <p className="text-xs mt-2 opacity-50">
                                We support PNGs, JPEGs, and GIFs under 10MB
                              </p>
                            </div>
                          </div>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="First Name"
                            {...field}
                            onChange={(event) => {
                              const value = event.target.value;
                              field.onChange(value);
                              form.setValue("firstName", value);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Last Name"
                            {...field}
                            onChange={(event) => {
                              const value = event.target.value;
                              field.onChange(value);
                              form.setValue("lastName", value);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="bio"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bio</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Tell us about yourself"
                          className="resize-y"
                          {...field}
                          onChange={(event) => {
                            const value = event.target.value;
                            field.onChange(value);
                            form.setValue("bio", value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="cursor-not-allowed">
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            disabled
                            placeholder="Email"
                            {...field}
                            value={user?.email || field.value}
                          />
                          {user?.hashedPassword && (
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setShowEmailDialog(true)}
                              className="absolute right-3 top-1/2 -translate-y-1/2 text-xs border border-input px-1 h-5 rounded-sm"
                            >
                              edit
                            </Button>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>

          <div>
            <Accordion type="single" collapsible>
              <AccordionItem value="password">
                <AccordionTrigger>
                  {user?.hashedPassword ? "Change Password" : "Set Password"}
                </AccordionTrigger>
                <AccordionContent>
                  <Form {...passwordForm}>
                    <form onSubmit={handlePasswordSubmit} className="space-y-4">
                      <input 
                        type="text"
                        name="username"
                        autoComplete="username"
                        defaultValue={user?.email}
                        className="hidden"
                      />
                      {user?.hashedPassword && (
                        <FormField
                          control={passwordForm.control}
                          name="oldPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Current Password</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Input
                                    type={showPassword ? "text" : "password"}
                                    placeholder="Current Password"
                                    autoComplete="current-password"
                                    {...field}
                                  />
                                  <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute right-3 top-1/2 -translate-y-1/2"
                                  >
                                    {showPassword ? (
                                      <IconEyeOff className="h-4 w-4 text-gray-500" />
                                    ) : (
                                      <IconEye className="h-4 w-4 text-gray-500" />
                                    )}
                                  </button>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                      
                      <FormField
                        control={passwordForm.control}
                        name="newPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{user?.hashedPassword ? "New Password" : "Password"}</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input
                                  type={showPassword ? "text" : "password"}
                                  placeholder={user?.hashedPassword ? "New Password" : "Password"}
                                  autoComplete="new-password"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  onClick={() => setShowPassword(!showPassword)}
                                  className="absolute right-3 top-1/2 -translate-y-1/2"
                                >
                                  {showPassword ? (
                                    <IconEyeOff className="h-4 w-4 text-gray-500" />
                                  ) : (
                                    <IconEye className="h-4 w-4 text-gray-500" />
                                  )}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={passwordForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Confirm Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input
                                  type={showPassword ? "text" : "password"}
                                  placeholder="Confirm Password"
                                  autoComplete="new-password"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  onClick={() => setShowPassword(!showPassword)}
                                  className="absolute right-3 top-1/2 -translate-y-1/2"
                                >
                                  {showPassword ? (
                                    <IconEyeOff className="h-4 w-4 text-gray-500" />
                                  ) : (
                                    <IconEye className="h-4 w-4 text-gray-500" />
                                  )}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button 
                        type="submit"
                        variant="outline" 
                        disabled={isSubmittingPassword}
                        className="w-full text-accent-foreground dark:bg-white dark:text-black"
                      >
                        {user?.hashedPassword ? "Update Password" : "Set Password"}
                      </Button>
                    </form>
                  </Form>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>

          <Separator className="my-6" />

          <Card className="border border-red-600 p-4">
            <div className="flex items-center justify-between">
              <div className="flex flex-col justify-start gap2">
                <span className="text-sm">Delete Account</span>
                <p className="text-xs text-gray-400">
                  Once deleted, your account cannot be recovered.
                </p>
              </div>

              <Button variant="destructive" className="h-10">
                <IconTrash />
                Delete Account
              </Button>
            </div>
          </Card>
        </div>

        <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Change email address</DialogTitle>
              <DialogDescription>
                Enter your new email address below. You will need to verify this email before the change takes effect.
              </DialogDescription>
            </DialogHeader>
            <DialogTitle></DialogTitle>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium leading-none">
                  New Email Address
                </label>
                <Input
                  id="email"
                  placeholder="Enter new email"
                  value={newEmail}
                  onChange={(e) => setNewEmail(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowEmailDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleEmailChange}>
                Change email address
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <AlertDialog open={showGoogleWarning} onOpenChange={setShowGoogleWarning}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Warning: Google Account Linked</AlertDialogTitle>
              <AlertDialogDescription>
                Your account is currently linked to Google. Changing your email will unlink your Google account.
                Are you sure you want to proceed?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={proceedWithEmailChange}>
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Floating Save Button */}
        {hasChanges && (
          <div className="fixed bottom-6 right-6 z-50">
            <Button 
              onClick={form.handleSubmit(onSubmit)}
              className="shadow-lg"
              variant="default"
              disabled={isSubmitting}
            >
              <IconDeviceFloppy />
              Save Account
            </Button>
          </div>
        )}
      </div>
  );
};

export default AccountSettingsPageContent;