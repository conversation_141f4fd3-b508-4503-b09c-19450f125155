"use client";

import React from "react";
import { ThemeSelector } from "@/components/theme-selector";
import { useTheme } from "next-themes";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";

type Theme = "light" | "dark" | "system";

const AppearanceSettingsPageContent = () => {
  const { setTheme, theme } = useTheme() as {
    setTheme: (theme: Theme) => void;
    theme: Theme;
  };
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
      <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
        <h1 className="text-2xl font-semibold mb-2">Appearance</h1>
        <p className="text-gray-400 mb-6 text-md">
          Customize the look and feel of your platform
        </p>

        <div className="space-y-6 mt-12">
          <div className="flex flex-col gap-4">
            <div>
              <h2 className="text-md font-semibold">Theme</h2>
              <p className="text-muted-foreground text-sm">
                Select a theme to personalize your platform&apos;s appearance
              </p>
            </div>

            <RadioGroup
              value={theme}
              onValueChange={(value: Theme) => setTheme(value)}
              className="grid grid-cols-1 gap-4 sm:grid-cols-3"
            >
              {/* Light Theme Option */}
              <label htmlFor="light" className="cursor-pointer">
                <div
                  className={`relative flex flex-col gap-2 rounded-lg border pt-1 pl-1 pr-1 shadow transition-colors hover:bg-accent ${theme === "light" ? "border-primary" : ""}`}
                >
                  <div className="flex h-[140px] items-center justify-center rounded-md border bg-[#fff]">
                    <div className="w-32 rounded border bg-white p-2">
                      <div className="space-y-2">
                        <div className="h-2 w-[80px] rounded bg-[#e1e7ef]" />
                        <div className="h-2 w-[100px] rounded bg-[#e1e7ef]" />
                        <div className="h-2 w-[70px] rounded bg-[#e1e7ef]" />
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between px-2 pb-1">
                    <div className="flex items-center gap-2">
                      <RadioGroupItem
                        value="light"
                        id="light"
                        className="hidden"
                      />
                      <span className="text-sm font-medium">Light</span>
                    </div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      className="h-4 w-4"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </label>

              {/* Dark Theme Option */}
              <label htmlFor="dark" className="cursor-pointer">
                <div
                  className={`relative flex flex-col gap-2 rounded-lg border pt-1 pl-1 pr-1 shadow transition-colors hover:bg-accent ${theme === "dark" ? "border-primary" : ""}`}
                >
                  <div className="flex h-[140px] items-center justify-center rounded-md border bg-[#1c1c1c]">
                    <div className="w-32 rounded border border-[#2a2a2a] bg-[#1c1c1c] p-2">
                      <div className="space-y-2">
                        <div className="h-2 w-[80px] rounded bg-[#2a2a2a]" />
                        <div className="h-2 w-[100px] rounded bg-[#2a2a2a]" />
                        <div className="h-2 w-[70px] rounded bg-[#2a2a2a]" />
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between px-2 pb-1">
                    <div className="flex items-center gap-2">
                      <RadioGroupItem
                        value="dark"
                        id="dark"
                        className="hidden"
                      />
                      <span className="text-sm font-medium">Dark</span>
                    </div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      className="h-4 w-4"
                    >
                      <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                    </svg>
                  </div>
                </div>
              </label>

              {/* System Theme Option */}
              <label htmlFor="system" className="cursor-pointer">
                <div
                  className={`relative flex flex-col gap-2 rounded-lg border pt-1 pl-1 pr-1 shadow transition-colors hover:bg-accent ${theme === "system" ? "border-primary" : ""}`}
                >
                  <div className="flex h-[140px] items-center justify-center rounded-md border bg-gradient-to-r from-[#fff] to-[#1c1c1c]">
                    <div className="flex w-32 gap-2">
                      <div className="w-1/2 rounded border bg-white p-2">
                        <div className="space-y-2">
                          <div className="h-2 w-[80%] rounded bg-[#e1e7ef]" />
                          <div className="h-2 w-full rounded bg-[#e1e7ef]" />
                        </div>
                      </div>
                      <div className="w-1/2 rounded border border-[#2a2a2a] bg-[#1c1c1c] p-2">
                        <div className="space-y-2">
                          <div className="h-2 w-[80%] rounded bg-[#2a2a2a]" />
                          <div className="h-2 w-full rounded bg-[#2a2a2a]" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between px-2 pb-1">
                    <div className="flex items-center gap-2">
                      <RadioGroupItem
                        value="system"
                        id="system"
                        className="hidden"
                      />
                      <span className="text-sm font-medium">System</span>
                    </div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      className="h-4 w-4"
                    >
                      <path
                        fillRule="evenodd"
                        d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </label>
            </RadioGroup>
          </div>

          <div className="flex flex-col gap-4 mt-6">
            <div>
              <h2 className="text-md font-semibold">Color Scheme</h2>
              <p className="text-muted-foreground text-sm">
                Choose from our predefined color schemes or create your own
              </p>
            </div>
            <div className="flex items-center gap-4">
              <ThemeSelector />
            </div>
          </div>
        </div>
      </div>
  );
};

export default AppearanceSettingsPageContent;
