"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { Search as SearchIcon, ArrowLeft } from "lucide-react";
import { Input } from "@workspace/ui/components/input";
import { Button } from "@workspace/ui/components/button";
import { ProductResults } from "../_components/product-results";
import Link from "next/link";

export default function ProductSearchPage() {
  const searchParams = useSearchParams();
  const query = searchParams.get("q") || "";
  const [searchQuery, setSearchQuery] = useState(query);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;
    
    const url = new URL(window.location.href);
    url.searchParams.set("q", searchQuery);
    window.history.pushState({}, "", url);
  };

  useEffect(() => {
    setSearchQuery(query);
  }, [query]);

  return (
    <div className="container max-w-screen-xl mx-auto py-6 px-4 md:px-6">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-4">
          <Link href={`/search?q=${encodeURIComponent(query)}`}>
            <Button variant="ghost" size="icon" className="rounded-full">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">
            {query ? `Products matching "${query}"` : "Search Products"}
          </h1>
        </div>
        
        <form onSubmit={handleSubmit} className="flex w-full max-w-xl">
          <div className="relative flex-1">
            <Input
              placeholder="Search products"
              className="pr-10 h-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button 
              type="submit"
              size="icon"
              variant="ghost"
              className="absolute right-0 top-0 h-full aspect-square"
            >
              <SearchIcon className="h-4 w-4" />
            </Button>
          </div>
        </form>
      </div>

      <div className="my-4 pb-4 border-b">
        <div className="text-sm text-muted-foreground">
          {query ? `Showing results for "${query}"` : "Enter a search term"}
        </div>
      </div>

      {query ? (
        <ProductResults query={query} layout="grid" />
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground">Enter a search term to find products</p>
        </div>
      )}
    </div>
  );
} 