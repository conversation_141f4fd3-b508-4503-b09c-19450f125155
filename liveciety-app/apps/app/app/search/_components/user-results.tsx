"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { UserAvatar } from "@/components/user-avatar";
import { User as UserType } from "@/lib/types";
import { Skeleton } from "@workspace/ui/components/skeleton";

interface UserResultsProps {
  users: UserType[];
  layout?: "grid" | "list";
  onLoadMore?: () => void;
  hasMore?: boolean;
  loading?: boolean;
  sortOrder?: "asc" | "desc";
}

export function UserResults({ users, layout = "list", onLoadMore, hasMore, loading, sortOrder = "asc" }: UserResultsProps) {
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!hasMore || !onLoadMore) return;
    const observer = new window.IntersectionObserver((entries) => {
      if (entries[0] && entries[0].isIntersecting && !loading) {
        onLoadMore();
      }
    });
    if (loadMoreRef.current) observer.observe(loadMoreRef.current);
    return () => observer.disconnect();
  }, [hasMore, onLoadMore, loading]);

  if (loading && users.length === 0) {
    if (layout === "grid") {
      return (
        <div className="grid grid-cols-6 gap-4">
          {Array.from({ length: 25 }).map((_, i) => (
            <div key={i} className="flex flex-col items-center p-4 border rounded-xl bg-background">
              <div className="relative mb-3">
                <Skeleton className="h-12 w-12 rounded-full" />
              </div>
              <div className="w-full text-center">
                <Skeleton className="h-4 w-3/4 mx-auto mb-2" />
                <Skeleton className="h-3 w-1/2 mx-auto" />
              </div>
            </div>
          ))}
        </div>
      );
    } else {
      return (
        <div className="space-y-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <Skeleton key={i} className="h-16 w-full rounded-lg" />
          ))}
        </div>
      );
    }
  }

  return (
    <div className={layout === "grid" ? "grid-layout" : "list-layout"}>
      {layout === "grid" ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {users.map((user: UserType) => (
            <UserGridItem key={user._id} user={user} />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {users.map((user: UserType) => (
            <UserListItem key={user?._id} user={user} />
          ))}
        </div>
      )}
      {hasMore && (
        <div className="flex justify-center mt-4" ref={loadMoreRef}>
          {loading && users.length > 0 ? (
            layout === "grid" ? (
              <div className="grid grid-cols-6 gap-4 w-full">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="flex flex-col items-center p-4 border rounded-xl bg-background">
                    <div className="relative mb-3">
                      <Skeleton className="h-12 w-12 rounded-full" />
                    </div>
                    <div className="w-full text-center">
                      <Skeleton className="h-4 w-3/4 mx-auto mb-2" />
                      <Skeleton className="h-3 w-1/2 mx-auto" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col gap-2 w-full">
                {Array.from({ length: 2 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full rounded-lg" />
                ))}
              </div>
            )
          ) : null}
        </div>
      )}
    </div>
  );
}

function UserGridItem({ user }: { user: UserType }) {
  const [, setIsHovered] = useState(false);

  return (
    <Link 
      href={`/user/${user.username}`}
      className="block"
    >
      <div 
        className="flex flex-col items-center p-4 border rounded-xl hover:bg-accent/20 transition-colors relative"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative">
          <UserAvatar
            user={user}
            size="default"
          />
        </div>
        
        <div className="text-center">
          <p className="font-medium truncate w-full">{user.username || ''}</p>
          {user.name && (
            <p className="text-xs text-muted-foreground truncate w-full">{user.name}</p>
          )}
        </div>
      </div>
    </Link>
  );
}

function UserListItem({ user }: { user: UserType }) {
  return (
    <Link 
      href={`/user/${user.username}`}
      className="block"
    >
      <div className="flex items-center gap-3 p-3 border rounded-lg hover:bg-accent/20 transition-colors">
        <UserAvatar
          user={user as UserType}
          size="default"
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-baseline gap-1">
            <p className="text-sm font-medium truncate">{user.username || ''}</p>
            {user.name && (
              <p className="text-xs text-muted-foreground truncate">{user.name}</p>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
} 