"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import { useQuery, useConvex } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useSearchParams, useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>ontent } from "@workspace/ui/components/tabs";
import { cn } from "@workspace/ui/lib/utils";
import { Search as SearchIcon } from "lucide-react";
import { Input } from "@workspace/ui/components/input";
import { Button } from "@workspace/ui/components/button";
import { categories, subcategories } from "@workspace/lib/constants/categories";
import Image from "next/image";

import { UserResults } from "./_components/user-results";
import { ProductResults } from "./_components/product-results";
import { User } from "@/lib/types";

export default function SearchPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const query = searchParams.get("q") || "";
  const [searchQuery, setSearchQuery] = useState(query);
  const [activeTab, setActiveTab] = useState("all");
  const [sortOrder] = useState<"asc" | "desc">("asc");

  const firstPage = useQuery(
    api.users.searchUsers,
    query
      ? { searchQuery: query, paginationOpts: { numItems: 50, cursor: null }, sortOrder }
      : "skip"
  );

  const [users, setUsers] = useState<User[]>([]);
  const [userCursor, setUserCursor] = useState<string | null>(null);
  const [userIsDone, setUserIsDone] = useState(false);
  const [userLoading, setUserLoading] = useState(false);
  const initialFetch = useRef(false);

  const convex = useConvex();

  const [showAllUsers] = useState(false);
  const [showAllCategories, setShowAllCategories] = useState(false);
  const [showAllSubcategories, setShowAllSubcategories] = useState(false);
  const [showAllProducts] = useState(false);

  useEffect(() => {
    if (firstPage && firstPage.users) {
      setUsers(uniqueById(firstPage.users));
      setUserCursor(firstPage.continueCursor || null);
      setUserIsDone(firstPage.isDone);
    } else if (!query) {
      setUsers([]);
      setUserCursor(null);
    }
  }, [firstPage]);

  const fetchMoreUsers = useCallback(async () => {
    if (userLoading || userIsDone || !userCursor) return;
    setUserLoading(true);
    try {
      const res = await convex.query(
        api.users.searchUsers,
        { searchQuery: query, paginationOpts: { numItems: 50, cursor: userCursor } }
      );
      if (res && res.users) {
        setUsers((prev) => uniqueById([...prev, ...res.users]));
        setUserCursor(res.continueCursor || null);
        setUserIsDone(res.isDone);
      }
    } finally {
      setUserLoading(false);
    }
  }, [convex, query, userCursor, userIsDone, userLoading]);

  useEffect(() => {
    setUsers([]);
    setUserCursor(null);
    setUserIsDone(false);
    initialFetch.current = true;
  }, [query]);

  const usersForAllTab = showAllUsers ? users : users.slice(0, 3);
  const uniqueUsersForAllTab = uniqueById(usersForAllTab);
  const hasUserResults = users.length > 0;

  const filteredCategories = query
    ? categories.filter((cat) =>
        cat.title.toLowerCase().includes(query.toLowerCase()) ||
        cat.description.toLowerCase().includes(query.toLowerCase())
      )
    : [];

  const filteredSubcategories = query
    ? Object.entries(subcategories)
        .flatMap(([catId, subs]) =>
          subs.map((sub) => ({ ...sub, categoryId: catId }))
        )
        .filter((sub) =>
          sub.title.toLowerCase().includes(query.toLowerCase())
        )
    : [];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;
    
    const url = new URL(window.location.href);
    url.searchParams.set("q", searchQuery);
    window.history.pushState({}, "", url);
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    
    if (value === "users") {
      router.push(`/search/users?q=${encodeURIComponent(query)}`);
    } else if (value === "products") {
      router.push(`/search/products?q=${encodeURIComponent(query)}`);
    }
  };

  useEffect(() => {
    setSearchQuery(query);
  }, [query]);

  function renderCategoryResults() {
    if (!filteredCategories.length) return null;
    const categoriesToShow = showAllCategories ? filteredCategories : filteredCategories.slice(0, 3);
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Categories</h2>
          {filteredCategories.length > 3 && (
            <Button size="sm" variant="ghost" onClick={() => setShowAllCategories((v) => !v)}>
              {showAllCategories ? "Show less" : "Show more"}
            </Button>
          )}
        </div>
        <div className="space-y-2">
          {categoriesToShow.map((cat) => (
            <div
              key={cat.id}
              className="flex items-center gap-3 px-2 py-2 hover:bg-accent/50 rounded-lg cursor-pointer"
              onClick={() => router.push(`/browse/${cat.id}`)}
            >
              <Image
                src={cat.image?.src?.startsWith('/') ? cat.image.src : `/images/${cat.image?.src}`}
                alt={cat.title}
                width={32}
                height={32}
                className="h-8 w-8 rounded object-cover"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{cat.title}</p>
                <p className="text-xs text-muted-foreground truncate">{cat.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  function renderSubcategoryResults() {
    if (!filteredSubcategories.length) return null;
    const subcategoriesToShow = showAllSubcategories ? filteredSubcategories : filteredSubcategories.slice(0, 3);
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Subcategories</h2>
          {filteredSubcategories.length > 3 && (
            <Button size="sm" variant="ghost" onClick={() => setShowAllSubcategories((v) => !v)}>
              {showAllSubcategories ? "Show less" : "Show more"}
            </Button>
          )}
        </div>
        <div className="space-y-2">
          {subcategoriesToShow.map((sub) => (
            <div
              key={sub.id + "-" + sub.categoryId}
              className="flex items-center gap-3 px-2 py-2 hover:bg-accent/50 rounded-lg cursor-pointer"
              onClick={() => router.push(`/browse/${sub.categoryId}?subcategory=${sub.id}`)}
            >
              <div className="h-8 w-8 rounded bg-muted flex items-center justify-center text-xs font-bold">
                {sub.title[0]}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{sub.title}</p>
                <p className="text-xs text-muted-foreground truncate">Subcategory</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-screen-xl mx-auto py-6 px-4 md:px-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">
          {query ? `Results for "${query}"` : "Search"}
        </h1>
        <form onSubmit={handleSubmit} className="flex w-full max-w-xl">
          <div className="relative flex-1">
            <Input
              placeholder="Search Liveciety"
              className="pr-10 h-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button 
              type="submit"
              size="icon"
              variant="ghost"
              className="absolute right-0 top-0 h-full aspect-square"
            >
              <SearchIcon className="h-4 w-4" />
            </Button>
          </div>
        </form>
      </div>

      <Tabs 
        defaultValue="all" 
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <div className="border-b mb-6">
          <TabsList className="bg-transparent p-0 h-auto justify-start items-center gap-4">
            <TabsTrigger
              value="all"
              className={cn(
                "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                "data-[state=active]:text-foreground data-[state=active]:border-b-2",
                "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
              )}
            >
              All
            </TabsTrigger>
            <TabsTrigger
              value="users"
              className={cn(
                "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                "data-[state=active]:text-foreground data-[state=active]:border-b-2",
                "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
              )}
            >
              Users
            </TabsTrigger>
            <TabsTrigger
              value="products"
              className={cn(
                "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                "data-[state=active]:text-foreground data-[state=active]:border-b-2",
                "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
              )}
            >
              Products
            </TabsTrigger>
            <TabsTrigger
              value="categories"
              className={cn(
                "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                "data-[state=active]:text-foreground data-[state=active]:border-b-2",
                "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
              )}
            >
              Categories
            </TabsTrigger>
            <TabsTrigger
              value="subcategories"
              className={cn(
                "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                "data-[state=active]:text-foreground data-[state=active]:border-b-2",
                "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
              )}
            >
              Subcategories
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="all">
          {query ? (
            <>
              {hasUserResults && (
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold">People</h2>
                    {users.length > 3 && (
                      <Button size="sm" variant="ghost" onClick={() => {
                        router.push(`/search/users?q=${encodeURIComponent(query)}`);
                      }}>
                        {showAllUsers ? "Show less" : "Show more"}
                      </Button>
                    )}
                  </div>
                  <UserResults 
                    users={uniqueUsersForAllTab as User[]}
                    onLoadMore={showAllUsers ? fetchMoreUsers : undefined}
                    hasMore={showAllUsers && !userIsDone && uniqueUsersForAllTab.length < users.length}
                    loading={userLoading && showAllUsers}
                    sortOrder={sortOrder}
                  />
                </div>
              )}

              {renderCategoryResults()}
              {renderSubcategoryResults()}

              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold">Products</h2>
                  <ProductResults query={query} limit={showAllProducts ? undefined : 3} />
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">Enter a search term to see results</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="users">
          {query ? (
            users.length > 0 ? (
              <UserResults 
                users={users}
                layout="grid"
                onLoadMore={fetchMoreUsers}
                hasMore={!userIsDone}
                loading={userLoading}
              />
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No users found matching &quot;{query}&quot;</p>
              </div>
            )
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">Enter a search term to see results</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="products">
          {query ? (
            <ProductResults query={query} layout="grid" />
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">Enter a search term to see results</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
function uniqueById(arr: User[]) {
  const seen = new Set();
  return arr.filter((item) => {
    if (!item?._id) return false;
    if (seen.has(item._id)) return false;
    seen.add(item._id);
    return true;
  });
}

