"use client";

import React from "react";
import { ScheduleStreamForm, ScheduleStreamFormData } from "@/components/schedule/schedule-stream-form";
import { useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useRouter } from "next/navigation";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

const SchedulePage: React.FC = () => {
  const createStreamMutation = useAction(api.streams.createStream);
  const router = useRouter();

  const handleSubmit = async (data: ScheduleStreamFormData & { thumbnailStorageId?: string }) => {
    try {
      const streamId = await createStreamMutation({
        title: data.title,
        thumbnail: data.thumbnailStorageId as Id<"_storage"> | undefined,
        scheduledTime: data.scheduledDatetime.getTime(),
        category: data.category,
        subcategory: data.subcategory,
        format: data.format,
        tags: data.tags,
        explicitContent: data.content.explicitContent,
        muteWords: data.content.muteWords,
        visibility: data.visibility,
        eventType: data.eventType,
        description: data.description,
        moderatorIds: data.moderatorIds.map(id => id as Id<"users">),
      });
      router.push(`/stream/${streamId}`);
    } catch (err) {
      console.error("Failed to create stream:", err);
      alert("Failed to create stream. Check the console for details.");
    }
  };

  return (  
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-6">Schedule a Stream</h1>
      <ScheduleStreamForm onSubmit={handleSubmit} />
    </div>
  );
};

export default SchedulePage; 