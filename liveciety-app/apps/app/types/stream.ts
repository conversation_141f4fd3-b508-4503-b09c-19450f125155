import { Doc, Id } from "@workspace/backend/convex/_generated/dataModel";

export interface User extends Doc<"users"> {
  name?: string;
  firstName?: string;
  lastName?: string;
  email: string;
  phone?: string;
  image?: string;
  emailVerificationTime?: number;
  phoneVerificationTime?: number;
  isAnonymous?: boolean;
  lastLoginType?: "password" | "oauth" | "magiclink" | "credentials";
  color?: string;
  theme?: string;
  lastSeen?: number;
  status?: "online" | "idle" | "offline";
  username?: string;
  role: "user" | "seller" | "admin";
  bio?: string;
  sellerProfile?: {
    bio: string;
    storeName: string;
    storeDescription: string;
    verified: boolean;
    rating: number;
    totalSales: number;
    joinedAt: number;
  };
  preferences?: {
    notifications: boolean;
    emailUpdates: boolean;
    darkMode: boolean;
  };
}

export interface StreamMetrics {
  totalViews: number;
  peakViewerCount: number;
}

export interface Stream extends Doc<"streams"> {
  title: string;
  thumbnail?: Id<"_storage">;
  ingestUrl?: string;
  streamKey?: string; 
  isLive: boolean;
  isChatEnabled: boolean;
  isChatDelayed: boolean;
  isChatFollowersOnly: boolean;
  hostId: Id<"users">;
  updatedAt: number;
  streamer: User | null;
  host: User | null;
  viewerCount: number;
  viewers: User[];
  metrics: StreamMetrics;
}

export interface StreamCardProps {
  stream: Stream;
}

export interface StreamSectionProps {
  streams: Stream[];
}
