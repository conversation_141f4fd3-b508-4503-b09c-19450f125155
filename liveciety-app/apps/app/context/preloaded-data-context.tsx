"use client";

import React, { createContext, ReactNode } from "react";
import { Preloaded } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";

export interface PreloadedData {
  preloadedUser: Preloaded<typeof api.users.viewer>;
}

export const PreloadedDataContext = createContext<PreloadedData | null>(null);

export function PreloadedDataProvider({
  children,
  preloadedData,
}: {
  children: ReactNode;
  preloadedData: PreloadedData;
}) {
  return (
    <PreloadedDataContext.Provider value={preloadedData}>
      {children}
    </PreloadedDataContext.Provider>
  );
}
