import React from "react";
import { preloadQuery } from "convex/nextjs";
import { api } from "@workspace/backend/convex/_generated/api";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";
import { ClientWrapper } from "./client-wrapper";

export default async function AppWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const token = await convexAuthNextjsToken();
  console.log("token:", token);
  const isAuthenticated = !!token;
  console.log("isAuthenticated:", isAuthenticated);
  console.log("preloading user...");
  let preloadedUser;
  try {
    preloadedUser = await preloadQuery(api.users.viewer, {}, { token });
  } catch (e) {
    console.error("Error preloading user:", e);
    throw e;
  }

  return (
    <ClientWrapper
      preloadedData={{
        preloadedUser,
      }}
      isAuthenticated={isAuthenticated}
    >
      {isAuthenticated ? (
        <>
          {children}
        </>
      ) : (
        children
      )}
    </ClientWrapper>
  );
}
