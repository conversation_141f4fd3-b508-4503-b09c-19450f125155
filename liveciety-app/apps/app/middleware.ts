import {
  convexAuthNextjsMiddleware,
  createRouteMatcher,
  nextjsMiddlewareRedirect,
} from "@convex-dev/auth/nextjs/server";
import { NextResponse } from "next/server";

const isSignInPage = createRouteMatcher(["/login"]);
const isPublicRoute = createRouteMatcher([
  "/",
  "/login",
  "/privacy-policy",
  "/legal",
  "/legal/:path*",
]);

export default convexAuthNextjsMiddleware(async (request, { convexAuth }) => {
  if (request.headers.get("x-middleware-subrequest")) {
    return new NextResponse(null, { status: 403 });
  }

  const isAuthenticated = await convexAuth.isAuthenticated();

  if (isAuthenticated && isSignInPage(request)) {
    return nextjsMiddlewareRedirect(request, "/");
  }

  if (isPublicRoute(request)) {
    return NextResponse.next();
  }

  if (!isAuthenticated) {
    return nextjsMiddlewareRedirect(request, "/login");
  }
  return NextResponse.next();
});

export const config = {
  matcher: ["/((?!.*\\..*|_next).*)", "/", "/(api|trpc)(.*)"],
};
