import { But<PERSON> } from "@workspace/ui/components/button";
import { IconArrowLeft, IconSend, IconDotsVertical } from "@tabler/icons-react";
import { Input } from "@workspace/ui/components/input";
import { cn } from "@workspace/ui/lib/utils";
import { useState, useEffect, useMemo } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { formatDistanceToNow } from "date-fns";
import { UserAvatar } from "./user-avatar";
import { User } from "@workspace/backend/convex/lib/types";
import { BanIcon, ShieldAlertIcon } from "lucide-react";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { useRouter } from "next/navigation";

interface ChatInterfaceProps {
  recipient: any;
  onBack: () => void;
}

export const ChatInterface = ({ recipient, onBack }: ChatInterfaceProps) => {
  const router = useRouter();
  const [messageText, setMessageText] = useState("");
  const [chatId, setChatId] = useState<Id<"chats"> | null>(recipient.id || null);
  const [messageTimeDisplays, setMessageTimeDisplays] = useState<Record<string, boolean>>({});
  const [isBlockedByUser, setIsBlockedByUser] = useState(false);
  
  const messages = useQuery(
    api.chat.getMessages,
    chatId ? { chatId } : "skip"
  );
  
  const chatDetails = useQuery(
    api.chat.getChat,
    chatId ? { chatId } : "skip"
  );
  
  const createChat = useMutation(api.chat.createChat);
  const sendMessage = useMutation(api.chat.sendMessage);

  const blockUser = useMutation(api.users.blockUser);
  const unblockUser = useMutation(api.users.unblockUser);
  
  const hasBlockedCheck = useQuery(
    api.users.hasBlocked,
    chatDetails?.otherUser?._id ? { targetUserId: chatDetails.otherUser._id } : "skip"
  );
  
  const markMessagesAsRead = useMutation(api.chat.markMessagesAsRead);
  
  useEffect(() => {
    if (hasBlockedCheck !== undefined) {
      setIsBlockedByUser(hasBlockedCheck);
    }
  }, [hasBlockedCheck]);

  const recipientAsUser = useMemo(() => ({
    _id: recipient.id as unknown as string || "temp",
    name: recipient.name,
    username: recipient.name.toLowerCase().replace(/\s+/g, ''),
    image: recipient.avatar,
    color: "#94A3B8",
    status: "offline"
  }), [recipient]);

  const otherUser = useMemo(() => {
    if (!chatDetails || !chatDetails.otherUser) return recipientAsUser;
    
    const other = chatDetails.otherUser;
    
    return {
      _id: other._id,
      name: other.name || "Unknown",
      username: other.username || other.name?.toLowerCase().replace(/\s+/g, '') || "user",
      image: other.image || null,
      color: other.color || "#94A3B8",
    };
  }, [chatDetails, recipientAsUser]);

  useEffect(() => {
    const initializeChat = async () => {
      if (!chatId && recipient.id) {
        setChatId(recipient.id);
      }
    };
    
    initializeChat();
  }, [chatId, recipient, createChat]);

  useEffect(() => {
    if (chatId) {
      markMessagesAsRead({ chatId });
    }
  }, [chatId, markMessagesAsRead]);

  const handleSend = async () => {
    if (!messageText.trim()) return;
    
    try {
      const actualChatId = chatId;
      
      if (!actualChatId) {
        console.error("No chat ID available");
        return;
      }
      
      await sendMessage({
        chatId: actualChatId,
        text: messageText,
      });
      
      setMessageText("");
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  if (chatId && messages === undefined) {
    return (
      <div className="flex flex-col h-full">
        <div className="flex items-center gap-3 p-4 border-b border-input">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="shrink-0"
          >
            <IconArrowLeft className="h-5 w-5" />
          </Button>
          <UserAvatar 
            user={recipientAsUser as User}
          />
          <div>
            <h3 className="font-semibold">{recipient.name}</h3>
          </div>
        </div>
        
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-pulse h-6 w-6 rounded-full bg-primary/30" />
        </div>
      </div>
    );
  }

  const getSenderInfo = (senderId: Id<"users">) => {
    if (!chatDetails) return null;
    
    if (senderId === chatDetails.currentUserId) {
      const selfParticipant = chatDetails.participants.find(p => p._id === chatDetails.currentUserId);
      
      return {
        _id: chatDetails.currentUserId,
        name: "Me",
        username: "me",
        image: selfParticipant?.image || null,
        color: selfParticipant?.color || "#4F46E5",
        status: "online" as const,
      };
    }
    
    const sender = chatDetails.participants.find(p => p._id === senderId);
    if (!sender) return null;
    
    return {
      _id: sender._id as Id<"users">,
      name: sender.name || "Unknown",
      username: sender.username || sender.name?.toLowerCase().replace(/\s+/g, '') || "user",
      image: sender.image || null,
      color: sender.color || "#94A3B8",
    };
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b border-input">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="shrink-0"
          >
            <IconArrowLeft className="h-5 w-5" />
          </Button>
          <UserAvatar 
            user={otherUser as User}
            size="default"
          />
          <div>
            <h3 className="font-semibold cursor-pointer hover:underline" 
              onClick={() => {
                if (otherUser.username) {
                  router.push(`/user/${otherUser.username}`);
                }
              }}
            >
              {otherUser.name}
            </h3>
          </div>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <IconDotsVertical className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="min-w-[160px]">
            <DropdownMenuItem variant="destructive" onClick={async () => {
              if (!otherUser._id) return;
              
              try {
                if (isBlockedByUser) {
                  await unblockUser({ targetUserId: otherUser._id as Id<"users"> });
                  toast.success(`Unblocked ${otherUser.name}`);
                } else {
                  await blockUser({ targetUserId: otherUser._id as Id<"users"> });
                  toast.success(`Blocked ${otherUser.name}`);
                }
                setIsBlockedByUser(!isBlockedByUser);
              } catch (error) {
                toast.error("Failed to update block status");
                console.error(error);
              }
            }}>
              <BanIcon className="h-4 w-4 mr-2" />
              {isBlockedByUser ? "Unblock" : "Block"}
            </DropdownMenuItem>
            <DropdownMenuItem variant="destructive" onClick={() => {
              if (otherUser.username) {
                router.push(`/user/${otherUser.username}`);
              }
            }}>
              <ShieldAlertIcon className="h-4 w-4 mr-2" />
              Report user
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages && messages.length > 0 ? (
          messages.map((msg) => {
            const isCurrentUser = chatDetails && msg.senderId === chatDetails.currentUserId;
            const senderInfo = getSenderInfo(msg.senderId);
            
            if (!senderInfo) return null;
            
            return (
              <div
                key={msg._id}
                className={cn(
                  "flex gap-2 max-w-[80%]",
                  isCurrentUser ? "ml-auto flex-row-reverse" : "",
                )}
              >
                <UserAvatar 
                  user={senderInfo as User}
                  size="sm"
                />
                <div className={cn(
                  isCurrentUser ? "flex flex-col items-end" : ""
                )}>
                  <div
                    className={cn(
                      "rounded-lg px-2 py-1 w-fit",
                      isCurrentUser
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted",
                    )}
                  >
                    <p className="text-sm">{msg.text}</p>
                  </div>
                  <p 
                    className="text-xs text-muted-foreground mt-1 cursor-pointer"
                    onClick={() => {
                      setMessageTimeDisplays(prev => ({
                        ...prev,
                        [msg._id]: !prev[msg._id]
                      }));
                    }}
                  >
                    {messageTimeDisplays[msg._id] ? (
                      `${new Date(msg._creationTime).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                      })} ${new Date(msg._creationTime).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                      })}`
                    ) : (
                      formatDistanceToNow(new Date(msg._creationTime), { addSuffix: true })
                    )}
                  </p>
                </div>
              </div>
            );
          })
        ) : (
          <div className="text-center text-muted-foreground py-8">
            No messages yet. Send a message to start the conversation.
          </div>
        )}
      </div>

      <div className="p-4 border-t border-input">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSend();
          }}
          className="relative"
        >
          <Input
            placeholder="Type a message..."
            value={messageText}
            onChange={(e) => setMessageText(e.target.value)}
            className="pr-12"
          />
          <Button
            type="submit"
            size="icon"
            disabled={!messageText.trim() || !chatId}
            className={cn(
              "absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7",
              (!messageText.trim() || !chatId) && "opacity-50 cursor-not-allowed",
            )}
          >
            <IconSend className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
};
