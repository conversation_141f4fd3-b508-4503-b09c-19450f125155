import React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { User } from "@/lib/types";
import { getAvatarImageUrl } from "@/lib/utils";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { LiveBadge } from "@/components/live-badge";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { isValidStorageId } from "@workspace/backend/convex/helpers/utils";

const avatarSizes = cva("", {
  variants: {
    size: {
      default: "h-8 w-8",
      sm: "h-6 w-6",
      lg: "h-14 w-14",
      xl: "h-24 w-24",
      xs: "h-4 w-4",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

const statusSizes = cva("", {
  variants: {
    size: {
      default: "size-3",
      sm: "size-2.5",
      lg: "size-4",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

interface UserAvatarProps extends VariantProps<typeof avatarSizes> {
  user: Partial<User> & {
    _id?: Id<"users"> | string;
    name: string;
    username: string;
    image?: string | null;
    status?: "online" | "offline";
    color?: string;
    isLive?: boolean;
  } | any;
  height?: number;
  width?: number;
  showOnlineIndicator?: boolean;
  isLive?: boolean;
}

interface UserAvatarSkeletonProps extends VariantProps<typeof avatarSizes> {}

export function UserAvatar({
  user,
  size = "default",
  height,
  width,
  showOnlineIndicator = true,
  isLive,
}: UserAvatarProps) {
  // Try to get the image URL using Convex storage if it's a storage ID
  const storageUrl = useQuery(
    api.users.getImageUrl,
    user?.image && isValidStorageId(user.image) 
      ? { storageId: user.image as Id<"_storage"> }
      : "skip"
  );

  // Determine the final image URL
  let imageUrl: string | undefined = undefined;
  
  if (user?.image) {
    // If it's already a URL, use it directly
    if (typeof user.image === 'string' && (user.image.includes('http') || user.image.includes('/getImage'))) {
      imageUrl = user.image;
    } 
    // If we got a storage URL from the query, use that
    else if (storageUrl) {
      imageUrl = storageUrl;
    } 
    // Otherwise try to get the avatar URL using the utility function
    else {
      const avatarUrl = getAvatarImageUrl(user.image);
      imageUrl = avatarUrl ? avatarUrl : undefined;
    }
  }

  return (
    <div className="relative w-fit flex flex-col items-center">
      <Avatar
        className={cn(
          "rounded-full",
          isLive && "ring-rose-500 ring-1",
          avatarSizes({ size }),
        )}
        style={{ 
          height, 
          width, 
          backgroundColor: user?.color ? `${user.color}50` : undefined, 
        }}
      >
        <AvatarImage 
          src={imageUrl} 
          className="object-cover" 
          style={{ height, width }}
          onError={(e) => {
            console.error("Avatar image failed to load:", imageUrl);
            e.currentTarget.src = "";
          }}
        />
        <AvatarFallback style={{ 
          height, 
          width, 
          backgroundColor: user?.color ? `${user.color}80` : undefined, 
        }}>
          {user?.username?.[0]?.toUpperCase() || user?.name?.[0]?.toUpperCase() || "?"}
          {user?.username?.[user?.username?.length - 1]?.toUpperCase() || user?.name?.[user?.name?.length - 1]?.toUpperCase() || "?"}
        </AvatarFallback>
      </Avatar>
      {isLive && (
        <div className="absolute -bottom-3 right-1/2 transform translate-x-1/2">
          <LiveBadge />
        </div>
      )}
    </div>
  );
}

export function UserAvatarSkeleton({ size }: UserAvatarSkeletonProps) {
  return <Skeleton className={cn("rounded-full", avatarSizes({ size }))} />;
}
