"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { useState } from "react";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  IconBan,
  IconFlag,
  IconTruck,
  IconMilitaryAward,
  IconPackage,
} from "@tabler/icons-react";
import { SellerCategorySelection } from "./seller-category-selection";
import { User } from "@/lib/types";

const items = [
  {
    title: "Honor purchases and giveaways",
    description: "Don't cancel auctions for going below a desired amount.",
    icon: IconMilitaryAward,
  },
  {
    title: "Do not sell counterfeits",
    description:
      "Don't sell fake items on Liveciety. If you're unsure about an item, just don't sell it.",
    icon: IconBan,
  },
  {
    title: "Be honest about items",
    description:
      "Don't mislead buyers about an item's condition, value, or anything else.",
    icon: IconFlag,
  },
  {
    title: "Ship quickly and safely",
    description:
      "Ship items within 2 business days after a show has ended or when an item is sold.",
    icon: IconTruck,
  },
  {
    title: "Handle all buyers returns",
    description: "All returns are handled between you and the buyer in timely fashion (2 days)",
    icon: IconPackage
  }
];

export function BecomeASellerDialog({ user }: { user: User | null }) {
  const [step, setStep] = useState<"terms" | "category">("terms");
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const handleContinue = () => {
    if (submitting) return;
    if (step === "terms" && agreedToTerms) {
      setSubmitting(true);
      setStep("category");
      setSubmitting(false);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          className="font-medium hover:bg-primary/10 hover:text-primary transition-colors"
          disabled={user?.isSellerInterested}
        >
          {user?.isSellerInterested ? "Applied for Seller" : "Become a Seller"}
        </Button>
      </DialogTrigger>
      <DialogContent
        className="!w-screen !h-auto max-w-none max-h-none rounded-none sm:!w-screen sm:!min-w-screen sm:!h-screen sm:rounded-none p-0 shadow-xl"
        close={false}
      >
        <DialogTitle className="sr-only">
          Let&apos;s get started!
        </DialogTitle>
        {step === "terms" ? (
          <div className="flex flex-col">
            <div className="bg-gradient-to-br from-red-400/30 to-blue-500/20 px-8 py-10">
              <DialogHeader>
                <DialogTitle className="text-4xl font-bold tracking-tight mb-2">
                  Let&apos;s get started!
                </DialogTitle>
                <DialogDescription className="text-base opacity-90">
                  Before you kick off your selling journey, please agree to these
                  guidelines.
                </DialogDescription>
              </DialogHeader>

              <div className="mt-6">
                <div className="flex-none">
                  <div className="w-full h-2 bg-primary/10 rounded-full overflow-hidden">
                    <div className="h-full w-[20%] bg-gradient-to-r from-red-400 to-blue-500 rounded-full"></div>
                  </div>
                  <div className="flex justify-between text-sm mt-3">
                    <span className="font-medium">Guidelines</span>
                    <span className="text-zinc-500">Categories</span>
                    <span className="text-zinc-500">Experience</span>
                    <span className="text-zinc-500">Additional Details</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="px-8 py-6 overflow-y-auto sm:!h-[calc(100vh-19.5rem)] h-full">
              <div className="space-y-6 mb-8">
                {items.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-4 group"
                  >
                    <div className="p-3 bg-gradient-to-br from-red-400/20 to-blue-500/10 rounded-full text-zinc-800 dark:text-white shrink-0 group-hover:from-red-400/30 group-hover:to-blue-500/20 transition-colors">
                      <item.icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-base mb-1">{item.title}</h3>
                      <p className="text-zinc-600 dark:text-zinc-400 text-sm">
                        {item.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="flex items-center space-x-3 p-4 border border-zinc-200 dark:border-zinc-800 rounded-lg bg-zinc-50 dark:bg-zinc-900/50">
                <Checkbox
                  id="terms"
                  checked={agreedToTerms}
                  onCheckedChange={(checked) =>
                    setAgreedToTerms(checked as boolean)
                  }
                  className="h-5 w-5 data-[state=checked]:bg-gradient-to-br data-[state=checked]:from-red-400 data-[state=checked]:to-blue-500 data-[state=checked]:border-none"
                />
                <label
                  htmlFor="terms"
                  className="text-sm leading-tight cursor-pointer"
                >
                  I agree to honor purchases, not sell counterfeits, be honest
                  about items, and ship quickly and safely
                </label>
              </div>
            </div>

            <DialogFooter className="px-8 py-6 border-t dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900/50">
              <Button
                size="lg"
                onClick={handleContinue}
                disabled={!agreedToTerms || submitting}
                className="w-full sm:w-auto font-medium transition-colors"
              >
                {submitting ? "Continuing..." : "Continue"}
              </Button>
            </DialogFooter>
          </div>
        ) : (
          <SellerCategorySelection setStep={setStep} />
        )}
      </DialogContent>
    </Dialog>
  );
}
