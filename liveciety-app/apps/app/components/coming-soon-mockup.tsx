import React from "react";

const placeholderCards = Array.from({ length: 12 });

export const ComingSoonMockup: React.FC = () => {
  return (
    <div className="relative bg-zinc-100 dark:bg-zinc-900 p-6 min-h-[calc(100vh-100px)]">
      {/* Grid of placeholder cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6">
        {placeholderCards.map((_, idx) => (
          <div
            key={idx}
            className="bg-white dark:bg-zinc-800 rounded-xl shadow-md h-56 flex flex-col items-center justify-center animate-pulse border border-zinc-200 dark:border-zinc-700"
          >
            <div className="w-20 h-20 bg-zinc-200 dark:bg-zinc-700 rounded-full mb-4" />
            <div className="w-24 h-4 bg-zinc-200 dark:bg-zinc-700 rounded mb-2" />
            <div className="w-16 h-4 bg-zinc-200 dark:bg-zinc-700 rounded" />
          </div>
        ))}
      </div>
      {/* Blur overlay */}
      <div className="absolute inset-0 flex items-center justify-center backdrop-blur-md bg-black/40 dark:bg-black/70 z-10">
        <span className="text-4xl sm:text-5xl font-bold text-white drop-shadow-lg">Coming soon!</span>
      </div>
    </div>
  );
};

export default ComingSoonMockup; 