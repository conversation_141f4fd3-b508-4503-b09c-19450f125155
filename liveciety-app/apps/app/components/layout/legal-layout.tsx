"use client";

import Link from "next/link";
import { ChevronLeft } from "lucide-react";

interface LegalLayoutProps {
  children: React.ReactNode;
  title: string;
  lastUpdated?: string;
}

export function LegalLayout({
  children,
  title,
  lastUpdated,
}: LegalLayoutProps) {
  return (
    <div className="max-w-3xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <Link
        href="/legal"
        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-6"
      >
        <ChevronLeft className="w-4 h-4 mr-1" />
        Back to Legal
      </Link>
      <div className="prose prose-gray max-w-none">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
          {title}
        </h1>
        {lastUpdated && (
          <p className="text-sm text-gray-500 mt-2">
            Last Updated {lastUpdated}
          </p>
        )}
        <div className="mt-8">{children}</div>
      </div>
    </div>
  );
}
