"use client";

import * as React from "react";
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Home,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  X,
  Bell,
  FileText,
  Package,
} from "lucide-react";
import {
  IconCircleCheck,
  IconDashboard,
  IconHelp,
  IconUserCircle,
  IconLogout,
  IconBuildingSkyscraper,
  IconSettings,
  IconUser,
  IconUsers,
  IconBell,
  IconKey,
  IconCreditCard,
  IconChevronLeft,
  IconSearch,
  IconMail,
  IconCreditCardPay,
  IconTrash,
  IconPlus,
  IconSquareRoundedCheck,
  IconSquareRoundedCheckFilled,
  IconSmartHome,
  IconVideo,
  IconVersions,
  IconMapPin,
  IconCast,
  IconScreenShare,
  IconPackage,
} from "@tabler/icons-react";

import { NavMain } from "@/components/layout/nav-main";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  useSidebar,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
} from "@workspace/ui/components/sidebar";
import { Logo } from "@workspace/ui/components/logo";
import { cn } from "@workspace/ui/lib/utils";
import { NavFollowing } from "./nav-faollowing";
import { useRouter, usePathname } from "next/navigation";
import { Input } from "@workspace/ui/components/input";
import Image from "next/image";
import { UserPreferences } from "./user-preferences";
import { FeedbackButton } from "@/components/feedback/feedback-button";
import { ChangelogButton } from "@/components/changelog/changelog-button";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { usePreloadedQuery } from "convex/react";

const data = {
  navMain: [
    {
      title: "Home",
      url: "/",
      icon: IconSmartHome,
    },
    {
      title: "Browse",
      url: "/browse",
      icon: IconSearch,
    },
    {
      title: "For You",
      url: "/for-you",
      icon: IconVideo,
    },
    {
      title: "Followed Hosts",
      url: "/followed-hosts",
      icon: IconUsers,
    },
  ],
  following: [
    {
      name: "Design Engineering",
      url: "#",
      icon: Frame,
    },
    {
      name: "Sales & Marketing",
      url: "#",
      icon: PieChart,
    },
    {
      name: "Travel",
      url: "#",
      icon: Map,
    },
  ],
  settingsNav: [
    {
      heading: "Account",
      items: [
        {
          title: "Account",
          url: "account",
          icon: IconUser,
          subItems: [
            { title: "Change email", url: "account/email", icon: IconMail },
            {
              title: "Change password",
              url: "account/password",
              icon: IconKey,
            },
            {
              title: "Update password",
              url: "account/update-password",
              icon: IconKey,
            },
            { title: "Delete account", url: "account/delete", icon: IconTrash },
          ],
        },
        {
          title: "Appearance",
          url: "appearance",
          icon: IconSettings,
        },
      ],
    },
    {
      heading: "Buyer",
      items: [
        {
          title: "Payments",
          url: "buyers/payments",
          icon: IconCreditCard,
        },
        {
          title: "Addresses",
          url: "buyers/addresses",
          icon: IconMapPin,
        },
      ],
    },
    {
      heading: "Seller",
      items: [
        {
          title: "Products",
          url: "sellers/products",
          icon: IconPackage,
        },
        {
          title: "Payouts",
          url: "sellers/payouts",
          icon: IconCreditCard,
        },
        {
          title: "Stream Tools",
          url: "sellers/tools",
          icon: IconCast,
        },
        {
          title: "Streams",
          url: "sellers/streams",
          icon: IconScreenShare,
        },
      ],
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { isMobile, state, setOpenMobile } = useSidebar();
  const router = useRouter();
  const pathname = usePathname();
  const [searchQuery, setSearchQuery] = React.useState("");
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);
  const isSettingsPage = pathname?.includes("/settings");

  const filteredSettingsNav = React.useMemo(() => {
    if (!user || user.role === "seller") return data.settingsNav;
    return data.settingsNav.filter(
      (section: any) => section.heading !== "Seller"
    );
  }, [user]);

  const filterItems = (items: any[]) => {
    if (!items) return [];
    if (!searchQuery.trim()) return items;

    return items.filter((item) => {
      if (!item) return false;

      const matchesSearch = item.title
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase());
      const hasMatchingSubItems = item.subItems?.some((subItem: any) =>
        subItem?.title?.toLowerCase().includes(searchQuery.toLowerCase()),
      );

      return matchesSearch || hasMatchingSubItems;
    });
  };

  const renderSettingsNav = (items: any[]) => {
    if (!items) return null;

    return items.map((item) => {
      if (!item) return null;

      if ("heading" in item) {
        return (
          <React.Fragment key={`heading-${item.heading}`}>
            <div className="px-2 pt-4 pb-2">
              <h3 className="text-xs font-normal text-gray-500">
                {item.heading}
              </h3>
            </div>
            {item.items && renderSettingsNav(filterItems(item.items))}
          </React.Fragment>
        );
      }

      return (
        <SidebarMenuItem key={`menu-${item.url}`}>
          <SidebarMenuButton
            onClick={() => router.push(`/settings/${item.url}`)}
            className={
              pathname?.includes(item.url || "") ? "bg-sidebar-accent" : ""
            }
          >
            {item.icon &&
              React.createElement(item.icon, { className: "h-4 w-4" })}
            <span>{item.title}</span>
          </SidebarMenuButton>
          {item.subItems && item.subItems.length > 0 && searchQuery.trim() && (
            <SidebarMenuSub>
              {item.subItems
                .filter((subItem: any) =>
                  subItem?.title
                    ?.toLowerCase()
                    .includes(searchQuery.toLowerCase()),
                )
                .map((subItem: any) => (
                  <SidebarMenuItem key={`sub-${item.url}-${subItem.url}`}>
                    <SidebarMenuButton
                      onClick={() => router.push(`/settings/${subItem.url}`)}
                      className={
                        pathname?.includes(subItem.url)
                          ? "bg-sidebar-accent"
                          : ""
                      }
                    >
                      <span>{subItem.title}</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
            </SidebarMenuSub>
          )}
        </SidebarMenuItem>
      );
    });
  };

  if (isSettingsPage) {
    return (
      <Sidebar collapsible="offcanvas" {...props}>
        <SidebarHeader className="bg-transparent">
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                onClick={() => router.push(`/`)}
                className="gap-2"
              >
                <IconChevronLeft className="h-4 w-4" />
                <span>Settings</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <div className="pt-2">
                <div className="relative">
                  <IconSearch className="absolute left-3 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    id="search-settings"
                    name="search-settings"
                    type="search"
                    autoComplete="off"
                    data-lpignore="true"
                    data-form-type="other"
                    placeholder="Search settings..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 bg-transparent"
                  />
                </div>
              </div>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarMenu className="px-2">
            {renderSettingsNav(filteredSettingsNav)}
          </SidebarMenu>
        </SidebarContent>
      </Sidebar>
    );
  }

  return (
    <Sidebar className="border-none bg-sidebar" collapsible="icon" {...props}>
      <SidebarHeader className="border-b h-16 flex items-center px-4">
        <div className="flex items-center justify-between w-full h-full">
          <div className="flex items-center gap-3 h-full">
            <div className="flex items-center justify-center h-6">
              <Image src="/logo-dark.png" alt="Liveciety" width={32} height={32} />
            </div>
            <span
              className={cn("tracking-tight font-bold text-lg leading-none", {
                hidden: state === "collapsed" && !isMobile,
                "text-lg": isMobile,
              })}
            >
              Liveciety
            </span>
          </div>
          {isMobile && (
            <button
              onClick={() => setOpenMobile(false)}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              <X size={20} />
            </button>
          )}
        </div>
      </SidebarHeader>
      <SidebarContent className="!border-r">
        <NavMain items={data.navMain} />
        <UserPreferences />
      </SidebarContent>
      <SidebarFooter className="!border-r !border-t">
        <SidebarMenu>
          <SidebarMenuItem>
            <FeedbackButton />
          </SidebarMenuItem>
          <SidebarMenuItem>
            <ChangelogButton />
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              onClick={() => router.push("/settings/account")}
              className={
                pathname?.includes("settings") ? "bg-sidebar-accent" : ""
              }
            >
              <IconSettings className="h-4 w-4" />
              <span>Settings</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
