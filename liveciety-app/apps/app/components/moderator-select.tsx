import React, { useState } from "react";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";
import { X } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@workspace/ui/components/avatar";
import { Command, CommandList, CommandEmpty, CommandGroup, CommandItem } from "@workspace/ui/components/command";
import { User } from "../lib/types";
import { UserAvatar } from "./user-avatar";
import { api } from "@workspace/backend/convex/_generated/api";
import { useQuery } from "convex/react";
import { getAvatarImageUrl } from "../lib/utils";
import { cn } from "@workspace/ui/lib/utils";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

export const ModeratorSelect: React.FC<{
  selected: Id<'users'>[];
  onChange: (selected: Id<'users'>[]) => void;
  placeholder?: string;
}> = ({ selected, onChange, placeholder = "Select moderators" }) => {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [search, setSearch] = useState("");
  const [open, setOpen] = useState(false);
  const searchResults = useQuery(api.users.searchUsers, search ? { searchQuery: search } : "skip");
  const selectedUsers = useQuery(api.users.getUsersByIds, selected.length > 0 ? { userIds: selected } : "skip") ?? [];

  const options = (searchResults?.users ?? []).map((user: User) => ({
    value: user._id,
    label: user.name || user.username || user.email,
    image: user.image,
    email: user.email,
    username: user.username,
    color: user.color,
  }));

  const selectables = options.filter(opt => !selected.includes(opt.value as Id<'users'>));

  const handleUnselect = (id: Id<'users'>) => onChange(selected.filter(s => s !== id));
  const handleSelect = (id: Id<'users'>) => {
    const user = options.find(u => u.value === id);
    if (user && !selected.includes(id)) {
      onChange([...selected, id]);
    }
    setSearch("");
    setOpen(false);
    inputRef.current?.blur();
  };

  const selectedUserObjects = selected.map(id =>
    selectedUsers.find((u) => u?._id === id) || { _id: id, username: id, image: undefined }
  );

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (
      (e.key === "Enter" || e.key === "," || e.key === " ") && selectables.length > 0 && search
    ) {
      e.preventDefault();
      if (selectables[0]?.value) {
        handleSelect(selectables[0]?.value as Id<'users'>);
      }
    } else if (e.key === "Backspace" && !search && selected.length) {
      onChange(selected.slice(0, -1));
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-1 py-1 rounded-lg relative">
      <Input
        ref={inputRef}
        className="flex-1 outline-none bg-transparent min-w-[100px] py-1"
        value={search}
        onChange={e => {
          setSearch(e.target.value);
          setOpen(true);
        }}
        onFocus={() => setOpen(true)}
        onBlur={() => setTimeout(() => setOpen(false), 100)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
      />
      {selectedUserObjects.map(user => (
        <Badge key={user._id} variant="secondary" className="flex items-center justify-between gap-1 w-full ">
          <div className="flex items-center gap-1">
            <UserAvatar size="sm" user={user} showOnlineIndicator={false} />
            {user.username}
          </div>
          <button
            type="button"
            onClick={() => handleUnselect(user._id as Id<'users'>)}
            className="ml-1 hover:opacity-70 transition-opacity"
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      ))}
      {open && selectables.length > 0 && (
        <div className={cn(
          "absolute left-0 top-full mt-2 w-full z-10 rounded-md border text-popover-foreground shadow-md outline-none animate-in",
          "ring-ring/50 ring-[3px] selection:!bg-blue-500/50",
        )}>
          <Command className="!bg-background">
            <CommandList>
              <CommandEmpty>No results found.</CommandEmpty>
              <CommandGroup>
                {selectables.map(option => (
                  <CommandItem
                    key={option.value}
                    onMouseDown={e => e.preventDefault()}
                    onSelect={() => handleSelect(option.value as Id<'users'>)}
                  >
                    <Avatar>
                      {option.image ? (
                        <AvatarImage src={getAvatarImageUrl(option.image)} alt={option.label} />
                      ) : (
                        <AvatarFallback
                          className="text-xs"
                          style={{ backgroundColor: option.color }}
                        >{option.label?.[0] || "U"}</AvatarFallback>
                      )}
                    </Avatar>
                    {option.username}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
      )}
    </div>
  );
}; 