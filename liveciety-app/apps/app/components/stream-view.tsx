import { useEffect, useState, useRef, FormEvent } from "react";
import { useAction, useMutation, useQuery } from "convex/react";
import { api } from "../../../packages/backend/convex/_generated/api";
import { Doc, Id } from "../../../packages/backend/convex/_generated/dataModel";
import {
  RoomEvent,
  Room,
  LocalTrackPublication,
  Track,
  RemoteParticipant,
  RemoteTrackPublication,
  ConnectionState,
  Participant,
} from "livekit-client";
import { toast } from "sonner";
import { DualSidebarProvider } from "@/app/stream/_components/dual-sidebar-provider";
import { CollapsibleLeftSidebar } from "@/app/stream/_components/collapsible-left-sidebar";
import { UserAvatar } from "./user-avatar";
import { Button } from "@workspace/ui/components/button";
import { VideoPlayer } from "@/app/stream/_components/video-player";
import { CollapsibleRightSidebar } from "@/app/stream/_components/collapsible-right-sidebar";
import { IconHeart, IconHeartFilled, IconShare, IconBookmark, IconBookmarkFilled, IconClock, IconPlayerPlay } from "@tabler/icons-react";
import { Badge } from "@workspace/ui/components/badge";
import { useToggleFollow } from "@/hooks/use-toggle-follow";
import { ShareModal } from "./share-modal";
import { PinnedProducts } from "@/app/stream/_components/pinned-products";

const LIVEKIT_WS_URL = process.env.NEXT_PUBLIC_LIVEKIT_WS_URL;
const OBS_DETECTION_TIMEOUT_MS = 3500;

interface StreamViewProps {
  streamId: Id<"streams">;
  onStreamEnded?: () => void;
  loggedInUser: Doc<"users">;
}

const StreamView = ({ loggedInUser, streamId, onStreamEnded }: StreamViewProps) => {
  const streamDetails = useQuery(api.streams.getStream, { streamId });
  const generateHostToken = useAction(api.integration.livekit.generateHostToken);
  const generateViewerToken = useAction(api.integration.livekit.generateViewerToken);
  const endStreamMutation = useMutation(api.streams.endStream);
  const goLiveMutation = useMutation(api.streams.goLive);
  const generateIngestInfoAction = useAction(api.integration.livekit.generateIngestInfo);
  const getParticipantCountAction = useAction(api.integration.livekit.getRoomParticipantsCount);
  const sendMessageMutation = useMutation(api.streams.sendMessage);

  const moderationData = useQuery(
    api.moderation.getStreamModerationData,
    streamId ? { streamId } : "skip"
  );
  const addModeratorMutation = useMutation(api.moderation.addModerator);
  const removeModeratorMutation = useMutation(api.moderation.removeModerator);
  const unblockUserMutation = useMutation(api.moderation.unblockUserFromChat);

  const [newModeratorId, setNewModeratorId] = useState<string>("");

  const [room, setRoom] = useState<Room | undefined>();
  const [token, setToken] = useState<string | undefined>();
  const [error, setError] = useState<string | undefined>();
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isLive, setIsLive] = useState(false);
  const [currentSource, setCurrentSource] = useState<"browser" | "obs" | "none">("none");
  const [viewerCount, setViewerCount] = useState<number | null>(null);

  const videoContainerRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const browserFallbackTimerRef = useRef<NodeJS.Timeout | null>(null);

  const [ingestUrl, setIngestUrl] = useState<string | null>(null);
  const [streamKey, setStreamKey] = useState<string | null>(null);
  const [showObsInfo, setShowObsInfo] = useState(false);
  const [assumeObsMode, setAssumeObsMode] = useState(false);
  const [showModerationPanel, setShowModerationPanel] = useState(false);

  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(0.5);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [shareOpen, setShareOpen] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [timeToStart, setTimeToStart] = useState<string>("");

  const {
    isFollowing,
    toggleFollow,
    loading: followLoading
  } = useToggleFollow({
    userId: streamDetails?.hostId,
    username: streamDetails?.streamer?.username,
    skip: !streamDetails?.hostId || !streamDetails?.streamer?.username
  })

  const isHost = loggedInUser?._id === streamDetails?.hostId;
  const isMod = streamDetails?.moderatorIds?.includes(loggedInUser?._id);

  const hasSentJoinMessageRef = useRef(false);

  // Calculate time until stream starts
  useEffect(() => {
    if (streamDetails?.scheduledStartTime && streamDetails.status === "scheduled") {
      const updateCountdown = () => {
        const now = new Date();
        const startTime = new Date(streamDetails.scheduledStartTime!);
        const diff = startTime.getTime() - now.getTime();

        if (diff <= 0) {
          setTimeToStart("Starting now!");
          return;
        }

        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);

        if (days > 0) {
          setTimeToStart(`${days}d ${hours}h ${minutes}m`);
        } else if (hours > 0) {
          setTimeToStart(`${hours}h ${minutes}m ${seconds}s`);
        } else if (minutes > 0) {
          setTimeToStart(`${minutes}m ${seconds}s`);
        } else {
          setTimeToStart(`${seconds}s`);
        }
      };

      updateCountdown();
      const interval = setInterval(updateCountdown, 1000);
      return () => clearInterval(interval);
    }
  }, [streamDetails?.scheduledStartTime, streamDetails?.status]);

  useEffect(() => {
    if (streamDetails?.status === "live" && streamDetails.roomName) {
      getParticipantCountAction({ roomName: streamDetails.roomName })
        .then(setViewerCount)
        .catch((err) => console.error("Failed to fetch viewer count:", err));

      const intervalId = setInterval(() => {
        if (room?.state === "connected" && streamDetails?.status === "live" && streamDetails.roomName) {
          getParticipantCountAction({ roomName: streamDetails.roomName })
            .then(setViewerCount)
            .catch((err) => console.error("Failed to refresh viewer count:", err));
        }
      }, 30000);
      return () => clearInterval(intervalId);
    } else {
      setViewerCount(null);
    }
  }, [streamDetails?.status, streamDetails?.roomName, getParticipantCountAction, room?.state]);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.muted = isMuted;
      videoRef.current.volume = volume;
    }
    if (audioRef.current) {
      audioRef.current.volume = volume;
      audioRef.current.muted = isMuted;
    }
  }, [isMuted, volume]);

  useEffect(() => {
    const handleFullscreenChange = () => setIsFullscreen(!!document.fullscreenElement);
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  useEffect(() => {
    if (!LIVEKIT_WS_URL) {
      setError("LiveKit WebSocket URL is not configured.");
      toast.error("LiveKit URL not configured.");
      return;
    }
    if (!streamId || !streamDetails) return;

    if (
      streamDetails.status !== "live" &&
      streamDetails.status !== "ended" &&
      streamDetails.status !== "scheduled" &&
      !isHost
    ) {
      setError(`Stream is not available. Current status: ${streamDetails.status}`);
      setIsConnected(false);
      if (room?.state === "connected") room.disconnect();
      return;
    }

    setError(undefined);

    // Only connect to LiveKit when stream is actually live
    if (streamDetails.status === "live") {
      if (isHost) {
        generateHostToken({ streamId })
          .then(setToken)
          .catch((err) => {
            console.error("Failed to get host token:", err);
            setError("Failed to get stream token.");
            toast.error("Error fetching stream token.");
          });
      } else {
        generateViewerToken({ streamId })
          .then(setToken)
          .catch((err) => {
            console.error("Failed to get viewer token:", err);
            setError("Failed to get stream token. Cannot view stream.");
            toast.error("Error fetching stream token.");
          });
      }
    }
  }, [streamId, generateHostToken, generateViewerToken, streamDetails, isHost]);

  useEffect(() => {
    // Only proceed with LiveKit connection if stream is live
    if (streamDetails?.status !== "live") {
      if (room?.state === ConnectionState.Connected || room?.state === ConnectionState.Connecting) {
        console.log("Disconnecting from room due to stream not being live");
        room.disconnect(true).catch(console.warn);
        setCurrentSource("none");
        setIsLive(false);
        setIsConnected(false);
        setRoom(undefined);
      }
      return;
    }

    if (error && !showObsInfo) return;
    if (!token || !LIVEKIT_WS_URL || !streamDetails?.roomName || !loggedInUser?._id) {
      return;
    }

    if (room && room.state !== ConnectionState.Disconnected && room.name === streamDetails.roomName) {
      if (isHost && assumeObsMode && room.state === ConnectionState.Connected) {
        room.localParticipant.setCameraEnabled(false).catch(console.warn);
        room.localParticipant.setMicrophoneEnabled(false).catch(console.warn);
        if (currentSource === "browser") {
          setCurrentSource("none");
          setIsLive(false);
        }
      }
      return;
    }

    const newRoom = new Room({
      adaptiveStream: true,
      dynacast: true,
      videoCaptureDefaults: { resolution: { width: 1280, height: 720, frameRate: 30 } },
    });
    setRoom(newRoom);
    setIsConnecting(true);
    setCurrentSource("none");
    setIsLive(false);
    setIsConnected(false);

    const connectAndSetup = async () => {
      try {
        await newRoom.connect(LIVEKIT_WS_URL, token, { autoSubscribe: !isHost });
        setIsConnecting(false);
        setIsConnected(true);

        // Auto-enable audio for better user experience
        if (audioRef.current) {
          audioRef.current.muted = false;
          audioRef.current.play().catch(console.warn);
        }

        // Only the host should ever request audio/video permissions from the browser.
        if (isHost) {
          if (browserFallbackTimerRef.current) clearTimeout(browserFallbackTimerRef.current);

          if (!assumeObsMode) {
            try {
              await newRoom.localParticipant.setCameraEnabled(true);
              await newRoom.localParticipant.setMicrophoneEnabled(true);
              setCurrentSource("browser");
              setIsLive(true);
            } catch (e) {
              browserFallbackTimerRef.current = setTimeout(async () => {
                if (newRoom.state === ConnectionState.Connected && currentSource === "none" && !isLive) {
                  try {
                    await newRoom.localParticipant.setCameraEnabled(true);
                    await newRoom.localParticipant.setMicrophoneEnabled(true);
                    setCurrentSource("browser");
                    setIsLive(true);
                  } catch (e) {
                    toast.error("Could not start browser camera/mic.");
                    setCurrentSource("none");
                    setIsLive(false);
                  }
                }
              }, OBS_DETECTION_TIMEOUT_MS);
            }
          } else {
            await newRoom.localParticipant.setCameraEnabled(false).catch(console.warn);
            await newRoom.localParticipant.setMicrophoneEnabled(false).catch(console.warn);
          }

          newRoom.remoteParticipants.forEach((rp) => {
            if (rp.identity === loggedInUser._id) {
              rp.trackPublications.forEach((pub) => {
                if (pub.kind === Track.Kind.Video && !pub.isSubscribed) {
                  pub.setSubscribed(true);
                }
              });
            }
          });
        }
      } catch (e: any) {
        setError(`Failed to connect: ${e.message}`);
        toast.error(`Connection failed: ${e.message}`);
        setIsConnecting(false);
        setIsConnected(false);
        setIsLive(false);
      }
    };

    connectAndSetup();

    const handleViewerTrackSubscribed = (track: Track, participant: Participant) => {
      console.log("Track subscribed", track);
      if (participant.identity === streamDetails.hostId) {
        if (track.kind === Track.Kind.Video) {
          if (videoRef.current) {
            track.attach(videoRef.current);
          }
        } else if (track.kind === Track.Kind.Audio) {
          if (audioRef.current) {
            console.log("audio attached", audioRef.current);
            track.attach(audioRef.current);
            audioRef.current.muted = false;
            audioRef.current.play().catch(console.warn);
          }
        }
      }
    };

    const handleViewerTrackUnsubscribed = (track: Track, participant: Participant) => {
      if (participant.identity === streamDetails.hostId) {
        track.detach();
        if (track.kind === Track.Kind.Video && videoRef.current) videoRef.current.srcObject = null;
      }
    };

    const handleRemoteTrackPublished = (pub: RemoteTrackPublication, rp: RemoteParticipant) => {
      if (!isHost) return;
      if (rp.identity === loggedInUser._id && pub.kind === Track.Kind.Video && !pub.isSubscribed) {
        pub.setSubscribed(true);
      }
    };

    const handleTrackSubscribed = (track: Track, pub: RemoteTrackPublication, rp: RemoteParticipant) => {
      if (isHost) {
        if (rp.identity === loggedInUser?._id && track.kind === Track.Kind.Video) {
          if (browserFallbackTimerRef.current) clearTimeout(browserFallbackTimerRef.current);
          if (videoRef.current) {
            track.attach(videoRef.current);
          }
          setCurrentSource("obs");
          setIsLive(true);
          toast.info("OBS stream displaying.");
          newRoom.localParticipant.setCameraEnabled(false).catch(console.warn);
          newRoom.localParticipant.setMicrophoneEnabled(false).catch(console.warn);
        }
      } else {
        if (rp.identity === streamDetails.hostId) {
          handleViewerTrackSubscribed(track, rp);
        }
      }
    };

    const handleTrackUnsubscribed = (track: Track, pub: RemoteTrackPublication, rp: RemoteParticipant) => {
      if (isHost) {
        if (rp.identity === loggedInUser._id && track.kind === Track.Kind.Video) {
          track.detach();
          setCurrentSource("none");
          setIsLive(false);
          toast.info("OBS stream stopped.");
        }
      } else {
        if (rp.identity === streamDetails.hostId) {
          handleViewerTrackUnsubscribed(track, rp);
        }
      }
    };

    const handleLocalTrackPublished = (pub: LocalTrackPublication) => {
      if (!isHost) return;
      if (assumeObsMode || currentSource === "obs") {
        if (pub.track) newRoom.localParticipant.unpublishTrack(pub.track, true);
        return;
      }
      if (pub.source === Track.Source.Camera && pub.videoTrack && videoRef.current) { 
        pub.videoTrack.attach(videoRef.current);
        setCurrentSource("browser");
        setIsLive(true);
      } else if (pub.source === Track.Source.Microphone) {
        if (currentSource !== "browser") {
          setCurrentSource("browser");
        }
        setIsLive(true);
      }
    };

    const handleLocalTrackUnpublished = (pub: LocalTrackPublication) => {
      if (!isHost) return;
      if (pub.source === Track.Source.Camera && videoRef.current && pub.track) pub.track.detach(videoRef.current);
      const camPub = newRoom.localParticipant.getTrackPublication(Track.Source.Camera);
      const micPub = newRoom.localParticipant.getTrackPublication(Track.Source.Microphone);
      if ((!camPub || !camPub.track) && (!micPub || !micPub.track) && currentSource === "browser") {
        setCurrentSource("none");
        setIsLive(false);
      }
    };

    newRoom.on(RoomEvent.TrackPublished, handleRemoteTrackPublished);
    newRoom.on(RoomEvent.TrackSubscribed, handleTrackSubscribed);
    newRoom.on(RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed);
    if (isHost) {
      newRoom.on(RoomEvent.LocalTrackPublished, handleLocalTrackPublished);
      newRoom.on(RoomEvent.LocalTrackUnpublished, handleLocalTrackUnpublished);
    }
    newRoom.on(RoomEvent.Disconnected, (r) => {
      if (browserFallbackTimerRef.current) clearTimeout(browserFallbackTimerRef.current);
      setIsConnecting(false);
      setIsLive(false);
      setIsConnected(false);
      setCurrentSource("none");
      toast.info(`Disconnected: ${r || "No reason"}`);
    });
    newRoom.on(RoomEvent.Reconnecting, () => {
      setIsConnecting(true);
      toast.info("Reconnecting...");
    });
    newRoom.on(RoomEvent.Reconnected, async () => {
      setIsConnecting(false);
      setIsConnected(true);
      toast.success("Reconnected!");
    });
    newRoom.on(RoomEvent.ParticipantDisconnected, (participant: RemoteParticipant) => {
      if (!isHost && streamDetails && participant.identity === streamDetails.hostId) {
        toast.info("The host has ended the stream.");
        setError("The host has ended the stream.");
        setIsConnected(false);
        if (videoRef.current) videoRef.current.srcObject = null;
      }
    });

    return () => {
      if (browserFallbackTimerRef.current) clearTimeout(browserFallbackTimerRef.current);
      
      // Only disconnect if room is connected or connecting
      if (newRoom.state === ConnectionState.Connected || newRoom.state === ConnectionState.Connecting) {
        console.log("Cleaning up LiveKit room connection");
        newRoom.disconnect(true).catch(console.warn);
      }
      
      setRoom(undefined);
      setCurrentSource("none");
      setIsLive(false);
      setIsConnecting(false);
      setIsConnected(false);
    };
  }, [token, streamDetails?.roomName, loggedInUser?._id, streamDetails?.status, assumeObsMode, isHost]);

  useEffect(() => {
    // Only viewers (not host), only when connected to a live stream, only once per session
    if (
      !isHost &&
      streamDetails?.status === "live" &&
      isConnected &&
      loggedInUser &&
      !hasSentJoinMessageRef.current
    ) {
      hasSentJoinMessageRef.current = true;
      // Send a join message to the chat
      sendMessageMutation({
        streamId,
        text: `${loggedInUser.username || "A user"} joined the stream!`,
        type: "system",
      }).catch((err) => {
        // Don't block UI, just log
        console.error("Failed to send join message:", err);
      });
    }
  }, [isHost, streamDetails?.status, isConnected, loggedInUser, streamId, sendMessageMutation]);

  const toggleMute = () => setIsMuted(!isMuted);

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    if (newVolume > 0 && isMuted) setIsMuted(false);
    else if (newVolume === 0 && !isMuted) setIsMuted(true);
  };

  const toggleFullscreen = () => {
    if (!videoContainerRef.current) return;
    if (!document.fullscreenElement) {
      videoContainerRef.current.requestFullscreen().catch((err) => {
        toast.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
      });
    } else {
      document.exitFullscreen();
    }
  };

  const handleGoLive = async () => {
    if (!streamId || !isHost) return;
    try {
      await goLiveMutation({ streamId });
      toast.success("Stream is now LIVE!");
    } catch (err: any) {
      console.error("Failed to go live:", err);
      toast.error(`Failed to go live: ${err.data?.value || err.message}`);
    }
  };

  const handleEndStream = async () => {
    if (room) await room.disconnect(true);
    try {
      await endStreamMutation({ streamId });
      toast.success("Stream ended.");
      onStreamEnded?.();
    } catch (err) {
      console.error("Failed to end stream:", err);
      toast.error("Failed to end stream on server.");
    }
  };

  const toggleBookmark = () => {
    setIsBookmarked(!isBookmarked);
    toast.success(isBookmarked ? "Removed from bookmarks" : "Added to bookmarks");
  };

  const copyToClipboard = (text: string | null) => {
    if (text)
      navigator.clipboard.writeText(text).then(
        () => toast.success("Copied!"),
        () => toast.error("Copy failed.")
      );
  };

  const handleAddModerator = async (e: FormEvent) => {
    e.preventDefault();
    if (!streamId || !newModeratorId.trim() || !isHost) return;
    try {
      await addModeratorMutation({ streamId, userIdToAdd: newModeratorId.trim() as Id<"users"> });
      toast.success("Moderator added.");
      setNewModeratorId("");
    } catch (err: any) {
      toast.error(`Failed to add moderator: ${err.data?.value || err.message}`);
    }
  };

  const handleRemoveModerator = async (userIdToRemove: Id<"users">) => {
    if (!streamId || !isHost) return;
    try {
      await removeModeratorMutation({ streamId, userIdToRemove });
      toast.success("Moderator removed.");
    } catch (err: any) {
      toast.error(`Failed to remove moderator: ${err.data?.value || err.message}`);
    }
  };

  const handleUnblockUser = async (userIdToUnblock: Id<"users">) => {
    if (!streamId || !isHost) return;
    try {
      await unblockUserMutation({ streamId, userIdToUnblock });
      toast.success("User unblocked.");
    } catch (err: any) {
      toast.error(`Failed to unblock user: ${err.data?.value || err.message}`);
    }
  };

  const overallError = error && !showObsInfo;
  const obsInfoError = error && showObsInfo;

  if (overallError)
    return (
      <div className="text-red-500 p-4 text-center text-xl bg-red-100 border border-red-500 rounded-md">
        {error}
      </div>
    );

  if (!streamDetails)
    return <div className="p-4 text-center">Loading stream details...</div>;

  if (!token && streamDetails?.status === "live")
    return <div className="p-4 text-center">Initializing stream session...</div>;

  let statusMessage = "Stream is PENDING. Click 'Go Live!' to start.";

  if (streamDetails?.status === "scheduled") {
    statusMessage = isHost 
      ? "Stream is SCHEDULED. Click 'Go Live!' to start."
      : `Stream starts in ${timeToStart}`;
  } else if (streamDetails?.status === "live") {
    if (isConnecting) statusMessage = "Connecting to LiveKit...";
    else if (room?.state === ConnectionState.Connected) {
      if (isHost) {
        if (isLive)
          statusMessage = `You are LIVE ${
            currentSource === "obs"
              ? "(via OBS)"
              : currentSource === "browser"
              ? "(via browser)"
              : "(unknown source)"
          }`;
        else
          statusMessage = `Connected. ${
            assumeObsMode ? "Waiting for OBS stream..." : "Waiting for video source (Browser or OBS)..."
          }`;
      } else {
        statusMessage = "Connected to stream";
      }
    } else if (room?.state === ConnectionState.Disconnected && !error) statusMessage = "Disconnected.";
    else if (room?.state === ConnectionState.Connecting) statusMessage = "Connecting...";
    else statusMessage = `Stream is Live. Status: ${room?.state || "Unknown"}. Waiting for video...`;
  } else if (streamDetails?.status === "ended") {
    statusMessage = "This stream has ended.";
  }

  const getStreamUrl = () => {
    if (typeof window !== 'undefined') {
      return window.location.href;
    }
    return '';
  };

  const formatDateTime = (dateString: string | number) => {
    return new Date(dateString).toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  };

  return (
    <DualSidebarProvider defaultLeftOpen={true} defaultRightOpen={true}>
      <div className="flex h-screen w-full bg-background">
        <CollapsibleLeftSidebar />

        <main className="flex-1 flex flex-col min-w-0">
          {/* Header */}
          <header className="border-b p-4 bg-background">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <UserAvatar user={streamDetails.streamer} />
                <div>
                  <h1 className="text-xl font-bold">{streamDetails.streamer?.username || streamDetails.hostName}</h1>
                  <p className="text-sm text-muted-foreground">{streamDetails.title}</p>
                  {streamDetails?.status === "scheduled" && streamDetails.scheduledStartTime && (
                    <div className="flex items-center gap-2 mt-1">
                      <IconClock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        {formatDateTime(String(streamDetails.scheduledStartTime))}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2">
                {streamDetails?.status === "scheduled" && (
                  <>
                    {isHost ? (
                      <div className="flex items-center gap-2">
                        <div className="text-right mr-4">
                          <p className="text-sm font-medium">Stream scheduled</p>
                          <p className="text-xs text-muted-foreground">{timeToStart}</p>
                        </div>
                        <Button variant="outline" size="sm" onClick={() => setShareOpen(true)}>
                          <IconShare className="h-4 w-4" />
                          Share Stream
                        </Button>
                        <Button size="sm" onClick={handleGoLive}>
                          <IconPlayerPlay className="h-4 w-4 mr-2" />
                          Start Stream
                        </Button>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <div className="text-right mr-4">
                          <p className="text-sm font-medium text-primary">Starting in</p>
                          <p className="text-lg font-bold text-primary">{timeToStart}</p>
                        </div>
                        <Button variant="outline" size="sm" onClick={toggleBookmark}>
                          {isBookmarked ? <IconBookmarkFilled className="h-4 w-4 text-primary" /> : <IconBookmark className="h-4 w-4" />}
                          {isBookmarked ? "Bookmarked" : "Bookmark"}
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => setShareOpen(true)}>
                          <IconShare className="h-4 w-4" />
                          Share
                        </Button>
                      </div>
                    )}
                  </>
                )}

                {streamDetails?.status === "live" && !isHost && (
                  <Button variant="outline" size="sm" onClick={toggleFollow} disabled={followLoading}>
                    {isFollowing ? <IconHeartFilled className="h-4 w-4 text-red-500" /> : <IconHeart className="h-4 w-4" />}
                    {isFollowing ? "Unfollow" : "Follow"}
                  </Button>
                )}

                {streamDetails?.status === "live" && (
                  <Button variant="outline" size="sm" onClick={() => setShareOpen(true)}>
                    <IconShare className="h-4 w-4" />
                    Share
                  </Button>
                )}

                <ShareModal
                  open={shareOpen}
                  onOpenChange={setShareOpen}
                  username={streamDetails?.streamer?.username || ""}
                  profileUrl={getStreamUrl()}
                />

                {isHost && streamDetails?.status === "live" && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={handleEndStream}
                  >
                    End Stream
                  </Button>
                )}

                {isHost && streamDetails?.status === "ended" && (
                  <Button
                    size="sm"
                    variant="default"
                    onClick={handleGoLive}
                  >
                    <IconPlayerPlay className="h-4 w-4 mr-2" />
                    Restart Stream
                  </Button>
                )}
              </div>
            </div>
          </header>

          {/* Video Content */}
          <div className="flex-1 h-full overflow-auto">
            <div className="relative h-full">
              {streamDetails.status === "ended" && (
                <div className="absolute inset-0 z-10 flex items-center justify-center bg-black/60">
                  <span className="text-white text-2xl font-bold bg-red-600 px-6 py-2 rounded shadow-lg">Stream Ended</span>
                </div>
              )}
              <VideoPlayer
                videoContainerRef={videoContainerRef}
                videoRef={videoRef}
                audioRef={audioRef}
                isHost={isHost}
                isConnected={isConnected}
                isLive={isLive}
                isMuted={isMuted}
                volume={volume}
                toggleMute={toggleMute}
                streamDetails={streamDetails}
                handleVolumeChange={handleVolumeChange}
                toggleFullscreen={toggleFullscreen}
                isFullscreen={isFullscreen}
              />
              <PinnedProducts />
            </div>
          </div>

          {/* Stream Info */}
          <div className="border-t p-4 bg-background">
            <div className="max-w-4xl">
              <h2 className="text-lg font-semibold mb-2">{streamDetails.title}</h2>
              <p className="text-sm text-muted-foreground">
                {streamDetails.description}
              </p>
              <div className="flex gap-2 mt-3">
                {streamDetails.tags?.map((tag) => (
                  <Badge key={tag}>#{tag}</Badge>
                ))}
              </div>
            </div>
          </div>
        </main>

        <CollapsibleRightSidebar 
          showModerationPanel={showModerationPanel}
          setShowModerationPanel={setShowModerationPanel}
          moderationData={moderationData}
          handleAddModerator={handleAddModerator}
          handleRemoveModerator={handleRemoveModerator}
          handleUnblockUser={handleUnblockUser}
          newModeratorId={newModeratorId}
          setNewModeratorId={setNewModeratorId}
          showObsInfo={showObsInfo}
          setShowObsInfo={setShowObsInfo}
          ingestUrl={streamDetails.ingestUrl || null}
          streamKey={streamDetails.streamKey || null}
          copyToClipboard={copyToClipboard}
          setError={setError}
          setAssumeObsMode={setAssumeObsMode}
          isHost={isHost}
          isMod={isMod ?? false}
          streamDetails={streamDetails}
        />
      </div>
    </DualSidebarProvider>
  );
};

export default StreamView;