"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { ChevronRight, ShieldAlert } from "lucide-react";
import { toast } from "sonner";

const REPORT_REASONS = [
  "Live Emergency",
  "Inappropriate content", 
  "Selling prohibited items",
  "Youth safety",
  "Account Integrity",
  "Other"
];

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  contentType: "user" | "profile" | "message";
  contentId: string;
  reportedUserId: string;
  username?: string;
}

export function ReportModal({ 
  isOpen, 
  onClose, 
  contentType, 
  contentId, 
  reportedUserId,
  username 
}: ReportModalProps) {
  const [selectedReason, setSelectedReason] = useState<string>("");
  const [additionalDetails, setAdditionalDetails] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const submitReport = useMutation(api.moderation.submitReport);

  const handleSubmitReport = async () => {
    if (!selectedReason) {
      toast.error("Please select a reason for reporting");
      return;
    }

    setIsSubmitting(true);
    try {
      await submitReport({
        contentType,
        contentId,
        reportedUserId: reportedUserId as Id<"users">,
        reason: selectedReason,
        additionalDetails,
      });

      toast.success("Report submitted successfully");
      onClose();
      setSelectedReason("");
      setAdditionalDetails("");
    } catch (error) {
      toast.error("Failed to submit report. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl">Report User {username ? `"${username}"` : ""}</DialogTitle>
          <DialogDescription>
            Select a problem to report
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-4">
            {REPORT_REASONS.map((reason) => (
              <Button
                key={reason}
                variant={selectedReason === reason ? "default" : "outline"}
                className="w-full justify-between"
                onClick={() => setSelectedReason(reason)}
              >
                <div className="flex items-center gap-2">
                  {selectedReason === reason && (
                    <ShieldAlert className="h-4 w-4" />
                  )}
                  <span>{reason}</span>
                </div>
                <ChevronRight className="h-4 w-4 text-muted-foreground" />
              </Button>
            ))}
          </div>

          {selectedReason && (
            <div>
              <Label htmlFor="details">Additional Details (Optional)</Label>
              <Textarea
                id="details"
                placeholder="Please provide any additional context..."
                className="mt-2"
                value={additionalDetails}
                onChange={(e) => setAdditionalDetails(e.target.value)}
              />
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="ghost"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleSubmitReport}
            disabled={!selectedReason || isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Submit Report"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 