"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/sheet";
import { Button } from "@workspace/ui/components/button";
import {
  IconDeviceImacBolt,
  IconFilter,
  IconFriends,
  IconLogout,
  IconPhoto,
  IconShoppingCartDown,
  IconArrowLeft,
} from "@tabler/icons-react";
import { useAuthActions } from "@convex-dev/auth/react";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@workspace/ui/components/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@workspace/ui/components/dropdown-menu";
import { IconChevronDown } from "@tabler/icons-react";
import { useState, useEffect } from "react";
import { IconButton } from "@workspace/ui/components/icon-button";
import { cn } from "@workspace/ui/lib/utils";
import { ChatInterface } from "./chat-interface";
import { FriendsList } from "./friends-list";
import { ActivitySections } from "./activity-sections";
import { ChatList } from "./chat-list";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Badge } from "@workspace/ui/components/badge";

export const UserActivity = () => {
  const { signOut } = useAuthActions();
  const [selectedFilter, setSelectedFilter] = useState<
    "all" | "outbid" | "winning" | "ended"
  >("all");
  const [sortBy, setSortBy] = useState<
    "default" | "lowest" | "highest" | "ending"
  >("default");
  const [selectedTab, setSelectedTab] = useState<
    "purchases" | "bids" | "offers" | "messages" | "bookmarked"
  >("messages");
  const [selectedChat, setSelectedChat] = useState<{
    id?: Id<"chats">;
    name: string;
    avatar?: string;
    initials: string;
  } | null>(null);
  const [showFriends, setShowFriends] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [selectedChatId, setSelectedChatId] = useState<Id<"chats"> | undefined>(undefined);
  const [chatOpenedFromProfile, setChatOpenedFromProfile] = useState(false);

  const unreadCount = useQuery(api.chat.getUnreadMessagesCount);

  const handleSortChange = (value: string) => {
    setSortBy(value as "default" | "lowest" | "highest" | "ending");
  };

  const handleLogout = async () => {
    await signOut();
  };

  const handleChatBack = () => {
    if (chatOpenedFromProfile) {
      setIsSheetOpen(false);
      setSelectedChat(null);
      setSelectedChatId(undefined);
      setChatOpenedFromProfile(false);
      setSelectedTab("messages");
    } else {
      setSelectedChat(null);
      setSelectedChatId(undefined);
      setSelectedTab("messages");
    }
  };

  useEffect(() => {
    const handleOpenActivity = (event: CustomEvent) => {
      if (event.detail) {
        if (event.detail.tab) {
          setSelectedTab(event.detail.tab);
        }
        
        if (event.detail.chatId && event.detail.tab === 'messages') {
          setSelectedChatId(event.detail.chatId);
          setSelectedTab('messages');
          
          if (event.detail.fromProfile) {
            setChatOpenedFromProfile(true);
          } else {
            setChatOpenedFromProfile(false);
          }
        }
        
        setIsSheetOpen(true);
      }
    };
    
    document.addEventListener('open-activity', handleOpenActivity as EventListener);
    
    return () => {
      document.removeEventListener('open-activity', handleOpenActivity as EventListener);
    };
  }, []);

  return (
    <>
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <div className="relative">
            <IconButton icon={IconDeviceImacBolt} onClick={() => setIsSheetOpen(true)} />
            {typeof unreadCount === 'number' && unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center"
              >
                {unreadCount > 9 ? '9+' : unreadCount}
              </Badge>
            )}
          </div>
        </SheetTrigger>
        <SheetContent
          close={false}
          overlay={false}
          className="md:!max-w-lg sm:!max-w-full"
        >
          <SheetTitle className="sr-only">Activity</SheetTitle>
          {selectedChat ? (
            <>
              <ChatInterface
                recipient={selectedChat}
                onBack={handleChatBack}
              />
            </>
          ) : showFriends ? (
            <>
              <SheetHeader className="border-b border-input">
                <SheetTitle className="flex items-center justify-between">
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowFriends(false)}
                  >
                    <IconArrowLeft className="h-5 w-5" />
                  </Button>
                  <span className="text-xl font-semibold">Friends</span>
                  <div></div>
                </SheetTitle>
              </SheetHeader>
              <div>
                <FriendsList
                  onBack={() => setShowFriends(false)}
                  onMessageUser={(user) => {
                    setSelectedChat(user);
                    setShowFriends(false);
                    setChatOpenedFromProfile(false);
                  }}
                />
              </div>
            </>
          ) : (
            <>
              <SheetHeader className="border-b border-input">
                <SheetTitle className="flex items-center justify-between">
                  <span className="text-xl font-semibold">Activity</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFriends(true)}
                  >
                    <IconFriends className="!size-4" />
                    Friends
                  </Button>
                </SheetTitle>
                <SheetDescription></SheetDescription>
              </SheetHeader>

              <div className="p-2 flex-1">
                <Tabs
                  value={selectedTab}
                  onValueChange={(value) =>
                    setSelectedTab(
                      value as
                        | "purchases"
                        | "bids"
                        | "offers"
                        | "messages"
                        | "bookmarked",
                    )
                  }
                  className="w-full"
                >
                  <TabsList className="w-full justify-start gap-1 bg-transparent border-b rounded-none">
                    <TabsTrigger
                      value="purchases"
                      className={cn(
                        "-mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                        "data-[state=active]:text-foreground",
                        "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                        "data-[state=active]:bg-muted/50 data-[state=active]:border-input rounded-lg",
                        "mb-2"
                      )}
                    >
                      Purchases
                    </TabsTrigger>
                    <TabsTrigger
                      value="bids"
                      className={cn(
                        "-mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                        "data-[state=active]:text-foreground",
                        "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                        "data-[state=active]:bg-muted/50 data-[state=active]:border-input rounded-lg",
                        "mb-2"
                      )}
                    >
                      Bids
                    </TabsTrigger>
                    <TabsTrigger
                      value="offers"
                      className={cn(
                        "-mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                        "data-[state=active]:text-foreground",
                        "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                        "data-[state=active]:bg-muted/50 data-[state=active]:border-input rounded-lg",
                        "mb-2"
                      )}
                    >
                      Offers
                    </TabsTrigger>
                    <TabsTrigger
                      value="messages"
                      className={cn(
                        "-mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                        "data-[state=active]:text-foreground",
                        "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                        "data-[state=active]:bg-muted/50 data-[state=active]:border-input rounded-lg",
                        "mb-2"
                      )}
                    >
                      Messages
                    </TabsTrigger>
                    <TabsTrigger
                      value="bookmarked"
                      className={cn(
                        "-mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                        "data-[state=active]:text-foreground",
                        "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                        "data-[state=active]:bg-muted/50 data-[state=active]:border-input rounded-lg",
                        "mb-2"
                      )}
                    >
                      Bookmarked
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="purchases" className="mt-2 p-2">
                    <div className="space-y-4">
                      <div className="text-sm text-muted-foreground">
                        No purchases yet
                      </div>

                      {/* TODO: Add purchases */}
                      {/* <div className="flex gap-3 p-3 rounded-lg border border-input hover:bg-accent/30 cursor-pointer">
                        <div className="relative w-20 aspect-square rounded-lg overflow-hidden bg-muted flex items-center justify-center">
                          <IconPhoto className="w-8 h-8 text-muted-foreground" />
                        </div>
                        <div className="flex w-full flex-col gap-1">
                          <div className="flex justify-between">
                            <div className="inline">
                              <div className="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-semibold bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                Pending Review
                              </div>
                            </div>
                          </div>
                          <p className="font-semibold line-clamp-1">
                            Auction #12
                          </p>
                          <div className="flex gap-1">
                            <p className="text-xs text-muted-foreground">
                              Purchased:
                            </p>
                            <p className="text-xs font-semibold">$29.60</p>
                          </div>
                          <div className="flex gap-1">
                            <p className="text-xs text-muted-foreground">
                              Date:
                            </p>
                            <p className="text-xs font-semibold">4/3/2025</p>
                          </div>
                        </div>
                      </div> */}
                    </div>
                  </TabsContent>

                  <TabsContent value="bids" className="mt-2 p-2">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`rounded-full ${selectedFilter === "all" ? "bg-accent text-accent-foreground" : "bg-background hover:bg-zinc-100 dark:hover:bg-zinc-800"}`}
                            onClick={() => setSelectedFilter("all")}
                          >
                            All
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`rounded-full ${selectedFilter === "outbid" ? "bg-accent text-accent-foreground" : "bg-background hover:bg-zinc-100 dark:hover:bg-zinc-800"}`}
                            onClick={() => setSelectedFilter("outbid")}
                          >
                            Outbid
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`rounded-full ${selectedFilter === "winning" ? "bg-accent text-accent-foreground" : "bg-background hover:bg-zinc-100 dark:hover:bg-zinc-800"}`}
                            onClick={() => setSelectedFilter("winning")}
                          >
                            Winning
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`rounded-full ${selectedFilter === "ended" ? "bg-accent text-accent-foreground" : "bg-background hover:bg-zinc-100 dark:hover:bg-zinc-800"}`}
                            onClick={() => setSelectedFilter("ended")}
                          >
                            Recently Ended
                          </Button>
                        </div>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="ml-auto"
                            >
                              <IconFilter className="!size-4" />
                              Sort By
                              <IconChevronDown className="ml-2 h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuRadioGroup
                              value={sortBy}
                              onValueChange={handleSortChange}
                            >
                              <DropdownMenuRadioItem value="default">
                                Default
                              </DropdownMenuRadioItem>
                              <DropdownMenuRadioItem value="lowest">
                                Lowest Price
                              </DropdownMenuRadioItem>
                              <DropdownMenuRadioItem value="highest">
                                Highest Price
                              </DropdownMenuRadioItem>
                              <DropdownMenuRadioItem value="ending">
                                Ending Soonest
                              </DropdownMenuRadioItem>
                            </DropdownMenuRadioGroup>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="flex flex-col items-center justify-center py-12 text-center">
                        <IconPhoto className="h-12 w-12 text-muted-foreground/50" />
                        <h3 className="mt-4 text-lg font-semibold">
                          No active bids
                        </h3>
                        <p className="mt-2 text-sm text-muted-foreground">
                          Browse auctions to place a bid.
                        </p>
                        <Button variant="primary" className="mt-4" size="sm">
                          Browse Auctions
                        </Button>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="offers" className="mt-2 p-2">
                    <div className="space-y-4">
                      {/* Placeholder for offers content */}
                      <div className="text-sm text-muted-foreground">
                        No offers
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="messages" className="mt-2 p-2">
                    <ChatList 
                      onSelectChat={(chat) => setSelectedChat(chat)}
                      selectedChatId={selectedChatId} 
                    />
                  </TabsContent>

                  <TabsContent value="bookmarked" className="mt-2 p-2">
                    <div className="space-y-4">
                      <ActivitySections />
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              {/* Conditional Footer */}
              {selectedTab === "purchases" && (
                <SheetFooter className="border-t border-input p-4">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleLogout}
                  >
                    <IconShoppingCartDown className="mr-2" />
                    Download Purchase History
                  </Button>
                </SheetFooter>
              )}
              {selectedTab === "messages" && (
                <SheetFooter className="border-t border-input p-4">
                  <Button variant="outline" className="w-full">
                    <IconLogout className="mr-2" />
                    Open Messages
                  </Button>
                </SheetFooter>
              )}
            </>
          )}
        </SheetContent>
      </Sheet>
    </>
  );
};
