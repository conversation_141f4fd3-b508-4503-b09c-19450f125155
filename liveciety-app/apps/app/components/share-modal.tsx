import React, { useState } from "react";
import { Search } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { UserAvatar } from "./user-avatar";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import {
  EmailIcon,
  EmailShareButton,
  FacebookIcon,
  FacebookShareButton,
  LinkedinShareButton,
  PinterestIcon,
  PinterestShareButton,
  RedditIcon,
  RedditShareButton,
  TelegramIcon,
  TelegramShareButton,
  ThreadsShareButton,
  TwitterIcon,
  TwitterShareButton,
  VKIcon,
  VKShareButton,
  WhatsappIcon,
  WhatsappShareButton,
  WorkplaceShareButton,
  LinkedinIcon,
  ThreadsIcon,
  WorkplaceIcon
} from "react-share";
import { toast } from "sonner";
import { Textarea } from "@workspace/ui/components/textarea";
import { IconLink } from "@tabler/icons-react";


interface ShareModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  username: string | undefined;
  profileUrl: string;
}

export function ShareModal({ open, onOpenChange, username = "", profileUrl }: ShareModalProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [copied, setCopied] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<Id<"users">[]>([]);
  const [message, setMessage] = useState(`Check out ${username}'s profile: ${profileUrl}`);
  
  const createNotification = useMutation(api.notifications.createNotification);
  const createChat = useMutation(api.chat.createChat);
  const sendMessage = useMutation(api.chat.sendMessage);

  const searchResults = useQuery(api.users.searchUsers, { 
    searchQuery: searchQuery
  });

  const handleCopyLink = () => {
    navigator.clipboard.writeText(profileUrl);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const toggleUserSelection = (userId: Id<"users">) => {
    if (selectedUsers.some(id => id === userId)) {
      setSelectedUsers(selectedUsers.filter(id => id !== userId));
    } else {
      if (selectedUsers.length === 1) {
        setSelectedUsers([userId]);
      } else {
        setSelectedUsers([...selectedUsers, userId]);
      }
    }
  };

  const handleShareWithUsers = async () => {
    if (selectedUsers.length === 0) {
      toast.error("Please select at least one user to share with");
      return;
    }

    try {
      if (selectedUsers.length === 1) {
        const targetUserId = selectedUsers[0];
        
        if (!targetUserId) {
          toast.error("Invalid user selected");
          return;
        }
        
        const chatId = await createChat({
          participantIds: [targetUserId],
          isGroup: false
        });
        
        if (chatId) {
          await sendMessage({
            chatId,
            text: message
          });
          
          await createNotification({
            userId: targetUserId,
            type: "share_profile"
          });
          
          toast.success("Message sent successfully");
        }
      } else {
        await Promise.all(
          selectedUsers.filter(userId => userId).map(async (userId) => {
            await createNotification({
              userId,
              type: "share_profile"
            });
          })
        );
        
        toast.success(`Shared with ${selectedUsers.length} users`);
      }
      
      setSelectedUsers([]);
      setMessage(`Check out ${username}'s profile: ${profileUrl}`);
      onOpenChange(false);
    } catch (error) {
      console.error("Error sharing profile:", error);
      toast.error("Failed to share profile");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share {username}&apos;s profile</DialogTitle>
        </DialogHeader>
        
        <div className="relative mt-2">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <Input 
              className="pl-10 py-5 w-full rounded-full border-none" 
              placeholder="Search users..." 
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="mt-4 overflow-auto max-h-60">
            <div className="grid grid-cols-3 gap-4">
              {searchResults?.users && searchResults.users.length > 0 ? (
                searchResults.users.map((user: any) => (
                  <div 
                    key={user._id} 
                    className={`flex flex-col items-center p-2 rounded-lg cursor-pointer`}
                    onClick={() => toggleUserSelection(user._id)}
                  >
                    <div className="mb-2 relative">
                      <UserAvatar 
                        user={user} 
                        height={64} 
                        width={64}
                      />
                      {selectedUsers.some(id => id === user._id) && (
                        <div className="absolute -top-1 -right-1 bg-blue-500 text-white h-5 w-5 rounded-full flex items-center justify-center text-xs">
                          ✓
                        </div>
                      )}
                    </div>
                    <p className="text-xs text-center truncate w-full">{user.username}</p>
                  </div>
                ))
              ) : searchQuery ? (
                <div className="col-span-3 text-center py-4 text-gray-500">
                  No users found
                </div>
              ) : (
                <div className="col-span-3 text-center py-4 text-gray-500">
                  Search for users to share with
                </div>
              )}
            </div>
          </div>
          
          {selectedUsers.length === 1 && (
            <div className="mt-4">
              <div className="p-3 rounded-lg">
                <p className="text-sm font-medium mb-2">Add a message</p>
                <Textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="resize-none"
                  rows={3}
                  placeholder="Write a message..."
                />
              </div>
            </div>
          )}
          
          {selectedUsers.length > 0 && (
            <div className="mt-4">
              <Button 
                className="w-full" 
                onClick={handleShareWithUsers}
              >
                {selectedUsers.length === 1 
                  ? 'Send message' 
                  : `Share with ${selectedUsers.length} users`}
              </Button>
            </div>
          )}
        </div>
        
        <div className="flex justify-between items-center border-t border-gray-200 mt-6 pt-4 overflow-x-auto gap-1">

          <div className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 h-full w-full flex items-center justify-center rounded-lg">
          <Button
            variant="ghost"
            className="flex flex-col items-center p-2 gap-1 rounded-lg px-2"
            onClick={handleCopyLink}
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <div className="relative w-12 h-12 flex items-center justify-center">
                <div className="absolute inset-0 rounded-full bg-[#7f7f7f]"></div>
                <IconLink className="relative z-10 text-white" size={48} />
              </div>
              <span className="text-xs">{copied ? "Copied!" : "Copy link"}</span>
            </div>
          </Button>
          </div>

          <EmailShareButton 
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          > 
            <div className="flex flex-col items-center p-2 gap-1">
              <EmailIcon size={48} round />
              <span className="text-xs">Email</span>
            </div>
          </EmailShareButton>
          
          <FacebookShareButton 
            url={profileUrl} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <FacebookIcon size={48} round />
              <span className="text-xs">Facebook</span>
            </div>
          </FacebookShareButton>

          <TwitterShareButton 
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <TwitterIcon size={48} round />
              <span className="text-xs">Twitter</span>
            </div>
          </TwitterShareButton>

          <LinkedinShareButton 
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <LinkedinIcon size={48} round />
              <span className="text-xs">LinkedIn</span>
            </div>
          </LinkedinShareButton>
          
          <ThreadsShareButton
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <ThreadsIcon size={48} round />
              <span className="text-xs">Threads</span>
            </div>
          </ThreadsShareButton>

          <RedditShareButton 
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <RedditIcon size={48} round />
              <span className="text-xs">Reddit</span>
            </div>
          </RedditShareButton>

          <WhatsappShareButton 
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <WhatsappIcon size={48} round />
              <span className="text-xs">WhatsApp</span>
            </div>
          </WhatsappShareButton>

          <WorkplaceShareButton
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <WorkplaceIcon size={48} round />
              <span className="text-xs">Workplace</span>
            </div>
          </WorkplaceShareButton>

          <VKShareButton 
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <VKIcon size={48} round />
              <span className="text-xs">VK</span>
            </div>
          </VKShareButton>

          <TelegramShareButton 
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg"
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <TelegramIcon size={48} round />
              <span className="text-xs">Telegram</span>
            </div>
          </TelegramShareButton>

          <PinterestShareButton 
            url={profileUrl} 
            title={`Check out ${username}'s profile`} 
            className="hover:!bg-zinc-100 dark:hover:!bg-zinc-800/50 rounded-lg" 
            media={profileUrl}
            style={{ backgroundColor: 'transparent', border: 'none', padding: '0px', font: 'inherit', color: 'inherit', cursor: 'pointer' }}
          >
            <div className="flex flex-col items-center p-2 gap-1">
              <PinterestIcon size={48} round />
              <span className="text-xs">Pinterest</span>
            </div>
          </PinterestShareButton>
        </div>
      </DialogContent>
    </Dialog>
  );
} 