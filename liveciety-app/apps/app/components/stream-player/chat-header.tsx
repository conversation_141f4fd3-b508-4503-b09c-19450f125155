"use client";

import { Skeleton } from "@workspace/ui/components/skeleton";

import { ChatToggle } from "./chat-toggle";
import { VariantToggle } from "./variant-toggle";

export const ChatHeader = () => {
  return (
    <div className="relative p-3.5 border-b flex items-center justify-center">
      <div className="hidden lg:block">
        <ChatToggle />
      </div>
      <p className="font-semibold text-primary text-center z-10 w-full">Stream Chat</p>
      <div className="">
        <VariantToggle />
      </div>
    </div>
  );
};

export const ChatHeaderSkeleton = () => {
  return (
    <div className="relative p-3 border-b hidden md:block">
      <Skeleton className="absolute h-6 w-6 left-3 top-3" />
      <Skeleton className="w-28 h-6 mx-auto" />
    </div>
  );
};
