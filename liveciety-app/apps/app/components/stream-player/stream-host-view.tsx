"use client";

import { useEffect, useState, useRef } from "react";
import { useAction, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import {
  RoomEvent,
  Room,
  LocalTrackPublication,
  Track,
  ConnectionState,
  DisconnectReason,
  VideoPresets,
} from "livekit-client";
import { toast } from "sonner";
import { Volume2, VolumeX, Maximize, Minimize, Users, Video, VideoOff, Mic, MicOff } from "lucide-react";

const LIVEKIT_WS_URL = process.env.NEXT_PUBLIC_LIVEKIT_WS_URL;

interface StreamHostViewProps {
  streamId: Id<"streams">;
  hostIdentity: Id<"users">;
  hostName: string;
}

export function StreamHostView({ streamId, hostIdentity, hostName }: StreamHostViewProps) {
  const generateHostToken = useAction(api.integration.livekit.generateHostToken);
  const getParticipantCountAction = useAction(api.integration.livekit.getRoomParticipantsCount);

  const [room, setRoom] = useState<Room | undefined>();
  const [token, setToken] = useState<string | undefined>();
  const [error, setError] = useState<string | undefined>();
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isLive, setIsLive] = useState(false);
  const [viewerCount, setViewerCount] = useState<number | null>(null);

  const videoContainerRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const isConnectingRef = useRef(false);
  const roomRef = useRef<Room | undefined>(undefined);
  const tokenRef = useRef<string | undefined>(undefined);

  const [isMuted, setIsMuted] = useState(true);
  const [volume, setVolume] = useState(0.5);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isCameraEnabled, setIsCameraEnabled] = useState(false);
  const [isMicEnabled, setIsMicEnabled] = useState(false);

  const [isReconnecting, setIsReconnecting] = useState(false);
  const [lastParticipantUpdate, setLastParticipantUpdate] = useState(0);
  const [connectionStabilityTimer, setConnectionStabilityTimer] = useState<NodeJS.Timeout | null>(null);
  const [isConnectionStable, setIsConnectionStable] = useState(false);

  const streamDetails = useQuery(api.streams.getStreamById, { streamId });

  // Add debouncing and connection state tracking
  const [isTokenGenerating, setIsTokenGenerating] = useState(false);
  const connectionTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const tokenGenerationTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const participantUpdateTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // Rate limiting constants
  const PARTICIPANT_UPDATE_DEBOUNCE = 5000; // 5 seconds between viewer count updates
  const CONNECTION_STABILITY_DELAY = 10000; // 10 seconds to consider connection stable
  const MIN_CONNECTION_TIME = 30000; // 30 seconds minimum before allowing reconnection

  useEffect(() => {
    if (streamDetails?.isLive && room?.state === ConnectionState.Connected) {
      const roomName = `stream-${streamId}`;
      getParticipantCountAction({ roomName })
        .then(setViewerCount)
        .catch(err => console.error("Failed to fetch viewer count:", err));

      const intervalId = setInterval(() => {
        if (room?.state === ConnectionState.Connected && streamDetails?.isLive) {
          getParticipantCountAction({ roomName })
            .then(setViewerCount)
            .catch(err => console.error("Failed to refresh viewer count:", err));
        }
      }, 30000);
      return () => clearInterval(intervalId);
    } else {
      setViewerCount(null);
    }
  }, [streamDetails?.isLive, streamId, getParticipantCountAction, room?.state]);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.muted = isMuted;
      videoRef.current.volume = volume;
    }
  }, [isMuted, volume]);

  const toggleMute = () => setIsMuted(!isMuted);

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    if (newVolume > 0 && isMuted) setIsMuted(false);
    else if (newVolume === 0 && !isMuted) setIsMuted(true);
  };

  const toggleFullscreen = () => {
    if (!videoContainerRef.current) return;
    if (!document.fullscreenElement) {
      videoContainerRef.current.requestFullscreen().catch(err => {
        toast.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
      });
    } else {
      document.exitFullscreen();
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => setIsFullscreen(!!document.fullscreenElement);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Token generation effect - with debouncing to prevent multiple generations
  useEffect(() => {
    if (tokenGenerationTimeoutRef.current) {
      clearTimeout(tokenGenerationTimeoutRef.current);
    }

    tokenGenerationTimeoutRef.current = setTimeout(() => {
      console.log("[StreamHostView] Token generation effect triggered");
      if (!LIVEKIT_WS_URL) {
        setError("LiveKit WebSocket URL is not configured.");
        toast.error("LiveKit URL not configured.");
        return;
      }
      if (!streamId || !streamDetails || isTokenGenerating) return;

      console.log("[StreamHostView] Generating host token for streamId:", streamId);
      console.log("[StreamHostView] Current user identity:", hostIdentity);
      
      setIsTokenGenerating(true);
      
      generateHostToken({ streamId })
        .then((newToken) => {
          console.log("[StreamHostView] Host token generated successfully");
          
          try {
            const tokenParts = newToken.split('.');
            if (tokenParts.length >= 2 && tokenParts[1]) {
              const tokenPayload = JSON.parse(atob(tokenParts[1]));
              console.log("[StreamHostView] Token payload:", {
                identity: tokenPayload.sub || tokenPayload.jti,
                name: tokenPayload.name,
                room: tokenPayload.video?.room,
                canPublish: tokenPayload.video?.canPublish,
                exp: new Date(tokenPayload.exp * 1000).toISOString()
              });
              
              const tokenIdentity = tokenPayload.sub || tokenPayload.jti;
              if (tokenIdentity !== hostIdentity) {
                console.error("[StreamHostView] CRITICAL: Received token with wrong identity!");
                setError(`Authentication error: Wrong user identity in token. Please refresh and try again.`);
                toast.error("Authentication error. Please refresh the page.");
                setIsTokenGenerating(false);
                return;
              }
              
              if (!tokenPayload.video?.canPublish) {
                console.error("[StreamHostView] CRITICAL: Token missing publish permissions!");
                setError(`Permission error: Token missing streaming permissions. Please refresh and try again.`);
                toast.error("Permission error. Please refresh the page.");
                setIsTokenGenerating(false);
                return;
              }
              
              const expectedRoom = `stream-${streamId}`;
              if (tokenPayload.video?.room !== expectedRoom) {
                console.error("[StreamHostView] CRITICAL: Token room mismatch!");
                setError(`Room mismatch error. Please refresh and try again.`);
                toast.error("Room configuration error. Please refresh the page.");
                setIsTokenGenerating(false);
                return;
              }
              
              console.log("[StreamHostView] ✅ Token validation passed");
            }
          } catch (e) {
            console.error("[StreamHostView] Failed to decode token:", e);
          }
          
          // Only update token if it's actually different to prevent unnecessary re-renders
          if (tokenRef.current !== newToken) {
            console.log("[StreamHostView] Token updated - will trigger connection");
            tokenRef.current = newToken;
            setToken(newToken);
          }
          setIsTokenGenerating(false);
        })
        .catch((err) => {
          console.error("[StreamHostView] Failed to get host token:", err);
          setError(`Failed to get stream token: ${err.message || err}`);
          toast.error(`Error fetching stream token: ${err.message || err}`);
          setIsTokenGenerating(false);
        });
    }, 300); // 300ms debounce

    return () => {
      if (tokenGenerationTimeoutRef.current) {
        clearTimeout(tokenGenerationTimeoutRef.current);
      }
    };
  }, [streamId, generateHostToken, streamDetails?.status, hostIdentity]);

  // Main connection effect - with improved timing and guards
  useEffect(() => {
    // Clear any existing connection timeout
    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current);
    }

    console.log("[StreamHostView] Connection effect triggered");
    console.log("[StreamHostView] streamDetails?.isLive:", streamDetails?.isLive);
    console.log("[StreamHostView] token state:", token ? "✓" : "✗");
    console.log("[StreamHostView] tokenRef.current:", tokenRef.current ? "✓" : "✗");
    console.log("[StreamHostView] error:", error);
    console.log("[StreamHostView] Current room state:", roomRef.current?.state);
    console.log("[StreamHostView] isConnectingRef.current:", isConnectingRef.current);

    // Disconnect if stream is not live
    if (!streamDetails?.isLive) {
      if (roomRef.current && !isConnectingRef.current) {
        console.log("[StreamHostView] Stream is not live, disconnecting room");
        roomRef.current.disconnect(true);
        setIsLive(false);
        setRoom(undefined);
        roomRef.current = undefined;
        setIsConnected(false);
        setIsConnecting(false);
        isConnectingRef.current = false;
      }
      return;
    }

    // Don't connect if we have errors or missing prerequisites
    if (!token || !tokenRef.current || !LIVEKIT_WS_URL) {
      console.log("[StreamHostView] Prerequisites not met - token state:", !!token, "tokenRef:", !!tokenRef.current, "LIVEKIT_WS_URL:", !!LIVEKIT_WS_URL, "error:", error);
      return;
    }

    // Skip connection if we have authentication errors (these need manual refresh)
    if (error && (error.includes('Authentication') || error.includes('Permission') || error.includes('Room mismatch'))) {
      console.log("[StreamHostView] Authentication/Permission error present, skipping connection:", error);
      return;
    }

    // Don't recreate room if already in a good state
    if (roomRef.current && roomRef.current.state === ConnectionState.Connected && isConnected) {
      console.log("[StreamHostView] Room already exists in connected state - skipping recreation");
      return;
    }

    // Prevent multiple simultaneous connection attempts
    if (isConnectingRef.current) {
      console.log("[StreamHostView] Already connecting, skipping");
      return;
    }

    // Add debounced connection to prevent rapid reconnections from React strict mode
    connectionTimeoutRef.current = setTimeout(() => {
      // Recheck prerequisites after delay
      if (!streamDetails?.isLive || !token || !tokenRef.current || !LIVEKIT_WS_URL) {
        console.log("[StreamHostView] Prerequisites changed during delay, aborting connection");
        return;
      }

      // Skip connection if we have authentication errors (these need manual refresh)
      if (error && (error.includes('Authentication') || error.includes('Permission') || error.includes('Room mismatch'))) {
        console.log("[StreamHostView] Authentication/Permission error present during delay, aborting connection:", error);
        return;
      }

      // Final check for existing connection
      if (roomRef.current && roomRef.current.state === ConnectionState.Connected && isConnected) {
        console.log("[StreamHostView] Room already connected during delay, aborting recreation");
        return;
      }

      // Prevent multiple simultaneous connection attempts after delay
      if (isConnectingRef.current) {
        console.log("[StreamHostView] Already connecting during delay, aborting");
        return;
      }

      // Clean up any existing room before creating new one
      if (roomRef.current) {
        console.log("[StreamHostView] Cleaning up existing room before creating new one");
        try {
          roomRef.current.disconnect(true);
        } catch (e) {
          console.log("[StreamHostView] Error during room cleanup:", e);
        }
        roomRef.current = undefined;
        setRoom(undefined);
      }

      console.log("[StreamHostView] Creating new Room instance for LIVE stream");
      isConnectingRef.current = true;
      setIsConnecting(true);
      setError(undefined); // Clear any previous errors
      
      const newRoom = new Room({
        adaptiveStream: true,
        dynacast: true,
        videoCaptureDefaults: {
          resolution: { width: 1280, height: 720, frameRate: 30 },
          facingMode: 'user'
        },
        audioCaptureDefaults: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
        // Less aggressive reconnection policy to prevent rapid reconnections
        reconnectPolicy: {
          nextRetryDelayInMs: (context) => {
            // More conservative delays with longer backoff
            const baseDelay = Math.min(2000 * Math.pow(2, context.retryCount), 30000);
            const jitter = Math.random() * 2000; // More jitter to avoid thundering herd
            console.log(`[StreamHostView] Reconnection attempt ${context.retryCount + 1}, delay: ${baseDelay + jitter}ms`);
            return baseDelay + jitter;
          },
        },
      });
      
      setRoom(newRoom);
      roomRef.current = newRoom;
      setIsLive(false);

      const connectAndSetup = async () => {
        try {
          console.log("[StreamHostView] Connecting to LiveKit room...");
          
          // Connect with explicit connection options
          await newRoom.connect(LIVEKIT_WS_URL, tokenRef.current!, {
            autoSubscribe: false,
            rtcConfig: {
              iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
              ],
            },
          });
          
          console.log("[StreamHostView] Connected to LiveKit room");
          
          // Wait for connection to be fully established and permissions to sync
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Verify we have publish permissions before proceeding
          let retryCount = 0;
          const maxRetries = 5;
          let hasPublishPermission = false;
          
          while (retryCount < maxRetries && !hasPublishPermission) {
            hasPublishPermission = newRoom.localParticipant.permissions?.canPublish ?? false;
            console.log(`[StreamHostView] Permission check attempt ${retryCount + 1}:`, hasPublishPermission);
            
            if (!hasPublishPermission) {
              console.log("[StreamHostView] Waiting for permissions to sync...");
              await new Promise(resolve => setTimeout(resolve, 1000));
              retryCount++;
            } else {
              break;
            }
          }
          
          if (!hasPublishPermission) {
            console.error("[StreamHostView] CRITICAL: Publish permissions never synced after connection");
            setError("Failed to establish streaming permissions. Please refresh the page and try again.");
            toast.error("Streaming permissions not available. Please refresh the page.");
            setIsConnecting(false);
            setIsConnected(false);
            isConnectingRef.current = false;
            return;
          }
          
          console.log("[StreamHostView] ✅ Permissions verified and ready for streaming");
          setIsConnecting(false);
          setIsConnected(true);
          isConnectingRef.current = false;
          setError(undefined);
          toast.success("Connected and ready to stream!");
          
        } catch (e: unknown) {
          console.error("[StreamHostView] Failed to connect to LiveKit room:", e);
          const errorMessage = e instanceof Error ? e.message : 'Unknown error';
          
          // Don't set error for client-initiated disconnects or cleanup-related errors
          if (!errorMessage.includes('Client initiated disconnect') && 
              !errorMessage.includes('abort') && 
              !errorMessage.includes('cancelled')) {
            if (errorMessage.includes('could not establish pc connection')) {
              console.log("[StreamHostView] Connection failed, LiveKit will retry with different region");
              toast.info("Connecting to stream... (trying different server)");
            } else {
              setError(`Failed to connect: ${errorMessage}`);
              toast.error(`Connection failed: ${errorMessage}`);
            }
          } else {
            console.log("[StreamHostView] Connection aborted by cleanup or cancelled, not treating as error");
          }
          
          setIsConnecting(false);
          setIsConnected(false);
          isConnectingRef.current = false;
          
          // Only clear room reference for real errors, not cleanup-related ones
          if (!errorMessage.includes('Client initiated disconnect') && 
              !errorMessage.includes('abort') && 
              !errorMessage.includes('cancelled') &&
              !errorMessage.includes('could not establish pc connection')) {
            roomRef.current = undefined;
          }
        }
      };

      const handleLocalTrackPublished = (pub: LocalTrackPublication) => {
        console.log("[StreamHostView] Local track published:", pub.source, pub.trackSid);
        if (pub.source === Track.Source.Camera && pub.videoTrack && videoRef.current) {
          console.log("[StreamHostView] Attaching local camera track");
          pub.videoTrack.attach(videoRef.current);
          setIsLive(true);
        }
      };

      const handleLocalTrackUnpublished = (pub: LocalTrackPublication) => {
        console.log("[StreamHostView] Local track unpublished:", pub.source);
        if (pub.source === Track.Source.Camera && videoRef.current && pub.track) {
          pub.track.detach(videoRef.current);
        }
        
        // Only update UI state if the room is still connected and this wasn't due to connection issues
        if (newRoom.state === ConnectionState.Connected) {
          // Check if this was a user-initiated action or connection issue
          // If we're in middle of reconnection or the connection quality is poor, don't update UI state
          const isConnectionIssue = newRoom.state !== ConnectionState.Connected || 
                                  isReconnecting;
          
          if (!isConnectionIssue) {
            if (pub.source === Track.Source.Camera) {
              setIsCameraEnabled(false);
              console.log("[StreamHostView] Camera disabled by user");
            } else if (pub.source === Track.Source.Microphone) {
              setIsMicEnabled(false);
              console.log("[StreamHostView] Microphone disabled by user");
            }
          } else {
            console.log("[StreamHostView] Track unpublished due to connection issues, keeping UI state");
          }
        } else {
          console.log("[StreamHostView] Track unpublished during disconnect/reconnect, keeping UI state");
        }
        
        // Update live state only if no tracks are published
        const camPub = newRoom.localParticipant.getTrackPublication(Track.Source.Camera);
        const micPub = newRoom.localParticipant.getTrackPublication(Track.Source.Microphone);
        if ((!camPub || !camPub.track) && (!micPub || !micPub.track)) {
          // Only set not live if room is stable and this wasn't a connection issue
          if (newRoom.state === ConnectionState.Connected && !isReconnecting) {
            setIsLive(false);
            console.log("[StreamHostView] All tracks unpublished, no longer live");
          }
        }
      };

      // Set up event listeners
      newRoom.on(RoomEvent.LocalTrackPublished, handleLocalTrackPublished);
      newRoom.on(RoomEvent.LocalTrackUnpublished, handleLocalTrackUnpublished);
      
      newRoom.on(RoomEvent.Disconnected, (reason) => {
        console.log("[StreamHostView] Disconnected from room:", reason);
        setIsConnecting(false);
        setIsConnected(false);
        setIsLive(false);
        isConnectingRef.current = false;
        
        // Only clear reconnection state if this is a final disconnect (not part of reconnection)
        if (reason === DisconnectReason.CLIENT_INITIATED || 
            reason === DisconnectReason.DUPLICATE_IDENTITY ||
            reason === DisconnectReason.SERVER_SHUTDOWN) {
          setIsReconnecting(false);
          roomRef.current = undefined;
          console.log("[StreamHostView] Final disconnect, clearing room reference");
        } else {
          console.log("[StreamHostView] Temporary disconnect, keeping room reference for reconnection");
        }
        
        if (reason !== DisconnectReason.CLIENT_INITIATED) {
          toast.info("Disconnected from streaming room");
        }
      });

      newRoom.on(RoomEvent.Reconnecting, () => {
        console.log("[StreamHostView] Reconnecting to room...");
        setIsReconnecting(true);
        setIsConnecting(true);
        setIsConnected(false);
        setIsConnectionStable(false);
        
        // Clear stability timer during reconnection
        if (connectionStabilityTimer) {
          clearTimeout(connectionStabilityTimer);
          setConnectionStabilityTimer(null);
        }
        
        toast.info("Reconnecting...", { duration: 2000 });
      });

      newRoom.on(RoomEvent.Reconnected, async () => {
        console.log("[StreamHostView] Reconnected to room");
        setIsReconnecting(false);
        setIsConnecting(false);
        setIsConnected(true);
        toast.success("Reconnected!", { duration: 2000 });

        // Wait for permissions to be re-established with shorter delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Only restart tracks if connection is stable to prevent rapid republishing
        // Remove aggressive track restart that was causing instability
        console.log("[StreamHostView] Reconnection complete, allowing natural track recovery");
        
        // Start new stability timer after reconnection
        if (connectionStabilityTimer) {
          clearTimeout(connectionStabilityTimer);
        }
        const timer = setTimeout(() => {
          setIsConnectionStable(true);
          console.log("[StreamHostView] Connection marked as stable after reconnection");
        }, CONNECTION_STABILITY_DELAY);
        setConnectionStabilityTimer(timer);
      });

      newRoom.on(RoomEvent.ConnectionQualityChanged, (quality, participant) => {
        if (participant?.isLocal) {
          console.log("[StreamHostView] Connection quality:", quality);
          
          // Only show warnings for sustained poor quality to avoid alarm fatigue
          if (quality === 'poor') {
            console.warn("[StreamHostView] Poor connection quality detected");
            // Only show toast if connection has been stable for a while to avoid spam
            if (isConnectionStable) {
              toast.warning("Poor connection quality detected", { duration: 3000 });
            }
          } 
          
          // Remove aggressive track republishing on quality improvement
          // This was causing unnecessary disconnections and republishing
          // LiveKit handles track management automatically during poor quality
        }
      });

      newRoom.on(RoomEvent.ConnectionStateChanged, (state) => {
        console.log("[StreamHostView] Connection state changed to:", state);
        
        if (state === ConnectionState.Disconnected) {
          console.log("[StreamHostView] Connection state is disconnected");
          setIsConnected(false);
          setIsConnecting(false);
          setIsConnectionStable(false);
          if (connectionStabilityTimer) {
            clearTimeout(connectionStabilityTimer);
            setConnectionStabilityTimer(null);
          }
        } else if (state === ConnectionState.Connecting) {
          setIsConnecting(true);
          setIsConnected(false);
          setIsConnectionStable(false);
        } else if (state === ConnectionState.Connected) {
          setIsConnecting(false);
          setIsConnected(true);
          
          // Start stability timer - connection is considered stable after a delay
          if (connectionStabilityTimer) {
            clearTimeout(connectionStabilityTimer);
          }
          const timer = setTimeout(() => {
            setIsConnectionStable(true);
            console.log("[StreamHostView] Connection marked as stable");
          }, CONNECTION_STABILITY_DELAY);
          setConnectionStabilityTimer(timer);
        }
      });

      newRoom.on(RoomEvent.ParticipantConnected, (participant) => {
        console.log("[StreamHostView] Participant connected:", participant.identity);
        
        // Rate limit participant updates to prevent connection flooding
        const now = Date.now();
        if (now - lastParticipantUpdate < PARTICIPANT_UPDATE_DEBOUNCE) {
          console.log("[StreamHostView] Participant update rate limited, skipping viewer count update");
          return;
        }
        
        setLastParticipantUpdate(now);
        
        // Debounce viewer count updates to prevent rapid API calls
        if (participantUpdateTimeoutRef.current) {
          clearTimeout(participantUpdateTimeoutRef.current);
        }
        
        participantUpdateTimeoutRef.current = setTimeout(() => {
          if (streamDetails?.isLive && roomRef.current?.state === ConnectionState.Connected && isConnectionStable) {
            const roomName = `stream-${streamId}`;
            getParticipantCountAction({ roomName })
              .then(setViewerCount)
              .catch(err => console.warn("Failed to update viewer count:", err.message));
          }
        }, 2000); // 2 second debounce
      });

      newRoom.on(RoomEvent.ParticipantDisconnected, (participant) => {
        console.log("[StreamHostView] Participant disconnected:", participant.identity);
        
        // Rate limit participant updates to prevent connection flooding
        const now = Date.now();
        if (now - lastParticipantUpdate < PARTICIPANT_UPDATE_DEBOUNCE) {
          console.log("[StreamHostView] Participant update rate limited, skipping viewer count update");
          return;
        }
        
        setLastParticipantUpdate(now);
        
        // Debounce viewer count updates to prevent rapid API calls
        if (participantUpdateTimeoutRef.current) {
          clearTimeout(participantUpdateTimeoutRef.current);
        }
        
        participantUpdateTimeoutRef.current = setTimeout(() => {
          if (streamDetails?.isLive && roomRef.current?.state === ConnectionState.Connected && isConnectionStable) {
            const roomName = `stream-${streamId}`;
            getParticipantCountAction({ roomName })
              .then(setViewerCount)
              .catch(err => console.warn("Failed to update viewer count:", err.message));
          }
        }, 2000); // 2 second debounce
      });

      // Start connection
      connectAndSetup();

    }, 150); // 150ms debounce for host connections

    // Cleanup function
    return () => {
      console.log("[StreamHostView] Cleanup - clearing timeouts and disconnecting room");
      
      // Clear connection timeout
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
        connectionTimeoutRef.current = undefined;
      }
      
      // Clear all timeouts to prevent memory leaks
      if (participantUpdateTimeoutRef.current) {
        clearTimeout(participantUpdateTimeoutRef.current);
        participantUpdateTimeoutRef.current = undefined;
      }
      if (connectionStabilityTimer) {
        clearTimeout(connectionStabilityTimer);
        setConnectionStabilityTimer(null);
      }

      // Only cleanup if not actively connecting to prevent race conditions
      if (!isConnectingRef.current) {
        if (roomRef.current) {
          try {
            roomRef.current.disconnect(true);
          } catch (e) {
            console.log("[StreamHostView] Error during cleanup disconnect:", e);
          }
          roomRef.current = undefined;
        }
        setRoom(undefined);
        setIsLive(false);
        setIsConnecting(false);
        setIsConnected(false);
      } else {
        console.log("[StreamHostView] Skipping cleanup - connection in progress");
      }
      
      isConnectingRef.current = false;
      setIsConnectionStable(false);
      setIsReconnecting(false);
    };
  }, [streamDetails?.isLive, error, token]);

  // Component unmount cleanup
  useEffect(() => {
    return () => {
      console.log("[StreamHostView] Component unmounting - clearing all timeouts");
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
      }
      if (tokenGenerationTimeoutRef.current) {
        clearTimeout(tokenGenerationTimeoutRef.current);
      }
      if (participantUpdateTimeoutRef.current) {
        clearTimeout(participantUpdateTimeoutRef.current);
      }
      if (connectionStabilityTimer) {
        clearTimeout(connectionStabilityTimer);
      }
    };
  }, []);

  const toggleCamera = async () => {
    if (!roomRef.current) {
      toast.error("Room not connected");
      return;
    }

    if (roomRef.current.state !== ConnectionState.Connected) {
      toast.error("Please wait for connection to be established");
      return;
    }

    let hasPermission = false;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries && !hasPermission) {
      hasPermission = roomRef.current.localParticipant.permissions?.canPublish ?? false;
      if (!hasPermission) {
        console.log(`[StreamHostView] Permission retry ${retryCount + 1} for camera`);
        await new Promise(resolve => setTimeout(resolve, 500));
        retryCount++;
      } else {
        break;
      }
    }

    if (!hasPermission) {
      console.error("[StreamHostView] No publish permissions available for camera");
      toast.error("Publishing not allowed. Please refresh the page and try again.");
      return;
    }

    try {
      const enabled = !isCameraEnabled;
      console.log("[StreamHostView] Toggling camera to:", enabled);

      if (enabled) {
        // Additional permission sync delay
        await new Promise(resolve => setTimeout(resolve, 200));
        
        try {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const audioContext = (roomRef.current.engine as any).audioContext as AudioContext;
          if (audioContext?.state === "suspended") {
            await audioContext.resume();
            console.log("[StreamHostView] Audio context resumed");
          }
        } catch (audioError) {
          console.warn("[StreamHostView] Failed to resume audio context:", audioError);
        }
      }

      // Use standard LiveKit methods with better error handling
      await roomRef.current.localParticipant.setCameraEnabled(enabled);
      setIsCameraEnabled(enabled);

      console.log("[StreamHostView] Camera", enabled ? "enabled and published" : "disabled");
    } catch (e) {
      console.error("Error toggling camera:", e);
      const errorMessage = e instanceof Error ? e.message : 'Unknown error';
      
      if (errorMessage.includes('insufficient permissions')) {
        toast.error("Permission denied. Please refresh the page and try again.");
        setError("Streaming permissions lost. Please refresh the page.");
      } else {
        toast.error(`Failed to toggle camera: ${errorMessage}`);
      }
    }
  };

  const toggleMicrophone = async () => {
    if (!roomRef.current) {
      toast.error("Room not connected");
      return;
    }

    if (roomRef.current.state !== ConnectionState.Connected) {
      toast.error("Please wait for connection to be established");
      return;
    }

    // Double-check permissions with retry mechanism
    let hasPermission = false;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries && !hasPermission) {
      hasPermission = roomRef.current.localParticipant.permissions?.canPublish ?? false;
      if (!hasPermission) {
        console.log(`[StreamHostView] Permission retry ${retryCount + 1} for microphone`);
        await new Promise(resolve => setTimeout(resolve, 500));
        retryCount++;
      } else {
        break;
      }
    }

    if (!hasPermission) {
      console.error("[StreamHostView] No publish permissions available for microphone");
      toast.error("Publishing not allowed. Please refresh the page and try again.");
      return;
    }

    try {
      const enabled = !isMicEnabled;
      console.log("[StreamHostView] Toggling microphone to:", enabled);

      if (enabled) {
        // Additional permission sync delay
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Use standard LiveKit methods with better error handling
      await roomRef.current.localParticipant.setMicrophoneEnabled(enabled);
      setIsMicEnabled(enabled);

      console.log("[StreamHostView] Microphone", enabled ? "enabled and published" : "disabled");
    } catch (e) {
      console.error("Error toggling microphone:", e);
      const errorMessage = e instanceof Error ? e.message : 'Unknown error';
      
      if (errorMessage.includes('insufficient permissions')) {
        toast.error("Permission denied. Please refresh the page and try again.");
        setError("Streaming permissions lost. Please refresh the page.");
      } else {
        toast.error(`Failed to toggle microphone: ${errorMessage}`);
      }
    }
  };

  if (!streamDetails) {
    return <div className="p-4 text-center">Loading stream details...</div>;
  }

  if (error) {
    return <div className="text-red-500 p-4 text-center text-xl bg-red-100 border border-red-500 rounded-md">{error}</div>;
  }

  if (!token && streamDetails.isLive) {
    return <div className="p-4 text-center">Initializing stream session...</div>;
  }

  let statusMessage = "Stream is not live yet.";
  if (streamDetails.isLive) {
    if (isConnecting) statusMessage = "Connecting to streaming room...";
    else if (roomRef.current?.state === ConnectionState.Connected && isConnected) {
      if (isLive) statusMessage = "You are LIVE!";
      else statusMessage = "Connected. Start your camera to go live.";
    } else if (roomRef.current?.state === ConnectionState.Connecting) {
      statusMessage = "Establishing connection...";
    } else statusMessage = "Waiting to connect...";
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold">{streamDetails.title}</h2>
          {streamDetails.isLive && viewerCount !== null && (
            <div className="flex items-center text-sm text-gray-400">
              <Users size={16} className="mr-1"/> {viewerCount} viewer(s)
            </div>
          )}
        </div>
      </div>

      <div ref={videoContainerRef} className="w-full aspect-video bg-black rounded-md shadow-2xl mb-4 relative overflow-hidden group">
        <video ref={videoRef} autoPlay playsInline className="w-full h-full object-contain"></video>

        {streamDetails.isLive && roomRef.current?.state === ConnectionState.Connected && (
          <div className="absolute bottom-0 left-0 right-0 p-2 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button onClick={toggleMute} className="text-white p-1.5 hover:bg-white/20 rounded-full">
                {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.05"
                value={isMuted ? 0 : volume}
                onChange={handleVolumeChange}
                className="w-20 h-1.5 accent-primary cursor-pointer"
              />
            </div>
            <button onClick={toggleFullscreen} className="text-white p-1.5 hover:bg-white/20 rounded-full">
              {isFullscreen ? <Minimize size={20} /> : <Maximize size={20} />}
            </button>
          </div>
        )}

        {!isLive && streamDetails.isLive && roomRef.current?.state === ConnectionState.Connected && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
            <div className="text-center">
              <p className="text-xl text-white mb-4">Ready to stream!</p>
              <p className="text-gray-300 mb-4">Click the camera button to start broadcasting</p>
            </div>
          </div>
        )}
      </div>

      <p className={`mb-4 ${isLive ? 'text-green-400 animate-pulse' : 'text-yellow-400'}`}>
        {statusMessage}
      </p>

      {streamDetails.isLive && roomRef.current?.state === ConnectionState.Connected && (
        <div className="flex gap-2 mb-4">
          <button
            onClick={toggleCamera}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-semibold ${
              isCameraEnabled
                ? 'bg-green-500 hover:bg-green-600 text-white'
                : 'bg-gray-500 hover:bg-gray-600 text-white'
            }`}
          >
            {isCameraEnabled ? <Video size={20} /> : <VideoOff size={20} />}
            {isCameraEnabled ? 'Camera On' : 'Camera Off'}
          </button>

          <button
            onClick={toggleMicrophone}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-semibold ${
              isMicEnabled
                ? 'bg-green-500 hover:bg-green-600 text-white'
                : 'bg-gray-500 hover:bg-gray-600 text-white'
            }`}
          >
            {isMicEnabled ? <Mic size={20} /> : <MicOff size={20} />}
            {isMicEnabled ? 'Mic On' : 'Mic Off'}
          </button>
        </div>
      )}
    </div>
  );
}
