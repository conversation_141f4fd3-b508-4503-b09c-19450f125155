"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

import { cn } from "@workspace/ui/lib/utils";
import { Input } from "@workspace/ui/components/input";
import { Button } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";

import { ChatInfo } from "./chat-info";

interface ChatFormProps {
  streamId: Id<"streams">;
  value: string;
  onChange: (value: string) => void;
  isFollowersOnly: boolean;
  isFollowing: boolean;
  isDelayed: boolean;
  error?: string | null;
}

export const ChatForm = ({
  streamId,
  value,
  onChange,
  isFollowersOnly,
  isFollowing,
  isDelayed,
  error: externalError,
}: ChatFormProps) => {
  const [isDelayBlocked, setIsDelayBlocked] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const sendMessage = useMutation(api.streams.sendStreamMessage);

  const isFollowersOnlyAndNotFollowing = isFollowersOnly && !isFollowing;
  const isDisabled =
    isDelayBlocked || isFollowersOnlyAndNotFollowing;

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setError(null);
    if (!value || isDisabled) return;

    const send = async () => {
      try {
        await sendMessage({ streamId, message: value });
        onChange("");
      } catch (err: any) {
        setError(err?.message || "Failed to send message");
      }
    };

    if (isDelayed && !isDelayBlocked) {
      setIsDelayBlocked(true);
      setTimeout(() => {
        setIsDelayBlocked(false);
        send();
      }, 3000);
    } else {
      send();
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="flex flex-col items-center gap-y-4 p-3"
    >
      <div className="w-full">
        <ChatInfo isDelayed={isDelayed} isFollowersOnly={isFollowersOnly} />
        <Input
          onChange={(e) => onChange(e.target.value)}
          value={value}
          disabled={isDisabled}
          placeholder="Send a message"
          className={cn(
            "border-white/10",
            (isFollowersOnly || isDelayed) && "rounded-t-none border-t-0"
          )}
        />
        {(error || externalError) && (
          <div className="text-red-500 text-xs mt-1">{error || externalError}</div>
        )}
      </div>
      <div className="ml-auto">
        <Button type="submit" variant="primary" size="sm" disabled={isDisabled}>
          Chat
        </Button>
      </div>
    </form>
  );
};

export const ChatFormSkeleton = () => {
  return (
    <div className="flex flex-col items-center gap-y-4 p-3">
      <Skeleton className="w-full h-10" />
      <div className="flex items-center gap-x-2 ml-auto">
        <Skeleton className="h-7 w-7" />
        <Skeleton className="h-7 w-12" />
      </div>
    </div>
  );
};
