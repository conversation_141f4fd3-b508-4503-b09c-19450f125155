"use client";

import { Skeleton } from "@workspace/ui/components/skeleton";
import { VerifiedMark } from "@/components/verified-mark";
import { UserAvatar, UserAvatarSkeleton } from "@/components/user-avatar";
import { User as UserType } from "@/lib/types";

import { Actions, ActionsSkeleton } from "./actions";
import { Button } from "@workspace/ui/components/button";
import { IconChevronLeft } from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import { ChatToggle } from "./chat-toggle";
import { cn } from "@workspace/ui/lib/utils";
import { useEffect } from "react";
import { useMutation, useQuery, useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import React from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@workspace/ui/components/dialog";
import Link from "next/link";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { ViewerCount as LiveViewerCount } from "@/components/viewer-count";

interface HeaderProps {
  user: UserType;
  stream: any;
  isFollowing: boolean;
  isLive: boolean;
  name: string;
  collapsed: boolean;
}

export const Header = ({
  user,
  stream,
  isFollowing,
  isLive,
  name,
  collapsed,
}: HeaderProps) => {
  const router = useRouter();
  const [loading, setLoading] = React.useState(false);
  const goLive = useMutation(api.streams.goLive);
  const endStream = useMutation(api.streams.endStream);
  const [missingKeysDialogOpen, setMissingKeysDialogOpen] = React.useState(false);
  const [missingKeysMessage, setMissingKeysMessage] = React.useState("");

  const handleStartStopStream = async () => {
    if (stream.isLive) {
      setLoading(true);
      try {
        await endStream({ streamId: stream.id || stream._id });
      } catch (err: any) {
        const msg = err?.message || "Failed to stop stream";
        alert(msg);
      } finally {
        setLoading(false);
      }
    } else {
      setLoading(true);
      try {
        await goLive({ streamId: stream.id || stream._id });
      } catch (err: any) {
        const msg = err?.message || "Failed to start stream";
        if (msg.includes("Missing serverUrl") || msg.includes("Missing serverUrl, streamKey, or ingressId")) {
          setMissingKeysMessage("You need to generate your Stream Key and Server URL before you can go live.");
          setMissingKeysDialogOpen(true);
        } else {
          alert(msg);
        }
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="flex flex-col lg:flex-row gap-y-4 lg:gap-y-0 items-start justify-between px-4 border-b border-input p-4">
      <div className="flex items-center gap-x-3">
        <Button
          onClick={() => router.back()}
          aria-label="Close stream"
          size="icon"
          variant="ghost"
        >
          <IconChevronLeft className="h-4 w-4" />
        </Button>
        <UserAvatar
          user={stream?.streamer}
          showOnlineIndicator={false}
        />
        <div className="space-y-1">
          <div className="flex items-center gap-x-2">
            <h2 className="text-lg font-semibold">{stream.streamer?.username}</h2>
            <VerifiedMark />
          </div>
          {/* Show viewer count for live streams */}
          {isLive && stream._id && (
            <LiveViewerCount
              streamId={stream._id}
              isLive={isLive}
              variant="text"
              size="sm"
              className="text-muted-foreground"
            />
          )}
        </div>
      </div>
      <div className={cn("flex items-center gap-x-3", collapsed && "mr-10")}>
        <Actions
          isFollowing={isFollowing}
          hostIdentity={user._id}
          isHost={stream.isHost}
        />
        {stream.isHost && (
          <>
            <Button
              onClick={handleStartStopStream}
              className={cn(
                "bg-sky-600 hover:bg-sky-700 text-white h-8",
                stream.isLive && "bg-red-600 hover:bg-red-700"
              )}
              disabled={loading}
            >
              {loading
                ? "Starting..."
                : stream.isLive
                ? "Stop Stream"
                : "Start Stream"}
            </Button>
            <Dialog open={missingKeysDialogOpen} onOpenChange={setMissingKeysDialogOpen}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Missing Stream Keys</DialogTitle>
                </DialogHeader>
                <div className="mb-4">
                  {missingKeysMessage || "You are missing required streaming keys. Please set them up before going live."}
                </div>
                <DialogFooter>
                  <Button asChild variant="default" onClick={() => setMissingKeysDialogOpen(false)}>
                    <Link href="/settings/sellers/tools">Go to Stream Key Setup</Link>
                  </Button>
                  <Button variant="ghost" onClick={() => setMissingKeysDialogOpen(false)}>
                    Cancel
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </>
        )}
      </div>
      {collapsed && (
        <div className="hidden lg:block fixed right-4 top-[15px] z-50 border-l border-input">
          <ChatToggle />
        </div>
      )}
    </div>
  );
};

export const HeaderSkeleton = () => {
  return (
    <div className="flex flex-col lg:flex-row gap-y-4 lg:gap-y-0 items-start justify-between px-4">
      <div className="flex items-center gap-x-2">
        <UserAvatarSkeleton size="lg" />
        <div className="space-y-2">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-24" />
        </div>
      </div>
      <ActionsSkeleton />
    </div>
  );
};

// Enhanced ViewerCount component using the new hook
export function ViewerCount({ streamId }: { streamId: Id<"streams"> }) {
  return (
    <LiveViewerCount
      streamId={streamId}
      isLive={true}
      variant="text"
      showIcon={true}
      showLabel={true}
      size="md"
    />
  );
}
