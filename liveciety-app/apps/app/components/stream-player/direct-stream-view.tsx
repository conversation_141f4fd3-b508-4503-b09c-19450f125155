"use client";

import { useEffect, useState, useRef } from "react";
import { useAction, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import {
  Room,
  RoomEvent,
  RemoteParticipant,
  RemoteTrackPublication,
  Track,
  Participant,
  ConnectionState,
} from "livekit-client";
import { toast } from "sonner";
import { Volume2, VolumeX, Maximize, Minimize, Users } from "lucide-react";
import { StandaloneChat } from "./standalone-chat";

const LIVEKIT_WS_URL = process.env.NEXT_PUBLIC_LIVEKIT_WS_URL;

interface DirectStreamViewProps {
  streamId: Id<"streams">;
  hostIdentity: string;
  hostName: string;
  viewerName?: string;
  isFollowing?: boolean;
  isChatEnabled?: boolean;
  isChatDelayed?: boolean;
  isChatFollowersOnly?: boolean;
  showChat?: boolean;
}

export function DirectStreamView({
  streamId,
  hostIdentity,
  hostName,
  viewerName = "Anonymous",
  isFollowing = false,
  isChatEnabled = true,
  isChatDelayed = false,
  isChatFollowersOnly = false,
  showChat = false
}: DirectStreamViewProps) {
  const generateViewerToken = useAction(api.integration.livekit.generateViewerToken);
  const getParticipantCountAction = useAction(api.integration.livekit.getRoomParticipantsCount);

  const [room, setRoom] = useState<Room | undefined>();
  const [token, setToken] = useState<string | undefined>();
  const [error, setError] = useState<string | undefined>();
  const [isConnected, setIsConnected] = useState(false);
  const [viewerCount, setViewerCount] = useState<number | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);

  const roomRef = useRef<Room | undefined>(undefined);
  const isConnectingRef = useRef(false);

  const videoContainerRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(0.5);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const streamDetails = useQuery(api.streams.getStreamById, { streamId });

  // Simple viewer count update
  useEffect(() => {
    if (streamDetails?.status === "live" && isConnected) {
      const roomName = `stream-${streamId}`;
      getParticipantCountAction({ roomName })
        .then(setViewerCount)
        .catch(() => {});

      const intervalId = setInterval(() => {
        if (isConnected && streamDetails?.status === "live") {
          getParticipantCountAction({ roomName })
            .then(setViewerCount)
            .catch(() => {});
        }
      }, 30000);
      return () => clearInterval(intervalId);
    } else {
      setViewerCount(null);
    }
  }, [streamDetails?.status, streamId, getParticipantCountAction, isConnected]);

  // Handle video/audio controls
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.volume = volume;
      videoRef.current.muted = isMuted;
    }
    if (audioRef.current) {
      audioRef.current.volume = volume;
      audioRef.current.muted = isMuted;
    }
  }, [isMuted, volume]);

  const toggleMute = () => setIsMuted(!isMuted);

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    if (newVolume > 0 && isMuted) {
      setIsMuted(false);
    } else if (newVolume === 0 && !isMuted) {
      setIsMuted(true);
    }
  };

  const toggleFullscreen = () => {
    if (!videoContainerRef.current) return;
    if (!document.fullscreenElement) {
      videoContainerRef.current.requestFullscreen().catch(err => {
        toast.error(`Error attempting to enable full-screen mode: ${err.message}`);
      });
    } else {
      document.exitFullscreen();
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Simple token generation
  useEffect(() => {
  
    if (!LIVEKIT_WS_URL) {
      console.error("[DirectStreamView] LiveKit WebSocket URL is not configured");
      setError("LiveKit WebSocket URL is not configured");
      return;
    }
    
    if (streamDetails?.status !== "live") {
      console.log("[DirectStreamView] Stream is not live, clearing token");
      setToken(undefined);
      setError(undefined);
      return;
    }

    console.log("[DirectStreamView] Generating viewer token for streamId:", streamId);
    generateViewerToken({ streamId })
      .then((newToken) => {
        console.log("[DirectStreamView] Token generated successfully, length:", newToken.length);
        setToken(newToken);
        setError(undefined);
      })
      .catch((err) => {
        console.error("[DirectStreamView] Failed to get viewer token:", err);
        setError("Failed to get stream token: " + (err.message || err));
        toast.error("Error fetching stream token");
      });
  }, [streamId, generateViewerToken, streamDetails?.status]);

  // Simple connection logic with race condition protection
  useEffect(() => {
    console.log("[DirectStreamView] Connection effect triggered");
    console.log("[DirectStreamView] Prerequisites check:", {
      hasToken: !!token,
      hasWSUrl: !!LIVEKIT_WS_URL,
      isLive: streamDetails?.status === "live",
      hasError: !!error,
      currentRoomState: roomRef.current?.state,
      isConnectingRef: isConnectingRef.current
    });

    // Don't connect if we don't have prerequisites
    if (!token || !LIVEKIT_WS_URL || streamDetails?.status !== "live" || error) {
      console.log("[DirectStreamView] Prerequisites not met, cleaning up");
      if (roomRef.current && !isConnectingRef.current) {
        console.log("[DirectStreamView] Disconnecting existing room");
        // Only disconnect if room is connected or connecting
        if (roomRef.current.state === ConnectionState.Connected || roomRef.current.state === ConnectionState.Connecting) {
          roomRef.current.disconnect(true).catch(console.warn);
        }
        roomRef.current = undefined;
        setRoom(undefined);
        setIsConnected(false);
        setIsConnecting(false);
        isConnectingRef.current = false;
      }
      return;
    }

    // Don't recreate room if already connected and working
    if (roomRef.current && roomRef.current.state === ConnectionState.Connected && isConnected) {
      console.log("[DirectStreamView] Room already connected and working, skipping");
      return;
    }

    // Don't create multiple rooms simultaneously
    if (isConnectingRef.current) {
      console.log("[DirectStreamView] Already connecting, skipping");
      return;
    }

    // Add a small delay to prevent React strict mode issues
    const connectionTimer = setTimeout(() => {
      // Recheck prerequisites after delay
      if (!token || !LIVEKIT_WS_URL || streamDetails?.status !== "live" || error || isConnectingRef.current) {
        console.log("[DirectStreamView] Prerequisites changed during delay, aborting");
        return;
      }

      // Clean up any existing room
      if (roomRef.current) {
        console.log("[DirectStreamView] Cleaning up existing room");
        // Only disconnect if room is connected or connecting
        if (roomRef.current.state === ConnectionState.Connected || roomRef.current.state === ConnectionState.Connecting) {
          roomRef.current.disconnect(true).catch(console.warn);
        }
        roomRef.current = undefined;
      }

      console.log("[DirectStreamView] Starting new connection attempt");
      console.log("[DirectStreamView] Creating new room and connecting to:", `stream-${streamId}`);
      
      isConnectingRef.current = true;
      setIsConnecting(true);
      setError(undefined);

      const newRoom = new Room({
        adaptiveStream: true,
        reconnectPolicy: {
          nextRetryDelayInMs: (context) => {
            return Math.min(1000 * Math.pow(2, context.retryCount), 30000);
          },
        },
      });

      roomRef.current = newRoom;
      setRoom(newRoom);

      // Helper function to check if participant is the host
      const isHostParticipant = (participant: Participant) => {
        return participant.identity === hostIdentity || 
               participant.identity.includes(hostIdentity) ||
               hostIdentity.includes(participant.identity) ||
               participant.name === hostName;
      };

      const handleTrackSubscribed = (track: Track, participant: Participant) => {
        if (isHostParticipant(participant)) {
          console.log("[DirectStreamView] Attaching host track:", track.kind);
          
          if (track.kind === Track.Kind.Video && videoRef.current) {
            track.attach(videoRef.current);
            videoRef.current.play().catch(() => {});
          } else if (track.kind === Track.Kind.Audio && audioRef.current) {
            track.attach(audioRef.current);
            audioRef.current.play().catch(() => {});
          }
          
          setError(undefined);
        }
      };

      const handleTrackUnsubscribed = (track: Track, participant: Participant) => {
        if (isHostParticipant(participant)) {
          track.detach();
          if (track.kind === Track.Kind.Video && videoRef.current) {
            videoRef.current.srcObject = null;
          }
        }
      };

      const connectToRoom = async () => {
        try {
          console.log("[DirectStreamView] Connecting to room...");
          await newRoom.connect(LIVEKIT_WS_URL, token, { 
            autoSubscribe: true,
            rtcConfig: {
              iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
              ],
            },
          });
          
          console.log("[DirectStreamView] Connected successfully");
          setIsConnected(true);
          setIsConnecting(false);
          isConnectingRef.current = false;
        } catch (e: any) {
          console.error("[DirectStreamView] Connection failed:", e);
          
          // Don't show error for cleanup-related disconnects
          if (!e.message?.includes('Client initiated disconnect') && !e.message?.includes('abort')) {
            setError(`Connection failed: ${e.message}`);
            toast.error(`Connection failed: ${e.message}`);
          } else {
            console.log("[DirectStreamView] Connection aborted by cleanup, not showing error");
          }
          
          setIsConnected(false);
          setIsConnecting(false);
          isConnectingRef.current = false;
          
          // Only clear room reference for real errors
          if (!e.message?.includes('Client initiated disconnect') && !e.message?.includes('abort')) {
            roomRef.current = undefined;
          }
        }
      };

      // Set up event listeners
      newRoom.on(RoomEvent.TrackSubscribed, (track: Track, publication: RemoteTrackPublication, participant: RemoteParticipant) => {
        handleTrackSubscribed(track, participant);
      });
      
      newRoom.on(RoomEvent.TrackUnsubscribed, (track: Track, publication: RemoteTrackPublication, participant: RemoteParticipant) => {
        handleTrackUnsubscribed(track, participant);
      });

      newRoom.on(RoomEvent.Disconnected, (reason) => {
        console.log("[DirectStreamView] Disconnected from room:", reason);
        setIsConnected(false);
        setIsConnecting(false);
        isConnectingRef.current = false;
        
        // Only show disconnect message for unexpected disconnections
        if (reason && reason !== 1) { // 1 is CLIENT_INITIATED
          toast.info("Disconnected from stream");
        }
      });

      newRoom.on(RoomEvent.Reconnecting, () => {
        console.log("[DirectStreamView] Reconnecting...");
        setIsConnecting(true);
        toast.info("Reconnecting...");
      });

      newRoom.on(RoomEvent.Reconnected, () => {
        console.log("[DirectStreamView] Reconnected");
        setIsConnecting(false);
        setIsConnected(true);
        toast.success("Reconnected!");
      });

      // Start connection
      connectToRoom();
    }, 100); // Small delay to prevent rapid effect re-runs

    // Cleanup function
    return () => {
      console.log("[DirectStreamView] Cleaning up connection");
      clearTimeout(connectionTimer);
      
      // Only cleanup if not actively connecting to prevent race conditions
      if (!isConnectingRef.current) {
        if (roomRef.current) {
          // Only disconnect if room is connected or connecting
          if (roomRef.current.state === ConnectionState.Connected || roomRef.current.state === ConnectionState.Connecting) {
            roomRef.current.disconnect(true).catch(console.warn);
          }
          roomRef.current = undefined;
        }
        setRoom(undefined);
        setIsConnected(false);
        setIsConnecting(false);
      } else {
        console.log("[DirectStreamView] Skipping cleanup - connection in progress");
      }
    };
  }, [token, streamDetails?.status, hostIdentity]);

  if (!streamDetails) {
    return <div className="p-4 text-center text-xl">Loading stream details...</div>;
  }

  if (streamDetails.status !== "live") {
    return <div className="p-4 text-center text-xl">Stream is not live.</div>;
  }

  if (error) {
    return <div className="text-red-500 p-4 text-center text-xl bg-red-100 border border-red-500 rounded-md">{error}</div>;
  }

  if (!token) {
    return <div className="p-4 text-center text-xl">Joining stream...</div>;
  }

  return (
    <div className="w-full">
      <div className="flex items-center space-x-4 mb-2">
        <h2 className="text-lg font-semibold">{streamDetails.title}</h2>
        {viewerCount !== null && (
          <div className="flex items-center text-sm text-gray-400">
            <Users size={16} className="mr-1"/> {viewerCount} viewer(s)
          </div>
        )}
      </div>

      <div ref={videoContainerRef} className="w-full aspect-video bg-black rounded-md shadow-2xl mb-4 relative overflow-hidden group">
        <video ref={videoRef} autoPlay playsInline className="w-full h-full object-contain"></video>
        <audio ref={audioRef} autoPlay playsInline className="hidden"></audio>

        {isConnected && (
          <div className="absolute bottom-0 left-0 right-0 p-2 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button onClick={toggleMute} className="text-white p-1.5 hover:bg-white/20 rounded-full">
                {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.05"
                value={isMuted ? 0 : volume}
                onChange={handleVolumeChange}
                className="w-20 h-1.5 accent-primary cursor-pointer"
              />
            </div>
            <button onClick={toggleFullscreen} className="text-white p-1.5 hover:bg-white/20 rounded-full">
              {isFullscreen ? <Minimize size={20} /> : <Maximize size={20} />}
            </button>
          </div>
        )}

        {(isConnecting || (!isConnected && !error)) && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
            <p className="ml-3 text-lg text-white">
              {isConnecting ? "Connecting..." : "Joining stream..."}
            </p>
          </div>
        )}

        {isConnected && videoRef.current && !videoRef.current.srcObject && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
            <p className="text-xl text-gray-300">Waiting for host to start video...</p>
          </div>
        )}
      </div>

      {isConnected ? (
        <p className="text-green-400 mb-4">Connected to stream</p>
      ) : (
        <p className="text-yellow-400 mb-4">
          {isConnecting ? "Connecting..." : "Joining stream..."}
        </p>
      )}
    </div>
  );
}
