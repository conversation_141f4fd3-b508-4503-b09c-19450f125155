"use client";

import React, { useState, useCallback } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@workspace/ui/components/input";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@workspace/ui/components/select";
import { Switch } from "@workspace/ui/components/switch";
import { Button } from "@workspace/ui/components/button";
import { Label } from "@workspace/ui/components/label";
import { DatetimePicker } from "@workspace/ui/components/date-time-picker";
import { categories, subcategories } from "@workspace/lib/constants/categories";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useCurrentUser } from "@/hooks/use-current-user";
import { Badge } from "@workspace/ui/components/badge";
import { X } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@workspace/ui/components/avatar";
import { Command, CommandList, CommandEmpty, CommandGroup, CommandItem } from "@workspace/ui/components/command";
import { User } from "@/lib/types";
import { UserAvatar } from "../user-avatar";
import { IconPhoto } from "@tabler/icons-react";
import Image from "next/image";
import Cropper from "react-easy-crop";
import { Dialog, DialogContent, DialogFooter, DialogDescription, DialogTitle } from "@workspace/ui/components/dialog";
import { Slider } from "@workspace/ui/components/slider";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@workspace/ui/components/accordion";
import { SELLING_FORMATS } from "@workspace/lib/constants/formats";
import { getAvatarImageUrl } from "@/lib/utils";
import { cn } from "@workspace/ui/lib/utils";
import { Textarea } from "@workspace/ui/components/textarea";

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface CroppedAreaPixels {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const scheduleStreamSchema = z.object({
  eventType: z.enum(["scheduled", "popup"]),
  title: z.string().min(1, "Show name is required"),
  description: z.string().optional(),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().optional(),
  format: z.string().min(1, "Selling format is required"),
  repeatsOption: z.string(),
  explicitContent: z.boolean(),
  thumbnail: z.any().optional(),
  video: z.any().optional(),
  moderatorIds: z.array(z.string()),
  scheduledDatetime: z.date(),
  tags: z.array(z.string()).optional(),
  content: z.object({
    explicitContent: z.boolean(),
    muteWords: z.array(z.string()).optional(),
    muteWordsEnabled: z.boolean(),
  }),
  visibility: z.enum(["public", "private"]),
});

export type ScheduleStreamFormData = z.infer<typeof scheduleStreamSchema>;

interface ScheduleStreamFormProps {
  onSubmit: (data: ScheduleStreamFormData & { thumbnailStorageId?: string }) => void;
  initialValues?: Partial<ScheduleStreamFormData>;
  mode?: "create" | "edit";
}

const categoryOptions = categories.map(cat => ({
  value: cat.id,
  label: cat.title,
}));

type TagInputProps = {
  value: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
};

const TagInput: React.FC<TagInputProps> = ({ value, onChange, placeholder }) => {
  const [input, setInput] = useState("");

  const addTag = (tag: string) => {
    const trimmed = tag.trim();
    if (trimmed && !value.includes(trimmed)) {
      onChange([...value, trimmed]);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (
      e.key === "Enter" ||
      e.key === "," ||
      e.key === " "
    ) {
      e.preventDefault();
      addTag(input);
      setInput("");
    } else if (e.key === "Backspace" && !input && value.length) {
      onChange(value.slice(0, -1));
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-1 py-1 rounded-md">
      <Input
        className="flex-1 outline-none bg-transparent min-w-[100px]"
        value={input}
        onChange={e => setInput(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
      />
      {value.map((tag) => (
        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
          {tag}
          <button
            type="button"
            onClick={() => onChange(value.filter((t) => t !== tag))}
            className="ml-1"
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      ))}
    </div>
  );
};

const ModeratorSelect: React.FC<{
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder?: string;
}> = ({ selected, onChange, placeholder = "Select moderators" }) => {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [search, setSearch] = useState("");
  const [open, setOpen] = useState(false);
  const searchResults = useQuery(api.users.searchUsers, search ? { searchQuery: search } : "skip");
  const options = (searchResults?.users ?? []).map((user: User) => ({
    value: user._id,
    label: user.name || user.username || user.email,
    image: user.image,
    email: user.email,
    username: user.username,
    color: user.color,
  }));

  const selectables = options.filter(opt => !selected.includes(opt.value));
  
  const [selectedUsersMap, setSelectedUsersMap] = useState<Record<string, typeof options[0]>>({});

  const handleUnselect = (id: string) => onChange(selected.filter(s => s !== id));
  const handleSelect = (id: string) => {
    const user = options.find(u => u.value === id);
    if (user && !selected.includes(id)) {
      setSelectedUsersMap(prev => ({ ...prev, [id]: user }));
      onChange([...selected, id]);
    }
    setSearch("");
    setOpen(false);
    inputRef.current?.blur();
  };

  const selectedUsers = selected.map(id => selectedUsersMap[id] || { value: id, label: id, image: undefined, email: undefined, username: undefined });

  console.log('selectedUsers', selectedUsers);
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (
      (e.key === "Enter" || e.key === "," || e.key === " ") && selectables.length > 0 && search
    ) {
      e.preventDefault();

      if (selectables[0]?.value) {
        handleSelect(selectables[0].value);
      }
    } else if (e.key === "Backspace" && !search && selected.length) {
      onChange(selected.slice(0, -1));
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-1 py-1 rounded-lg relative">
      <Input
        ref={inputRef}
        className="flex-1 outline-none bg-transparent min-w-[100px] py-1"
        value={search}
        onChange={e => {
          setSearch(e.target.value);
          setOpen(true);
        }}
        onFocus={() => setOpen(true)}
        onBlur={() => setTimeout(() => setOpen(false), 100)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
      />
      {selectedUsers.map(user => (
        <Badge key={user.value} variant="secondary" className="flex items-center gap-1">
          <UserAvatar size="sm" user={user} showOnlineIndicator={false} />
          {user.username}
          <button
            type="button"
            onClick={() => handleUnselect(user.value)}
            className="ml-1"
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      ))}
      {open && selectables.length > 0 && (
        <div className={cn(
          "absolute left-0 top-full mt-2 w-full z-10 rounded-md border text-popover-foreground shadow-md outline-none animate-in",
          "ring-ring/50 ring-[3px] selection:!bg-blue-500/50",
        )}>
          <Command className="!bg-background">
            <CommandList>
              <CommandEmpty>No results found.</CommandEmpty>
              <CommandGroup>
                {selectables.map(option => (
                  <CommandItem
                    key={option.value}
                    onMouseDown={e => e.preventDefault()}
                    onSelect={() => handleSelect(option.value)}
                  >
                    <Avatar>
                      {option.image ? (
                        <AvatarImage src={getAvatarImageUrl(option.image)} alt={option.label} />
                      ) : (
                        <AvatarFallback
                          className="text-xs"
                          style={{ backgroundColor: option.color }}
                        >{option.label?.[0] || "U"}</AvatarFallback>
                      )}
                    </Avatar>
                    {option.username}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
      )}
    </div>
  );
};

export const ScheduleStreamForm: React.FC<ScheduleStreamFormProps> = ({ onSubmit, initialValues, mode = "create" }) => {
  const [thumbnailPreview, setThumbnailPreview] = React.useState<string | null>(null);
  const [cropModalOpen, setCropModalOpen] = React.useState(false);
  const [crop, setCrop] = React.useState({ x: 0, y: 0 });
  const [zoom, setZoom] = React.useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = React.useState<CroppedAreaPixels | null>(null);
  const [rawImage, setRawImage] = React.useState<string | null>(null);
  const [pendingRawImage, setPendingRawImage] = React.useState<string | null>(null);
  const [isUploading, setIsUploading] = React.useState(false);

  const { user } = useCurrentUser();
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);

  const sellerCategories = user?.sellerProfile?.categories || [];
  const singleCategory = sellerCategories.length === 1 ? sellerCategories[0] : undefined;

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
  } = useForm<ScheduleStreamFormData>({
    resolver: zodResolver(scheduleStreamSchema),
    defaultValues: {
      eventType: initialValues?.eventType || "scheduled",
      title: initialValues?.title || "",
      description: initialValues?.description || "",
      category: singleCategory || initialValues?.category || "",
      subcategory: initialValues?.subcategory || "",
      format: initialValues?.format || "",
      repeatsOption: initialValues?.repeatsOption || "Does not repeat",
      explicitContent: initialValues?.content?.explicitContent ?? false,
      thumbnail: initialValues?.thumbnail || undefined,
      video: initialValues?.video || undefined,
      moderatorIds: initialValues?.moderatorIds || [],
      scheduledDatetime: initialValues?.scheduledDatetime || new Date(),
      tags: initialValues?.tags || [],
      content: {
        explicitContent: initialValues?.content?.explicitContent ?? false,
        muteWords: initialValues?.content?.muteWords || [],
        muteWordsEnabled: initialValues?.content?.muteWordsEnabled ?? false,
      },
      visibility: initialValues?.visibility || "public",
    },
  });

  React.useEffect(() => {
    const file = watch("thumbnail");
    if (file && file[0]) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPendingRawImage(reader.result as string);
        setCropModalOpen(true);
      };
      reader.readAsDataURL(file[0]);
    } else {
      setThumbnailPreview(null);
      setRawImage(null);
      setPendingRawImage(null);
    }
  }, [watch("thumbnail")]);

  // Helper to crop the image
  const getCroppedImg = useCallback(async (imageSrc: string, cropArea: CroppedAreaPixels): Promise<string | null> => {
    const createImage = (url: string): Promise<HTMLImageElement> => {
      return new Promise((resolve, reject) => {
        const image = new window.Image();
        image.addEventListener('load', () => resolve(image));
        image.addEventListener('error', error => reject(error));
        image.setAttribute('crossOrigin', 'anonymous');
        image.src = url;
      });
    };
    const image = await createImage(imageSrc);
    const canvas = document.createElement('canvas');
    canvas.width = cropArea.width;
    canvas.height = cropArea.height;
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;
    ctx.drawImage(
      image,
      cropArea.x,
      cropArea.y,
      cropArea.width,
      cropArea.height,
      0,
      0,
      cropArea.width,
      cropArea.height
    );
    return new Promise<string>((resolve) => {
      canvas.toBlob(blob => {
        if (blob) {
          resolve(URL.createObjectURL(blob));
        }
      }, 'image/jpeg');
    });
  }, []);

  const handleCropComplete = useCallback((_: CropArea, croppedAreaPixels: CroppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const openCropModal = (image: string) => {
    setPendingRawImage(image);
    setCropModalOpen(true);
  };

  const handleCropConfirm = useCallback(async () => {
    if (pendingRawImage && croppedAreaPixels) {
      const croppedImg = await getCroppedImg(pendingRawImage, croppedAreaPixels);
      setThumbnailPreview(croppedImg);
      setRawImage(pendingRawImage);
      setCropModalOpen(false);
      setPendingRawImage(null);
    }
  }, [pendingRawImage, croppedAreaPixels, getCroppedImg]);

  const handleCropCancel = useCallback(() => {
    setCropModalOpen(false);
    setPendingRawImage(null);
  }, []);

  const selectedCategory = watch("category") || singleCategory;
  const availableSubcategories = (selectedCategory && subcategories[selectedCategory])
    ? subcategories[selectedCategory]
    : [];

  // Helper to convert blob URL to File
  const blobUrlToFile = async (blobUrl: string, fileName: string): Promise<File> => {
    const response = await fetch(blobUrl);
    const blob = await response.blob();
    return new File([blob], fileName, { type: blob.type });
  };

  // Upload file to Convex storage
  const uploadThumbnail = async (file: File): Promise<string> => {
    const uploadUrl = await generateUploadUrl({});
    const result = await fetch(uploadUrl, {
      method: "POST",
      headers: { "Content-Type": file.type },
      body: file,
    });
    const { storageId } = await result.json();
    return storageId;
  };

  // Modified form submit handler
  const handleFormSubmit = async (data: ScheduleStreamFormData) => {
    setIsUploading(true);
    try {
      let thumbnailStorageId: string | undefined;

      // If there's a cropped thumbnail, upload it
      if (thumbnailPreview) {
        const file = await blobUrlToFile(thumbnailPreview, 'thumbnail.jpg');
        thumbnailStorageId = await uploadThumbnail(file);
      }

      // If eventType is 'popup', set scheduledDatetime to now
      const submitData = {
        ...data,
        scheduledDatetime: data.eventType === 'popup' ? new Date() : data.scheduledDatetime,
        thumbnailStorageId,
      };

      onSubmit(submitData);
    } catch (error) {
      console.error('Failed to upload thumbnail:', error);
      // Still submit the form without thumbnail if upload fails
      onSubmit({
        ...data,
        scheduledDatetime: data.eventType === 'popup' ? new Date() : data.scheduledDatetime,
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8 max-w-lg mx-auto p-6 bg-background rounded-lg shadow">
      <div className="space-y-1">
        <Label htmlFor="title">Stream Title</Label>
        <Controller
          name="title"
          control={control}
          render={({ field }) => (
            <Input id="title" placeholder="Name your show *" {...field} />
          )}
        />
        {errors.title && <div className="text-red-500 text-sm">{errors.title.message}</div>}
      </div>
      {/* Event Type Selection (as Select dropdown) */}
      <div className="space-y-1">
        <Label htmlFor="eventType">Event Type</Label>
        <Controller
          name="eventType"
          control={control}
          render={({ field }) => (
            <Select value={field.value} onValueChange={field.onChange}>
              <SelectTrigger id="eventType" className="w-full">
                <SelectValue placeholder="Select event type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="scheduled">Scheduled Show</SelectItem>
                <SelectItem value="popup">Pop Up</SelectItem>
              </SelectContent>
            </Select>
          )}
        />
      </div>
      {/* Date & Time (only for scheduled) */}
      {watch("eventType") === "scheduled" && (
        <div className="space-y-1">
          <Label htmlFor="scheduledDatetime">Date & Time</Label>
          <Controller
            name="scheduledDatetime"
            control={control}
            render={({ field }) => (
              <DatetimePicker
                value={field.value}
                onChange={field.onChange}
              />
            )}
          />
        </div>
      )}
      <div className="space-y-1">
        <Label htmlFor="description">Description</Label>
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <Textarea 
              id="description" 
              placeholder="Description" 
              {...field} 
              className="min-h-[100px]"
            />
          )}
        />
      </div>
      {/* Category (only show if more than one) */}
      {sellerCategories.length > 1 && (
        <div className="space-y-1">
          <Label htmlFor="category">Category</Label>
          <Controller
            name="category"
            control={control}
            render={({ field }) => (
              <Select value={field.value} onValueChange={value => {
                field.onChange(value);
                setValue("subcategory", "");
              }}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categoryOptions.filter(option => sellerCategories.includes(option.value)).map(option => (
                    <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.category && <div className="text-red-500 text-sm">{errors.category.message}</div>}
        </div>
      )}
      {/* If only one category, show as text (hidden input) */}
      {sellerCategories.length === 1 && (
        <input type="hidden" value={singleCategory} {...(control.register ? control.register("category") : {})} />
      )}
      {/* Subcategory (filtered by selected category) */}
      <div className="space-y-1">
        <Label htmlFor="subcategory">Subcategory</Label>
        <Controller
          name="subcategory"
          control={control}
          render={({ field }) => (
            <Select value={field.value} onValueChange={field.onChange}>
              <SelectTrigger id="subcategory" className="w-full">
                <SelectValue placeholder="Select a subcategory" />
              </SelectTrigger>
              <SelectContent>
                {availableSubcategories.map(option => (
                  <SelectItem key={option.id} value={option.id}>{option.title}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />
      </div>
      {/* Selling Format */}
      <div className="space-y-1">
        <Label htmlFor="format">Format</Label>
        <Controller
          name="format"
          control={control}
          render={({ field }) => (
            <Select value={field.value} defaultValue="fixed" onValueChange={field.onChange}>
              <SelectTrigger id="format" className="w-full">
                <SelectValue placeholder="Select a selling format" />
              </SelectTrigger>
              <SelectContent>
                {SELLING_FORMATS.map(option => (
                  <SelectItem key={option.title} value={option.title}>{option.title}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />
        {errors.format && <div className="text-red-500 text-sm">{errors.format.message}</div>}
      </div>
      {/* Tags */}
      <div>
        <Label>Tags</Label>
        <Controller
          name="tags"
          control={control}
          render={({ field }) => (
            <TagInput
              value={field.value || []}
              onChange={field.onChange}
              placeholder="Type a tag and hit space, comma, or enter"
            />
          )}
        />
      </div>

      {/* Moderators */}
      <div>
        <Label>Moderators</Label>
        <Controller
          name="moderatorIds"
          control={control}
          render={({ field }) => (
            <ModeratorSelect
              selected={field.value || []}
              onChange={field.onChange}
              placeholder="Select moderators"
            />
          )}
        />
      </div>
      {/* Thumbnail */}
      <div className="space-y-1">
        <Label>Thumbnail
          <span className="text-xs text-muted-foreground/60"> 
            (optional)
          </span>
        </Label>
        <Controller
          name="thumbnail"
          control={control}
          render={({ field }) => {
            const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
              e.preventDefault();
              if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                field.onChange(e.dataTransfer.files);
              }
            };
            const handleClick = () => {
              if (thumbnailPreview && rawImage) {
                openCropModal(rawImage);
              } else {
                document.getElementById("thumbnail-input")?.click();
              }
            };
            return (
              <>
                <div
                  onDrop={handleDrop}
                  onDragOver={e => e.preventDefault()}
                  onClick={handleClick}
                  className="flex flex-col items-center justify-center w-60 h-100 border-2 border-dashed border-muted-foreground/40 rounded cursor-pointer transition hover:border-primary/60 bg-muted/10 relative"
                  style={{ minHeight: 200 }}
                >
                  {thumbnailPreview ? (
                    <>
                      <Image
                        src={thumbnailPreview}
                        alt="Thumbnail preview"
                        className="absolute inset-0 w-full h-full object-cover rounded"
                        width={100}
                        height={100}
                      />
                      <Button
                        type="button"
                        size="sm"
                        className="absolute top-2 right-2 z-20 text-xs font-medium shadow transition"
                        onClick={e => {
                          e.stopPropagation();
                          if (rawImage) openCropModal(rawImage);
                        }}
                        aria-label="Edit thumbnail crop"
                      >
                        Edit
                      </Button>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center z-10">
                      <IconPhoto className="w-12 h-12 text-muted-foreground/50 mb-2" />
                      <span className="text-muted-foreground font-medium">Upload thumbnail</span>
                      <span className="text-xs text-muted-foreground/60">Drag & drop or click to select</span>
                    </div>
                  )}
                  <input
                    id="thumbnail-input"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={e => field.onChange(e.target.files)}
                  />
                </div>
                <Dialog open={cropModalOpen} onOpenChange={setCropModalOpen}>
                  <DialogContent className="max-w-lg">
                    <DialogTitle>Crop Thumbnail</DialogTitle>
                    <DialogDescription className="sr-only">
                      Crop the thumbnail to the desired size.
                    </DialogDescription>
                    {pendingRawImage && (
                      <div className="relative w-full h-72 bg-black">
                        <Cropper
                          image={pendingRawImage}
                          crop={crop}
                          zoom={zoom}
                          aspect={9/16}
                          onCropChange={setCrop}
                          onZoomChange={setZoom}
                          onCropComplete={handleCropComplete}
                        />
                      </div>
                    )}
                    <div className="flex items-center gap-4 mt-4">
                      <span className="text-xs">Zoom</span>
                      <Slider min={1} max={3} step={0.01} value={[zoom]} onValueChange={([z]) => setZoom(z ?? 1)} />
                    </div>
                    <DialogFooter>
                      <Button type="button" onClick={handleCropCancel} variant="secondary">Cancel</Button>
                      <Button type="button" onClick={handleCropConfirm}>Crop</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </>
            );
          }}
        />
      </div>
      
      {/* Content Settings */}
      <div className="space-y-1">
        <h3 className="text-lg font-semibold mb-2">Content Settings</h3>
        <div className="flex items-center justify-between gap-4 mb-2">
          <span className="font-medium">Explicit Content</span>
          <Controller
            name="content.explicitContent"
            control={control}
            render={({ field }) => (
              <Switch checked={field.value} onCheckedChange={field.onChange} />
            )}
          />
        </div>
        <div className="text-muted-foreground text-sm mb-2">
          Turn this on if your stream contains explicit content.
        </div>
        <Accordion type="single" collapsible>
          <AccordionItem value="learn-more-explicit-content">
            <AccordionTrigger className="text-xs px-0 py-2 font-medium underline hover:no-underline w-fit">Learn more</AccordionTrigger>
            <AccordionContent>
              <div className="text-xs text-muted-foreground space-y-2">
                <div>
                  If this setting is enabled, you may use explicit language on Liveciety. However, any language directed at an individual that is violent, sexually inappropriate, or hateful is strictly prohibited.
                </div>
                <div>
                  Visually explicit content (nudity, sexually suggestive material) is not allowed. For the Comic Books & Manga categories, this is permitted, but preview videos and thumbnails must not contain explicit content as outlined here.
                </div>
                <div>
                  Violating any of these guidelines may result in your removal from Liveciety.
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        
        <div className="space-y-1 mt-4">
          <Label>Mute Words</Label>
          <div className="flex items-center justify-between gap-4 mb-2">
            <span className="text-muted-foreground text-sm">Turn this on to add words you&apos;d like to mute from your show&apos;s chat.</span>
            <Controller
              name="content.muteWordsEnabled"
              control={control}
              render={({ field }) => (
                <Switch
                  checked={field.value}
                  onCheckedChange={checked => {
                    field.onChange(checked);
                    if (!checked) setValue('content.muteWords', []);
                  }}
                />
              )}
            />
          </div>
          {watch('content.muteWordsEnabled') && (
            <>
              <Controller
                name="content.muteWords"
                control={control}
                render={({ field }) => (
                  <TagInput
                    value={field.value || []}
                    onChange={field.onChange}
                    placeholder="Type a word and hit space, comma, or enter"
                  />
                )}
              />
            </>
          )}
          <Accordion type="single" collapsible>
            <AccordionItem value="learn-more-mute-words">
              <AccordionTrigger className="text-xs px-0 py-2 font-medium underline hover:no-underline w-fit">Learn more</AccordionTrigger>
              <AccordionContent>
                <div className="text-xs text-muted-foreground">
                  Messages containing these words will be flagged and will not be visible to your show&apos;s viewers.
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
        
      </div>
      {/* Visibility */}
      <div className="space-y-1">
        <Label>Visibility</Label>
        <Controller
          name="visibility"
          control={control}
          render={({ field }) => (
            <Select value={field.value} onValueChange={field.onChange}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select visibility" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="public">Public</SelectItem>
                <SelectItem value="private">Private</SelectItem>
              </SelectContent>
            </Select>
          )}
        />
      </div>
      <Button type="submit" className="w-full mt-6" disabled={isUploading}>
        {isUploading ? "Uploading..." : mode === "edit" ? "Update" : "Schedule"}
      </Button>
    </form>
  );
};

export default ScheduleStreamForm; 