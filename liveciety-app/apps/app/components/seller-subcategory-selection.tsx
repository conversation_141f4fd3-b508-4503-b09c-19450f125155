"use client";

import { useState } from "react";
import { Button } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import { subcategories } from "@workspace/lib/constants/categories";
import { SellerExperienceSelection } from "./seller-experience-selection";

interface Props {
  category: string;
  onBack: () => void;
  onSelect: (subcategory: string) => void;
}

export function SellerSubcategorySelection({
  category,
  onBack,
  onSelect,
}: Props) {
  const [selectedSubcategories, setSelectedSubcategories] = useState<string[]>([]);
  const [step, setStep] = useState<"subcategory" | "experience">("subcategory");
  const availableSubcategories = subcategories[category] || [];

  const handleSubcategoryToggle = (subcategoryId: string) => {
    setSelectedSubcategories(prev => {
      if (prev.includes(subcategoryId)) {
        return prev.filter(id => id !== subcategoryId);
      } else {
        return [...prev, subcategoryId];
      }
    });
  };

  const handleContinue = () => {
    if (selectedSubcategories.length > 0) {
      setStep("experience");
    }
  };  

  const handleExperienceNext = () => {
    if (selectedSubcategories.length > 0) {
      onSelect(selectedSubcategories[0]!);
    }
  }

  if (step === "experience") {
    return (
      <SellerExperienceSelection 
        onBack={() => setStep("subcategory")} 
        onNext={handleExperienceNext} 
      />
    );
  }

  return (
    <div className="flex flex-col">
      <div className="bg-gradient-to-br from-red-400/30 to-blue-500/20 px-8 py-8">
        <div className="flex-none mb-6">
          <div className="w-full h-2 bg-primary/10 rounded-full overflow-hidden">
            <div className="h-full w-[40%] bg-gradient-to-r from-red-400 to-blue-500 rounded-full"></div>
          </div>
          <div className="flex justify-between text-sm mt-3">
            <span className="text-zinc-500">Guidelines</span>
            <span className="font-medium">Categories</span>
            <span className="text-zinc-500">Experience</span>
            <span className="text-zinc-500">Additional Details</span>
          </div>
        </div>

        <div className="flex-none">
          <h1 className="text-3xl font-bold mb-2">
            Select your subcategories
          </h1>
          <p className="text-zinc-600 dark:text-zinc-400">
            Select all that apply. You can always add more later.
          </p>
        </div>
      </div>

      <div className="px-8 py-6 overflow-y-auto sm:!h-[calc(100vh-17.75rem)] h-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          {availableSubcategories.map((subcategory) => {
            const isSelected = selectedSubcategories.includes(subcategory.id);
            return (
              <div 
                key={subcategory.id}
                className="relative rounded-xl overflow-hidden border group cursor-pointer transition-all duration-300 transform hover:-translate-y-1"
                onClick={() => handleSubcategoryToggle(subcategory.id)}
              >
                {/* Gradient border for selected subcategories */}
                {isSelected && (
                  <div className="absolute -inset-[3px] rounded-[14px] bg-gradient-to-br from-red-400/70 to-blue-500/70 animate-pulse z-10"></div>
                )}
                
                <Card
                  className="relative overflow-hidden border-0 shadow-lg !p-0 hover:shadow-xl h-20"
                >
                  <div className="aspect-square relative overflow-hidden bg-gradient-to-br from-zinc-800 to-zinc-900">
                    {/* Background gradient */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-red-400/20 opacity-70"></div>
                    
                    {/* Content */}
                    <div className="absolute inset-0 flex flex-col justify-center items-center p-4 text-center">
                      <h3 className="font-bold text-lg text-white mb-1">{subcategory.title}</h3>
                    </div>
                    
                    {/* Selection indicator */}
                    {isSelected && (
                      <div className="absolute top-3 right-3 bg-white dark:bg-zinc-800 rounded-full p-1 shadow-md z-20">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-primary">
                          <path fillRule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                    

                  </div>
                </Card>
              </div>
            );
          })}
        </div>
      </div>

      <div className="flex justify-between items-center px-8 py-6 border-t dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900/50">
        <Button variant="outline" size="lg" onClick={onBack}>
          Back
        </Button>
        <div className="flex items-center gap-3">
          <span className="text-sm text-zinc-500">
            {selectedSubcategories.length} {selectedSubcategories.length === 1 ? 'subcategory' : 'subcategories'} selected
          </span>
          <Button
            size="lg"
            disabled={selectedSubcategories.length === 0}
            onClick={handleContinue}
            className="font-medium"
          >
            Continue
          </Button>
        </div>
      </div>
    </div>
  );
}
