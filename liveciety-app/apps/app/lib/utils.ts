import { categories } from "@workspace/lib/constants/categories";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import stc from "string-to-color";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

/**
 * Get the URL for a path
 * @param baseUrl - The base URL to get the URL for
 * @param user - The user to get the URL for
 * @param activeWorkspace - The active workspace to get the URL for
 * @returns The URL for the path
 */
export function getUrl(baseUrl: string) {
  if (baseUrl === "#") return baseUrl;

  const url = baseUrl.startsWith("/") ? `/v/${baseUrl}` : `/v/${baseUrl}`;

  return url;
}

/**
 * Navigate to a path
 * @param path - The path to navigate to
 * @param user - The user to navigate to
 * @param activeWorkspace - The active workspace to navigate to
 * @param router - The router to navigate to
 */
export const navigateTo = (path: string, router: AppRouterInstance) => {
  const url = getUrl(path);
  router.push(url);
};

/**
 * @function formatFileSize
 * @param bytes
 * @returns {string}
 * @description This function will format the file size in bytes to a human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
  return `${size} ${sizes[i]}`;
};

/**
 * Get platform-specific keyboard shortcut symbol
 * @returns {Object} Object containing platform info and shortcut symbol
 */
export const getPlatformShortcuts = () => {
  if (typeof window === "undefined")
    return { isMac: false, shortcutSymbol: "Ctrl" };

  const isMac = navigator.userAgent.toLowerCase().indexOf("mac") !== -1;
  return {
    isMac,
    shortcutSymbol: isMac ? "⌘" : "Ctrl",
  };
};

/**
 * Get the avatar image URL
 * @param image - The image to get the URL for
 * @returns The URL for the image
 */
export function getAvatarImageUrl(image?: string | null | Id<"_storage">) {
  if (!image) return null;

  // Convert to string if it's not already
  const imageStr = String(image);
  
  // If it's already a full URL, return it
  if (imageStr.startsWith("http://") || imageStr.startsWith("https://")) {
    return imageStr;
  }
  
  // If it's already a getImage URL, return it
  if (imageStr.includes("/getImage?storageId=")) {
    return imageStr;
  }
  
  // If NEXT_PUBLIC_SITE_URL is not defined, log an error and return null
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL;
  if (!baseUrl) {
    console.error("NEXT_PUBLIC_SITE_URL environment variable is not defined");
    return null;
  }
  
  // Extract storage ID from URL if present - safely check for string before calling .match
  let storageIdMatch = null;
  if (typeof imageStr === 'string' && imageStr.match) {
    storageIdMatch = imageStr.match(/storageId=([^&]+)/);
  }
  if (storageIdMatch) {
    return `${baseUrl}/getImage?storageId=${storageIdMatch[1]}`;
  }
  
  // Assume it's a storage ID and construct the URL
  return `${baseUrl}/getImage?storageId=${imageStr}`;
}

/**
 * Convert a string to a color
 * @param str - The string to convert to a color
 * @returns The color
 */
export const stringToColor = (str: string) => {
  return stc(str);
};

/**
 * Get the title of a category
 * @param categoryId - The ID of the category
 * @returns The title of the category
 */
export const getCategoryTitle = (categoryId: string) => {
  const category = categories.find(cat => cat.id === categoryId);
  return category ? category.title : categoryId;
}

/**
 * Format a scheduled time
 * @param time - The time to format
 * @returns The formatted time
 */
export const formatScheduledTime = (time: string) => {
  const date = new Date(time)
  return date.toLocaleString("en-US", {
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
  })
}