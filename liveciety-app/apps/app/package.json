{"name": "@workspace/app", "version": "1.3.6", "private": true, "scripts": {"dev": "next dev --turbopack --port 3009", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@convex-dev/auth": "^0.0.80", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.3", "@livekit/components-react": "^2.9.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-google-maps/api": "^2.20.6", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-table": "^8.21.2", "@tanstack/table-core": "^8.21.2", "@vercel/analytics": "^1.5.0", "@workspace/backend": "workspace:*", "@workspace/ui": "workspace:*", "@xixixao/uploadstuff": "^0.0.5", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^11.0.0", "googleapis": "^148.0.0", "input-otp": "^1.4.2", "jwt-decode": "^4.0.0", "livekit-client": "^2.13.1", "lodash": "^4.17.21", "lucide-react": "^0.483.0", "next": "^15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.2", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.7", "react-share": "^5.2.2", "recharts": "^2.15.1", "sonner": "^2.0.1", "string-to-color": "^2.2.2", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.4", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/canvas-confetti": "^1.9.0", "@types/lodash": "^4.17.16", "@types/node": "^20.17.25", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "tailwindcss": "^4", "typescript": "^5"}}