import type { NextConfig } from "next";
import path from "path";

const nextConfig: NextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  transpilePackages: ["@workspace/ui", "@workspace/backend", "@workspace/lib"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "affable-partridge-134.convex.site",
      },
      {
        protocol: "https",
        hostname: "affable-partridge-134.convex.cloud",
      },
      {
        protocol: "https",
        hostname: "decisive-perch-342.convex.site",
      },
      {
        protocol: "https",
        hostname: "decisive-perch-342.convex.cloud",
      },
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
      },
      {
        protocol: "https",
        hostname: "res.cloudinary.com",
      },
      {
        protocol: "https",
        hostname: "firebasestorage.googleapis.com",
      },
      {
        protocol: "https",
        hostname: "placehold.co",
      },
    ],
  },
  webpack: (config) => {
    config.resolve = config.resolve || {};
    config.resolve.alias = config.resolve.alias || {};
    config.resolve.alias["@workspace/assets"] = path.resolve(
      __dirname,
      "../../packages/assets",
    );
    config.resolve.alias["@workspace/lib"] = path.resolve(
      __dirname,
      "../../packages/lib",
    );
    return config;
  },
};

export default nextConfig;
