import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  transpilePackages: ["@workspace/ui", "@workspace/backend", "@workspace/assets"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "affable-partridge-134.convex.site",
      },
      {
        protocol: "https",
        hostname: "decisive-perch-342.convex.site",
      },
      {
        protocol: "https",
        hostname: "decisive-perch-342.convex.cloud",
      },
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
      },
      {
        protocol: "https",
        hostname: "affable-partridge-134.convex.cloud",
      },
      {
        protocol: "https",
        hostname: "firebasestorage.googleapis.com",
      },
    ],
  },
};

export default nextConfig;
