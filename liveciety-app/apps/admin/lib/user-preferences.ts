import { STORAGE_KEYS, UserPreferences } from "./constants";
import { getLocalStorage, setLocalStorage } from "./local-storage";

const defaultPreferences: UserPreferences = {
  view: "list",
  sortBy: "_creationTime",
  sortOrder: "desc",
  filters: {
    name: "",
    email: "",
    username: "",
    role: "user" as "user" | "admin" | "seller",
  },
};

export const getUserPreferences = (): UserPreferences => {
  const stored = getLocalStorage(STORAGE_KEYS.USERS_PREFERENCES as string);
  if (!stored) return defaultPreferences;

  try {
    return JSON.parse(stored) as UserPreferences;
  } catch (e) {
    console.error("Error parsing user preferences", e);
    return defaultPreferences;
  }
};

export const updateUserPreferences = (
  updates: Partial<UserPreferences>,
): UserPreferences => {
  const current = getUserPreferences();
  const updated = {
    ...current,
    ...updates,
    filters: {
      ...current.filters,
      ...updates.filters,
    },
  };

  setLocalStorage(
    STORAGE_KEYS.USERS_PREFERENCES as string,
    JSON.stringify(updated),
  );
  return updated;
};

export const clearUserPreferences = () => {
  setLocalStorage(
    STORAGE_KEYS.USERS_PREFERENCES as string,
    JSON.stringify(defaultPreferences),
  );
  return defaultPreferences;
};
