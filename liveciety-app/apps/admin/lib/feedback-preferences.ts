import { FeedbackPreferences, STORAGE_KEYS } from "./constants";
import { getLocalStorage, setLocalStorage } from "./local-storage";

const defaultPreferences: FeedbackPreferences = {
  view: "list",
  sortBy: "createdAt",
  sortOrder: "desc",
  groupBy: "status",
  filters: {
    status: [],
    priority: [],
    assignee: [],
    title: "",
  },
  showCompleted: false,
  columnVisibility: {},
  statusColumnVisibility: {
    new: true,
    in_progress: true,
    done: true,
    rejected: true,
  },
};

export const getFeedbackPreferences = (): FeedbackPreferences => {
  const stored = getLocalStorage(STORAGE_KEYS.FEEDBACK_PREFERENCES as string);
  if (!stored) return defaultPreferences;

  try {
    return JSON.parse(stored) as FeedbackPreferences;
  } catch (e) {
    console.error("Error parsing feedback preferences", e);
    return defaultPreferences;
  }
};

export const updateFeedbackPreferences = (
  updates: Partial<FeedbackPreferences>,
): FeedbackPreferences => {
  const current = getFeedbackPreferences();
  const updated = {
    ...current,
    ...updates,
    filters: {
      ...current.filters,
      ...updates.filters,
    },
  };

  setLocalStorage(STORAGE_KEYS.FEEDBACK_PREFERENCES as string, JSON.stringify(updated));
  return updated;
};

export const clearFeedbackPreferences = () => {
  setLocalStorage(
    STORAGE_KEYS.FEEDBACK_PREFERENCES as string,
    JSON.stringify(defaultPreferences),
  );
  return defaultPreferences;
};
