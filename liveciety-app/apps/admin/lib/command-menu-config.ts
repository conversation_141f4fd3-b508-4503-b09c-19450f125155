import {
  IconUserCircle,
  IconLayoutDashboard,
  IconSettings,
  IconCreditCard,
  IconFile,
  IconLifebuoy,
  IconHelp,
  IconBuildingSkyscraper,
  IconBookmark,
  IconSun,
  IconMoon,
  Icon,
  IconSquareRoundedCheckFilled,
} from "@tabler/icons-react";
import { getPlatformShortcuts } from "./utils";
import { useHelp } from "../context/help-wrapper";
import { useTheme } from "next-themes";
import { useTasks } from "@/context/tasks-wrapper";

export interface CommandItem {
  icon: Icon;
  label: string;
  shortcut?: string;
  href?: string;
  isExternal?: boolean;
  action?: () => void;
  buttonText?: string;
}

export interface CommandGroup {
  heading: string;
  items: CommandItem[];
}

export function useCommandGroups(): CommandGroup[] {
  const { openHelp } = useHelp();
  const { openCreateTask } = useTasks();
  const { setTheme } = useTheme();

  return [
    {
      heading: "Actions",
      items: [
        {
          icon: IconSquareRoundedCheckFilled,
          label: "Create Task",
          buttonText: "Create",
          action: openCreateTask,
          shortcut: "T",
        },
        {
          icon: IconLayoutDashboard,
          label: "Go to Dashboard",
          href: "dashboard",
          buttonText: "Open",
        },
        {
          icon: IconSettings,
          label: "Open Settings",
          href: "settings",
          buttonText: "Open",
        },
        {
          icon: IconSun,
          label: "Use Light Theme",
          action: () => setTheme("light"),
          buttonText: "Enable theme",
        },
        {
          icon: IconMoon,
          label: "Use Dark Theme",
          action: () => setTheme("dark"),
          buttonText: "Enable theme",
        },
        {
          icon: IconBookmark,
          label: "Add to Bookmarks",
          shortcut: "⌘D",
          buttonText: "Add to bookmarks",
          action: () => {
            if (typeof window !== "undefined") {
              const { shortcutSymbol } = getPlatformShortcuts();
              alert(`Press ${shortcutSymbol}D to bookmark this page.`);
            }
          },
        },
      ],
    },
    {
      heading: "Resources",
      items: [
        {
          icon: IconUserCircle,
          label: "Account Settings",
          href: "settings/account",
          buttonText: "Open",
        },
        {
          icon: IconBuildingSkyscraper,
          label: "Workspace Settings",
          href: "settings/workspace",
          buttonText: "Open",
        },
        {
          icon: IconCreditCard,
          label: "Billing",
          href: "settings/billing",
          buttonText: "Open",
        },
      ],
    },
    {
      heading: "Help & Support",
      items: [
        {
          icon: IconHelp,
          label: "Open Help Drawer",
          shortcut: "⌘H",
          action: openHelp,
          buttonText: "Open",
        },
        {
          icon: IconFile,
          label: "Convex Docs ↗",
          href: "https://docs.convex.dev",
          isExternal: true,
          buttonText: "Open new tab",
        },
        {
          icon: IconLifebuoy,
          label: "Support ↗",
          href: "https://discord.com/invite/nk6C2qTeCq",
          isExternal: true,
          buttonText: "Open new tab",
        },
      ],
    },
  ];
}
