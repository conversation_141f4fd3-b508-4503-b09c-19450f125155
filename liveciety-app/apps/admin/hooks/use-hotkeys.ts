import { useEffect } from "react";

type HotkeyCallback = (e: KeyboardEvent) => void;

interface HotkeyConfig {
  key: string;
  callback: HotkeyCallback;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  ignoreInputs?: boolean;
}

export function useHotkeys(configs: HotkeyConfig[]) {
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      if (
        (e.target as HTMLElement).tagName === "INPUT" ||
        (e.target as HTMLElement).tagName === "TEXTAREA"
      ) {
        return;
      }

      for (const config of configs) {
        const keyMatch = e.key.toLowerCase() === config.key.toLowerCase();
        const ctrlMatch = !config.ctrlKey || e.ctrlKey;
        const altMatch = !config.altKey || e.altKey;
        const shiftMatch = !config.shiftKey || e.shiftKey;
        const metaMatch = !config.metaKey || e.metaKey;
        const ignoreInputs = config.ignoreInputs ?? true;

        if (
          keyMatch &&
          ctrlMatch &&
          altMatch &&
          shiftMatch &&
          metaMatch &&
          (ignoreInputs ||
            !(
              (e.target as HTMLElement).tagName === "INPUT" ||
              (e.target as HTMLElement).tagName === "TEXTAREA"
            ))
        ) {
          e.preventDefault();
          config.callback(e);
          break;
        }
      }
    };

    window.addEventListener("keydown", handler);
    return () => window.removeEventListener("keydown", handler);
  }, [configs]);
}
