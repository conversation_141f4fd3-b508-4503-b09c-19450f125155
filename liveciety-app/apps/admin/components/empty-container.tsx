import React from "react";
import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { type Icon } from "@tabler/icons-react";

interface EmptyContainerProps {
  title: string;
  subtitle?: string;
  button?: string;
  icon?: Icon;
  onClick?: () => void;
  className?: string;
}
const EmptyContainer = ({
  title,
  subtitle,
  button,
  icon,
  onClick,
  className,
}: EmptyContainerProps) => {
  return (
    <div
      className={cn(
        "rounded-lg border border-dashed shadow-sm h-full flex items-center justify-center",
        className,
      )}
    >
      <div className="flex flex-col items-center gap-1 p-3 text-center">
        <h3 className="text-xl font-bold tracking-tight">{title}</h3>
        {subtitle && (
          <p className="text-xs text-muted-foreground">{subtitle}</p>
        )}
        {button && (
          <Button
            onClick={(e) => {
              e.preventDefault();
              if (onClick) onClick();
            }}
            className="mt-2 mr-2 bg-blue-700 hover:bg-blue-900 text-white py-1 px-2 rounded-lg w-fit"
          >
            {icon &&
              React.createElement(icon, {
                className: "mr-1 w-4 h-4 stroke-white",
              })}{" "}
            {button}
          </Button>
        )}
      </div>
    </div>
  );
};

export default EmptyContainer;
