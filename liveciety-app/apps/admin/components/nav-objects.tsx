"use client";

import { IconChevronDown, type Icon } from "@tabler/icons-react";
import Link from "next/link";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@workspace/ui/components/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@radix-ui/react-collapsible";
import { cn } from "@workspace/ui/lib/utils";

export function NavObjects({
  items,
}: {
  items: {
    name: string;
    url: string;
    icon: Icon;
    isActive?: boolean;
  }[];
}) {
  return (
    <Collapsible
      defaultOpen
      className="group/collapsible group-data-[collapsible=icon]:mb-6"
    >
      <SidebarGroup>
        <SidebarGroupLabel className="group-data-[collapsible=icon]:opacity-100 !p-0">
          <CollapsibleTrigger
            className={cn(
              "h-8",
              "peer/menu-button flex w-full items-center overflow-hidden rounded-md text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",
              "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
            )}
          >
            <>
              <div className="flex items-center gap-2 group-data-[collapsible=icon]:hidden px-2">
                <IconChevronDown
                  height={16}
                  width={16}
                  className="transition-transform group-data-[state=open]/collapsible:rotate-180"
                />
                <span className="text-xs">Objects</span>
              </div>
            </>
          </CollapsibleTrigger>
        </SidebarGroupLabel>
        <CollapsibleContent>
          <SidebarMenu>
            {items.length < 1 && (
              <span
                className={cn(
                  "text-xs cursor-default text-foreground/50 pl-3",
                  "group-data-[collapsible=icon]:hidden",
                )}
              >
                No objects yet
              </span>
            )}

            {items.map((item) => (
              <SidebarMenuItem key={item.name}>
                <SidebarMenuButton asChild isActive={item.isActive}>
                  <Link href={`/v/${item.url}`}>
                    <item.icon />
                    <span>{item.name}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </CollapsibleContent>
      </SidebarGroup>
    </Collapsible>
  );
}
