"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { FEEDBACK_STATUS, FeedbackStatus, TASK_STATUS } from "@/lib/constants";
import { useEffect, useId, useState } from "react";
import { TaskStatus } from "@workspace/backend/convex/lib/types";
import React from "react";
import { IconSquareRoundedCheckFilled } from "@tabler/icons-react";
import { IconCircleDotted } from "@tabler/icons-react";

interface StatusSelectorProps {
  status: TaskStatus | FeedbackStatus;
  taskId?: string;
  feedbackId?: string;
  onStatusChange?: (
    taskId: string,
    feedbackId: string,
    status: TaskStatus | FeedbackStatus,
  ) => void;
}

export function StatusSelector({
  status,
  taskId,
  feedbackId,
  onStatusChange,
}: StatusSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<TaskStatus | FeedbackStatus>(status);

  useEffect(() => {
    setValue(status);
  }, [status]);

  const handleStatusChange = (statusId: string) => {
    const newStatus = (feedbackId ? statusId as FeedbackStatus : statusId as TaskStatus);
    setValue(newStatus);
    setOpen(false);

    if (taskId && feedbackId && onStatusChange) {
      onStatusChange(taskId, feedbackId, newStatus);
    }
  };

  return (
    <div className="*:not-first:mt-2" onClick={(e) => e.stopPropagation()}>
      <Popover
        open={open}
        onOpenChange={(isOpen) => {
          setOpen(isOpen);
        }}
      >
        <PopoverTrigger asChild>
          <Button
            id={id}
            className="size-7 flex items-center justify-center"
            size="icon"
            variant="ghost"
            role="combobox"
            aria-expanded={open}
            onClick={(e) => e.stopPropagation()}
          >
            {(() => {
              let selectedItem;

              if (feedbackId) {
                selectedItem = FEEDBACK_STATUS.find(
                  (item) => item.value === value,
                );
              } else {
                selectedItem = TASK_STATUS.find(
                  (item) => item.value === value,
                );
              }

              if (selectedItem && !feedbackId) {
                if ('icon' in selectedItem && selectedItem.icon) {
                  const Icon = selectedItem.icon;
                  const color = selectedItem.color || "text-zinc-500";
                  return <Icon className={`size-4 ${color}`} />;
                }
              }
              return <IconCircleDotted className="size-4 text-zinc-500" />;
            })()}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="border-input p-0 bg-zinc-100 dark:bg-zinc-900 min-w-[var(--radix-popper-anchor-width)]"
          align="start"
        >
          <Command className="bg-zinc-100 dark:bg-zinc-900">
            <CommandInput placeholder="Set status..." />
            <CommandList className="!h-fit">
              <CommandEmpty>No status found.</CommandEmpty>
              <CommandGroup>
                {feedbackId 
                  ? FEEDBACK_STATUS.map((item) => (
                    <CommandItem
                      key={item.value}
                      value={item.value}
                      onSelect={handleStatusChange}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <IconCircleDotted className="size-4 text-zinc-500" />
                        {item.label}
                      </div>
                      {value === item.value && (
                        <IconSquareRoundedCheckFilled
                          size={16}
                          className="ml-auto text-blue-400"
                        />
                      )}
                    </CommandItem>
                  ))
                  : TASK_STATUS.map((item) => (
                    <CommandItem
                      key={item.value}
                      value={item.value}
                      onSelect={handleStatusChange}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        {React.createElement(item.icon, {
                          className: `size-4 ${item.color}`,
                        })}
                        {item.label}
                      </div>
                      {value === item.value && (
                        <IconSquareRoundedCheckFilled
                          size={16}
                          className="ml-auto text-blue-400"
                        />
                      )}
                    </CommandItem>
                  ))
                }
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
