import { IconCopy, <PERSON>con<PERSON>he<PERSON> } from "@tabler/icons-react";
import { toast } from "sonner";

export function CopyButton({ text }: { text: string }) {
  return (
    <button
      type="button"
      onClick={() => {
        navigator.clipboard.writeText(text);
        toast.custom((t) => (
          <div className="bg-green-500 text-white p-2 rounded-md">
            <IconCheck className="h-4 w-4" />
            Copied to clipboard
          </div>
        ));
      }}
      className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
    >
      <IconCopy className="h-4 w-4 text-gray-500" />
    </button>
  );
}
