"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";

import { IconCalendarClock } from "@tabler/icons-react";
import { Alert } from "@workspace/ui/components/alert";

import { AlertTitle } from "@workspace/ui/components/alert";
import { Workspace } from "@workspace/backend/convex/lib/types";
import { useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";

export function TrialNotice({
  workspace,
  sidebar = false,
}: {
  workspace: Workspace;
  sidebar?: boolean;
}) {
  const createCustomerPortal = useAction(api.stripe.createCustomerPortal);

  const freeTrial = workspace?.subscription?.status === "trialing";
  const freeTrialDaysLeft = workspace?.subscription?.currentPeriodEnd
    ? Math.ceil(
        (workspace.subscription.currentPeriodEnd * 1000 - Date.now()) /
          (1000 * 60 * 60 * 24),
      )
    : 0;

  if (!freeTrial) return null;

  const handleCreateCustomerPortal = async () => {
    if (!workspace?._id) {
      console.error("No workspace ID found");
      return;
    }

    if (!workspace.subscription?.customerId) {
      console.error("No Stripe customer ID associated with this workspace");
      return;
    }

    try {
      const customerPortalUrl = await createCustomerPortal({
        wsId: workspace._id,
      });

      if (customerPortalUrl) {
        window.location.href = customerPortalUrl;
      }
    } catch (error) {
      console.error("Error creating customer portal:", error);
    }
  };

  return (
    <>
      {!sidebar ? (
        <Alert
          className="bg-muted p-4 mb-8 border border-zinc-200 dark:border-zinc-700 flex"
          variant="default"
        >
          <AlertTitle className="flex items-center gap-2 justify-between w-full">
            <div className="flex items-center gap-2 text-md">
              <IconCalendarClock className="w-6 h-6" />
              You have {freeTrialDaysLeft} days left in your free trial
            </div>

            <Button
              variant="default"
              size="sm"
              className="bg-blue-500 hover:bg-blue-600 text-white"
              onClick={handleCreateCustomerPortal}
            >
              Add billing details
            </Button>
          </AlertTitle>
        </Alert>
      ) : (
        <div>DISPLAY IN SIDEBAR</div>
      )}
    </>
  );
}
