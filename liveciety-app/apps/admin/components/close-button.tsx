import { IconX } from "@tabler/icons-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";

export function CloseButton({
  className,
  onClick,
}: {
  className?: string;
  onClick?: () => void;
}) {
  return (
    <Button
      size="icon"
      variant="ghost"
      className={cn("rounded-md p-1 h-7 w-7 hover:bg-accent", className)}
      onClick={onClick}
    >
      <IconX />
    </Button>
  );
}
