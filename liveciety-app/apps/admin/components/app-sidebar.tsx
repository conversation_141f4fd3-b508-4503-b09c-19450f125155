"use client";

import * as React from "react";
import {
  IconCircleCheck,
  IconDashboard,
  IconHelp,
  IconUserCircle,
  IconLogout,
  IconBuildingSkyscraper,
  IconSettings,
  IconUser,
  IconUsers,
  IconBell,
  IconKey,
  IconCreditCard,
  IconChevronLeft,
  IconSearch,
  IconMail,
  IconCreditCardPay,
  IconTrash,
  IconPlus,
  IconSquareRoundedCheck,
  IconSquareRoundedCheckFilled,
  IconMessage,
  IconVersions,
} from "@tabler/icons-react";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useHelp } from "@/context/help-wrapper";

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  useSidebar,
} from "@workspace/ui/components/sidebar";
import { NavMain } from "@/components/nav-main";
import { NavSecondary } from "@/components/nav-secondary";
import {
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@workspace/ui/components/dropdown-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { api } from "@workspace/backend/convex/_generated/api";
import { CommandMenu } from "./command-dialog";
import { useRouter, usePathname } from "next/navigation";
import { useAuthActions } from "@convex-dev/auth/react";
import { Input } from "@workspace/ui/components/input";
import {
  Preloaded,
  useMutation,
  usePreloadedQuery,
  useQuery,
} from "convex/react";
import { NavFavorites } from "./nav-favorites";
import { NavObjects } from "./nav-objects";
import { getAvatarImageUrl } from "@/lib/utils";

interface NavItem {
  title: string;
  url?: string;
  icon?: typeof IconSettings;
  onClick?: () => void;
  count?: number;
  subItems?: NavSubItem[];
}

interface NavSubItem {
  title: string;
  url: string;
  icon?: typeof IconSettings;
}

interface NavSection {
  heading: string;
  title?: string;
  url?: string;
  icon?: typeof IconSettings;
  items?: NavItem[] | ((role?: string) => NavItem[]);
  subItems?: NavSubItem[];
}

const isNavSection = (item: NavItem | NavSection): item is NavSection => {
  return "heading" in item;
};

const isNavItem = (item: NavItem | NavSection): item is NavItem => {
  return !isNavSection(item);
};

const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "dashboard",
      icon: IconDashboard,
    },
    {
      title: "Tasks",
      url: "tasks",
      icon: IconSquareRoundedCheck,
      count: "",
    },
    {
      title: "Feedback",
      url: "feedback",
      icon: IconMessage,
    },
    {
      title: "Changelog",
      url: "changelog",
      icon: IconVersions,
    },
  ],
  navSecondary: (openHelp: () => void) => [
    {
      title: "Get Help",
      onClick: openHelp,
      icon: IconHelp,
    },
  ],
  objectsNav: [
    {
      name: "Users",
      url: "users",
      icon: IconUser,
    },
  ],
  settingsNav: [
    {
      heading: "Account",
      items: [
        {
          title: "Account",
          url: "account",
          icon: IconUser,
          subItems: [
            { title: "Change email", url: "account/email", icon: IconMail },
            {
              title: "Change password",
              url: "account/password",
              icon: IconKey,
            },
            {
              title: "Update password",
              url: "account/update-password",
              icon: IconKey,
            },
            { title: "Delete account", url: "account/delete", icon: IconTrash },
          ],
        },
        {
          title: "Appearance",
          url: "appearance",
          icon: IconSettings,
        },
        {
          title: "Notifications",
          url: "notifications",
          icon: IconBell,
          subItems: [
            {
              title: "Collaboration notifications",
              url: "notifications/collaboration",
              icon: IconBell,
            },
          ],
        },
      ],
    },
  ],
};

export function AppSidebar({
  preloadedUser,
  preloadedTaskCount,
  preloadedFavorites,
  ...sidebarProps
}: React.ComponentProps<typeof Sidebar> & {
  preloadedUser: Preloaded<typeof api.users.viewer>;
  preloadedTaskCount: Preloaded<typeof api.tasks.count>;
  preloadedFavorites: Preloaded<typeof api.favorites.get>;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { isMobile } = useSidebar();
  const { signOut } = useAuthActions();

  const user = usePreloadedQuery(preloadedUser);
  const taskCount = preloadedTaskCount
    ? usePreloadedQuery(preloadedTaskCount)
    : 0;

  const favorites =
    usePreloadedQuery(preloadedFavorites)
      ?.map((f: any) => {
        if (!f) return null;

        return {
          ...f,
          name: f.name || "Untitled",
          url: `/v/${f.tableName || "users"}/${f.objectId}`,
          icon: (f.tableName === "users" || !f.tableName) ? IconUser : 
                f.tableName === "feedback" ? IconMessage : 
                IconSquareRoundedCheck,
          isActive: pathname?.includes(f.objectId),
          objectId: f.objectId,
          tableName: f.tableName || "users",
        } as const;
      })
      .filter((f): f is NonNullable<typeof f> => f !== null) || [];

  const [searchQuery, setSearchQuery] = React.useState("");
  const isSettingsPage = pathname?.includes("/settings");

  const filterItems = (items: (NavItem | NavSection)[]) => {
    if (!items) return [];
    if (!searchQuery.trim()) return items;

    return items.filter((item) => {
      if (!item) return false;

      const matchesSearch = item.title
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase());
      const hasMatchingSubItems = item.subItems?.some((subItem) =>
        subItem?.title?.toLowerCase().includes(searchQuery.toLowerCase()),
      );

      let itemsMatchSearch = false;
      if (isNavSection(item) && item.items) {
        if (typeof item.items === "function") {
          const functionItems = item.items();
          itemsMatchSearch =
            functionItems?.some((subItem) =>
              subItem?.title?.toLowerCase().includes(searchQuery.toLowerCase()),
            ) || false;
        } else {
          itemsMatchSearch =
            item.items?.some((subItem) =>
              subItem?.title?.toLowerCase().includes(searchQuery.toLowerCase()),
            ) || false;
        }
      }

      return matchesSearch || hasMatchingSubItems || itemsMatchSearch;
    });
  };

  const renderSettingsNav = (items: (NavItem | NavSection)[]) => {
    if (!items) return null;

    return items.map((item) => {
      if (!item) return null;

      if (isNavSection(item)) {
        return (
          <React.Fragment key={`heading-${item.heading}`}>
            <div className="px-2 pt-4 pb-2">
              <h3 className="text-xs font-normal text-gray-500">
                {item.heading}
              </h3>
            </div>
            {item.items &&
              renderSettingsNav(
                typeof item.items === "function"
                  ? filterItems(item.items())
                  : filterItems(item.items),
              )}
          </React.Fragment>
        );
      }

      return (
        <SidebarMenuItem key={`menu-${item.url}`}>
          <SidebarMenuButton
            onClick={() => router.push(`/v/settings/${item.url}`)}
            className={
              pathname?.includes(item.url || "") ? "bg-sidebar-accent" : ""
            }
          >
            {item.icon &&
              React.createElement(item.icon, { className: "h-4 w-4" })}
            <span>{item.title}</span>
          </SidebarMenuButton>
          {item.subItems && item.subItems.length > 0 && searchQuery.trim() && (
            <SidebarMenuSub>
              {item.subItems
                .filter((subItem) =>
                  subItem?.title
                    ?.toLowerCase()
                    .includes(searchQuery.toLowerCase()),
                )
                .map((subItem) => (
                  <SidebarMenuItem key={`sub-${item.url}-${subItem.url}`}>
                    <SidebarMenuButton
                      onClick={() => router.push(`/v/settings/${subItem.url}`)}
                      className={
                        pathname?.includes(subItem.url)
                          ? "bg-sidebar-accent"
                          : ""
                      }
                    >
                      <span>{subItem.title}</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
            </SidebarMenuSub>
          )}
        </SidebarMenuItem>
      );
    });
  };

  const navMainItems = React.useMemo(() => {
    return data.navMain.map((item) => {
      if (item.url === "tasks" && taskCount && taskCount > 0) {
        return { ...item, count: taskCount };
      }
      return item;
    });
  }, [taskCount]);

  if (isSettingsPage) {
    return (
      <Sidebar collapsible="offcanvas" {...sidebarProps}>
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                onClick={() => router.push(`/v/dashboard`)}
                className="gap-2"
              >
                <IconChevronLeft className="h-4 w-4" />
                <span>Settings</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <div className="pt-2">
                <div className="relative">
                  <IconSearch className="absolute left-3 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    id="search-settings"
                    name="search-settings"
                    type="search"
                    autoComplete="off"
                    data-lpignore="true"
                    data-form-type="other"
                    placeholder="Search settings..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 bg-transparent"
                  />
                </div>
              </div>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarMenu className="px-2">
            {renderSettingsNav(filterItems(data.settingsNav))}
          </SidebarMenu>
        </SidebarContent>
      </Sidebar>
    );
  }

  return (
    <Sidebar collapsible="offcanvas" {...sidebarProps}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={getAvatarImageUrl(user?.image)} alt={user?.name} />
                    <AvatarFallback
                      className="rounded-lg"
                      style={{ backgroundColor: user?.color }}
                    >
                      {user?.name?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">{user?.name}</span>
                  </div>
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-(--radix-dropdown-menu-trigger-width) min-w-64 rounded-lg"
                side={isMobile ? "bottom" : "bottom"}
                align="end"
                sideOffset={4}
              >
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={() => router.push(`/v/settings/account`)}
                  >
                    <IconUserCircle />
                    Account Settings
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => void signOut()}>
                  <IconLogout />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <CommandMenu />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain
          router={router}
          items={navMainItems}
          preloadedUser={preloadedUser}
        />
        <NavFavorites
          items={favorites
            .filter((f) => f.objectId && ["tasks", "users", "feedback"].includes(f.tableName || "users"))}
        />
        <NavObjects items={data.objectsNav} />
        {/* <NavSecondary items={data.navSecondary(openHelp)} className="mt-auto" /> */}
      </SidebarContent>
    </Sidebar>
  );
}
