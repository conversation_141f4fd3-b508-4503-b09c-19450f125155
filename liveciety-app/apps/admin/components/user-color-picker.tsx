"use client";

import { useState, useRef } from "react";
import { ColorPicker } from "@/components/color-picker";
import { Button } from "@workspace/ui/components/button";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

export function UserColorPicker({ color, userId }: { color: string; userId: string }) {
  const [currentColor, setCurrentColor] = useState(color);
  const [pendingColor, setPendingColor] = useState(color);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const updateUser = useMutation(api.users.update);

  const handleColorChange = (newColor: string) => {
    setPendingColor(newColor);
    setError(null);
  };

  const handleSave = async () => {
    setLoading(true);
    setError(null);
    try {
      await updateUser({
        id: userId as Id<"users">,
        color: pendingColor,
      });
      setCurrentColor(pendingColor);
    } catch (err) {
      setError("Failed to update color");
      setPendingColor(currentColor);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setPendingColor(currentColor);
    setError(null);
  };

  return (
    <span className="flex items-center gap-2">
      <ColorPicker
        value={pendingColor}
        onChange={handleColorChange}
        className="w-6 h-6 rounded-full"
        disabled={loading}
        onSave={handleSave}
        onCancel={handleCancel}
      />
      {loading && <span className="text-xs text-gray-400">Saving...</span>}
      {error && <span className="text-xs text-red-500">{error}</span>}
    </span>
  );
} 