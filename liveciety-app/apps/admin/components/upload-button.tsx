import {
  UploadButton as BaseUploadButton,
  UploadFileResponse,
} from "@xixixao/uploadstuff/react";
import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { IconTrash } from "@tabler/icons-react";

export interface UploadButtonProps {
  onUploadComplete?: (uploaded: UploadFileResponse[]) => void;
  onUploadError?: (error: unknown) => void;
  onRemove?: () => void;
  generateUploadUrl: () => Promise<string>;
  fileTypes?: string[];
  isUploading?: boolean;
  showRemoveButton?: boolean;
  className?: string;
  buttonClassName?: string;
  removeButtonClassName?: string;
}

export function UploadButton({
  onUploadComplete,
  onUploadError,
  onRemove,
  generateUploadUrl,
  fileTypes = ["image/*"],
  isUploading = false,
  showRemoveButton = true,
  className,
  buttonClassName,
  removeButtonClassName,
}: UploadButtonProps) {
  return (
    <div className={cn("flex flex-row space-x-1 items-center", className)}>
      <BaseUploadButton
        className={() =>
          cn(
            "h-8 px-4 py-2 has-[>svg]:px-3 cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive text-sm border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            buttonClassName,
          )
        }
        uploadUrl={generateUploadUrl}
        fileTypes={fileTypes}
        onUploadComplete={onUploadComplete}
        onUploadError={(error) => {
          if (onUploadError) {
            onUploadError(error);
          } else {
            console.error("Upload error:", error);
          }
        }}
        content={(progress: number | null) => {
          if (progress === null) {
            return "Upload";
          }
          return `${progress}%`;
        }}
      />
      {showRemoveButton && onRemove && (
        <Button
          disabled={isUploading}
          onClick={onRemove}
          className={cn(
            "h-8 w-8 bg-transparent hover:bg-muted/50 text-muted-foreground hover:text-primary cursor-pointer border border-input",
            removeButtonClassName,
          )}
        >
          <IconTrash className="text-red-600" />
        </Button>
      )}
    </div>
  );
}
