import React from "react";
import { preloadQuery } from "convex/nextjs";
import { api } from "@workspace/backend/convex/_generated/api";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";
import { ClientWrapper } from "./client-wrapper";

export default async function AppWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const token = await convexAuthNextjsToken();
  const preloadedUser = await preloadQuery(api.users.viewer, {}, { token });
  const preloadedTasks = await preloadQuery(
    api.tasks.getAll,
    { favorites: false },
    { token },
  );

  return (
    <ClientWrapper
      preloadedData={{
        preloadedUser,
        preloadedTasks,
      }}
    >
      {children}
    </ClientWrapper>
  );
}
