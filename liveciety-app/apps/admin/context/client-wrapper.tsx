"use client";

import React from "react";
import { PreloadedDataProvider } from "./preloaded-data-context";
import { PreloadedData } from "./preloaded-data-context";

export function ClientWrapper({
  children,
  preloadedData,
}: {
  children: React.ReactNode;
  preloadedData: PreloadedData;
}) {
  if (!preloadedData.preloadedUser) {
    return <div>Loading...</div>;
  }

  return (
    <PreloadedDataProvider preloadedData={preloadedData}>
      <div className="overflow-y-auto">{children}</div>
    </PreloadedDataProvider>
  );
}
