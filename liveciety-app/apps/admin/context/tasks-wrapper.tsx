"use client";

import * as React from "react";
import { TaskStatus } from "@/lib/constants";
import { CreateTaskModal } from "@/components/tasks/create-task-modal";
import { useHotkeys } from "@/hooks/use-hotkeys";

interface TasksContextType {
  openCreateTask: (defaultStatus?: TaskStatus) => void;
}

const TasksContext = React.createContext<TasksContextType | undefined>(
  undefined,
);

export function useTasks() {
  const context = React.useContext(TasksContext);
  if (!context) {
    throw new Error("useTasks must be used within a TasksProvider");
  }
  return context;
}

export function TasksProvider({ children }: { children: React.ReactNode }) {
  const [isCreateModalOpen, setIsCreateModalOpen] = React.useState(false);
  const [defaultStatus, setDefaultStatus] = React.useState<
    TaskStatus | undefined
  >();

  useHotkeys([
    {
      key: "t",
      callback: () => {
        if (!isCreateModalOpen) {
          setIsCreateModalOpen(true);
        }
      },
      ignoreInputs: true,
    },
  ]);

  const openCreateTask = React.useCallback((status?: TaskStatus) => {
    setDefaultStatus(status);
    setIsCreateModalOpen(true);
  }, []);

  const handleModalOpenChange = (open: boolean) => {
    setIsCreateModalOpen(open);
    if (!open) {
      setDefaultStatus(undefined);
    }
  };

  return (
    <TasksContext.Provider value={{ openCreateTask }}>
      {children}
      <CreateTaskModal
        open={isCreateModalOpen}
        onOpenChange={handleModalOpenChange}
        defaultStatus={defaultStatus}
      />
    </TasksContext.Provider>
  );
}
