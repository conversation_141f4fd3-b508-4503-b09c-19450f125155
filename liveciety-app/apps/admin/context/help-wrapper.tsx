"use client";

import { createContext, useContext, useState, useEffect } from "react";

interface HelpContextType {
  isHelpOpen: boolean;
  openHelp: () => void;
  closeHelp: () => void;
  shortcutSymbol: string;
}

const HelpContext = createContext<HelpContextType | undefined>(undefined);

export function HelpProvider({ children }: { children: React.ReactNode }) {
  const [isHelpOpen, setIsHelpOpen] = useState(false);
  const [shortcutSymbol, setShortcutSymbol] = useState("Ctrl");

  useEffect(() => {
    const isMac =
      typeof window !== "undefined" &&
      navigator.userAgent.toLowerCase().indexOf("mac") !== -1;
    setShortcutSymbol(isMac ? "⌘" : "Ctrl");

    const down = (e: KeyboardEvent) => {
      if (e.key === "h" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setIsHelpOpen((open) => !open);
      }
    };

    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, []);

  const openHelp = () => setIsHelpOpen(true);
  const closeHelp = () => setIsHelpOpen(false);

  return (
    <HelpContext.Provider
      value={{ isHelpOpen, openHelp, closeHelp, shortcutSymbol }}
    >
      {children}
    </HelpContext.Provider>
  );
}

export function useHelp() {
  const context = useContext(HelpContext);
  if (context === undefined) {
    throw new Error("useHelp must be used within a HelpProvider");
  }
  return context;
}
