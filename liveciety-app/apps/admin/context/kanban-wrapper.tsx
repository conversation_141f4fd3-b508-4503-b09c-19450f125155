"use client";

import React, { createContext, useContext, useState } from "react";
import { Task, TaskStatus } from "@workspace/backend/convex/lib/types";

interface KanbanContextType {
  hoveredColumn: TaskStatus | null;
  setHoveredColumn: (column: TaskStatus | null) => void;
  selectedTasks: Set<string>;
  setSelectedTasks: (tasks: Set<string>) => void;
  hiddenStatuses: TaskStatus[];
  setHiddenStatuses: (statuses: TaskStatus[]) => void;
  draggedTask: Task | null;
  setDraggedTask: (task: Task | null) => void;
  dragOverColumn: TaskStatus | null;
  setDragOverColumn: (column: TaskStatus | null) => void;
  dragOverTask: Task | null;
  setDragOverTask: (task: Task | null) => void;
  dropPosition: "before" | "after" | null;
  setDropPosition: (position: "before" | "after" | null) => void;
}

const KanbanContext = createContext<KanbanContextType | undefined>(undefined);

export function KanbanProvider({ children }: { children: React.ReactNode }) {
  const [hoveredColumn, setHoveredColumn] = useState<TaskStatus | null>(null);
  const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
  const [hiddenStatuses, setHiddenStatuses] = useState<TaskStatus[]>([]);
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);
  const [dragOverColumn, setDragOverColumn] = useState<TaskStatus | null>(null);
  const [dragOverTask, setDragOverTask] = useState<Task | null>(null);
  const [dropPosition, setDropPosition] = useState<"before" | "after" | null>(
    null,
  );

  return (
    <KanbanContext.Provider
      value={{
        hoveredColumn,
        setHoveredColumn,
        selectedTasks,
        setSelectedTasks,
        hiddenStatuses,
        setHiddenStatuses,
        draggedTask,
        setDraggedTask,
        dragOverColumn,
        setDragOverColumn,
        dragOverTask,
        setDragOverTask,
        dropPosition,
        setDropPosition,
      }}
    >
      {children}
    </KanbanContext.Provider>
  );
}

export function useKanban() {
  const context = useContext(KanbanContext);
  if (context === undefined) {
    throw new Error("useKanban must be used within a KanbanProvider");
  }
  return context;
}
