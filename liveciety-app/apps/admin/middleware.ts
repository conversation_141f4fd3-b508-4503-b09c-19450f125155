import {
  convexAuthNextjsMiddleware,
  createRouteMatcher,
  nextjsMiddlewareRedirect,
} from "@convex-dev/auth/nextjs/server";
import { fetchQuery } from "convex/nextjs";
import { api } from "@workspace/backend/convex/_generated/api";
import { NextResponse } from "next/server";

const isMainRoute = createRouteMatcher(["/"]);
const isSignInPage = createRouteMatcher(["/login"]);
const isProtectedRoute = createRouteMatcher(["/dashboard(.*)"]);
const isNoAccessPage = createRouteMatcher(["/no-access"]);
const isDashboardRoute = createRouteMatcher(["/dashboard"]);
const isSystemRoute = createRouteMatcher([
  "/invite(.*)",
  "/settings(.*)",
  "/signup(.*)",
  "/no-access(.*)",
]);

export default convexAuthNextjsMiddleware(async (request, { convexAuth }) => {
  if (request.headers.get("x-middleware-subrequest")) {
    return new NextResponse(null, { status: 403 });
  }

  if (isSignInPage(request) || isNoAccessPage(request)) {
    return NextResponse.next();
  }

  const isAuthenticated = await convexAuth.isAuthenticated();

  if (
    !isAuthenticated &&
    (isProtectedRoute(request) ||
      isMainRoute(request) ||
      isSystemRoute(request))
  ) {
    return nextjsMiddlewareRedirect(request, "/login");
  }

  if (isAuthenticated) {
    const token = await convexAuth.getToken();
    const user = await fetchQuery(api.users.viewer, {}, { token });

    if (isSignInPage(request)) {
      return nextjsMiddlewareRedirect(request, `/dashboard`);
    }

    if (isDashboardRoute(request)) {
      return NextResponse.next();
    }

    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    if (pathParts[1] === "dashboard" && pathParts.length > 2) {
      return nextjsMiddlewareRedirect(request, "/no-access");
    }

    if (isSystemRoute(request)) {
      return NextResponse.next();
    }
  }

  return NextResponse.next();
});

export const config = {
  matcher: ["/((?!.*\\..*|_next).*)", "/", "/(api|trpc)(.*)"],
};
