"use client";

import { ConvexReactClient } from "convex/react";
import { ConvexAuthNextjsProvider } from "@convex-dev/auth/nextjs";
import { ReactNode } from "react";
import dynamic from "next/dynamic";
import type { ComponentProps } from "react";

import type ConvexPanelType from "convex-panel";

const ConvexPanel = dynamic<ComponentProps<typeof ConvexPanelType>>(
  () => import("convex-panel"),
  {
    ssr: false,
  },
);

const convex = new ConvexReactClient(
  process.env.NEXT_PUBLIC_CONVEX_URL! as string,
);

export function ConvexClientProvider({ children }: { children: ReactNode }) {
  return (
    <ConvexAuthNextjsProvider client={convex}>
      {children}
      {/* TODO: Add for local development */}
      {/* <ConvexPanel
        accessToken={process.env.NEXT_PUBLIC_ACCESS_TOKEN!}
        deployKey={process.env.NEXT_PUBLIC_DEPLOY_KEY!}
      /> */}
    </ConvexAuthNextjsProvider>
  );
}
