"use client";

import React, { useEffect, useState, useMemo } from "react";
import TaskList from "./list";
import { getLocalStorage, setLocalStorage } from "@/lib/local-storage";
import { TableToolbar } from "./_components/table-toolbar";
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { columns } from "./list/_components/columns";
import { Task, User } from "@workspace/backend/convex/lib/types";
import { usePreloadedQuery } from "convex/react";
import { STORAGE_KEYS, TASK_STATUS, TaskStatus } from "@/lib/constants";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { CreateTaskButton } from "@/components/tasks/create-task-button";
import { TableSettings } from "./list/_components/table-settings";
import BottomBarTasks from "@/components/tasks/tasks-bottom-bar";
import {
  getTaskPreferences,
  updateTaskPreferences,
} from "@/lib/task-preferences";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { KanbanProvider } from "@/context/kanban-wrapper";
import TasksBoard from "./board";
import CalendarPage from "./calendar";

const TaskContent = () => {
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser) as User;
  const tasksQuery = useQuery(api.tasks.getAll, { favorites: false });

  const tasks = useMemo(() => tasksQuery || [], [tasksQuery]);

  const tableData = useMemo(
    () =>
      tasks.map((task) => ({
        ...task,
      })),
    [tasks],
  );

  const [isMounted, setIsMounted] = useState(false);

  const preferences = getTaskPreferences();
  const [sorting, setSorting] = useState<SortingState>(
    preferences.sortBy
      ? [{ id: preferences.sortBy, desc: preferences.sortOrder === "desc" }]
      : [],
  );
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    preferences.columnVisibility || {},
  );
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(
    Object.entries(preferences.filters || {})
      .map(([id, value]) => ({
        id,
        value: value || [],
      }))
      .filter((filter) => filter.value.length > 0),
  );
  const [rowSelection, setRowSelection] = useState({});
  const [selectedTasks, setSelectedTasks] = useState<Task[]>([]);
  const [value, setValue] = useState<string>(preferences.view);
  const [groupBy, setGroupBy] = useState(preferences.groupBy || "status");
  const [showCompletedTasks, setShowCompletedTasks] = useState(
    preferences.showCompleted ?? true,
  );
  const [statusVisibility, setStatusVisibility] = useState<
    Record<TaskStatus, boolean>
  >(() => {
    const savedVisibility = preferences.statusColumnVisibility;
    return TASK_STATUS.reduce(
      (acc, status) => ({
        ...acc,
        [status.value]: savedVisibility
          ? savedVisibility[status.value] !== false
          : true,
      }),
      {} as Record<TaskStatus, boolean>,
    );
  });

  useEffect(() => {
    updateTaskPreferences({ view: value });
  }, [value]);

  useEffect(() => {
    if (sorting.length > 0 && sorting[0]) {
      updateTaskPreferences({
        sortBy: sorting[0].id,
        sortOrder: sorting[0].desc ? "desc" : "asc",
      });
    }
  }, [sorting]);

  useEffect(() => {
    const filters = columnFilters.reduce(
      (acc, filter) => ({
        ...acc,
        [filter.id]: filter.value,
      }),
      {},
    );

    updateTaskPreferences({ filters });
  }, [columnFilters]);

  useEffect(() => {
    updateTaskPreferences({ groupBy });
  }, [groupBy]);

  useEffect(() => {
    updateTaskPreferences({ showCompleted: showCompletedTasks });
  }, [showCompletedTasks]);

  useEffect(() => {
    updateTaskPreferences({ columnVisibility });
  }, [columnVisibility]);

  useEffect(() => {
    updateTaskPreferences({ statusColumnVisibility: statusVisibility });
  }, [statusVisibility]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    setLocalStorage(STORAGE_KEYS.TASKS_VIEW ?? "", value);
  }, [value]);

  useEffect(() => {
    const storedValue = getLocalStorage(STORAGE_KEYS.TASKS_VIEW ?? "");
    if (storedValue) {
      setValue(storedValue);
    }
  }, []);

  const table = useReactTable({
    data: tableData as unknown as Task[],
    columns: columns as ColumnDef<Task>[],
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    enableSorting: true,
    enableMultiSort: false,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (tableData !== undefined && isMounted) {
      setIsLoading(false);
    }
  }, [tableData, isMounted]);

  useEffect(() => {
    if (table && isMounted) {
      const selectedRows = table.getFilteredSelectedRowModel().rows;
      const selectedTasksData = selectedRows.map((row) => row.original as Task);
      setSelectedTasks(selectedTasksData);
    }
  }, [rowSelection, table, isMounted]);

  const handleStatusVisibilityChange = (
    status: TaskStatus,
    visible: boolean,
  ) => {
    setStatusVisibility((prev) => ({ ...prev, [status]: visible }));
  };

  if (!isMounted) {
    return null;
  }

  return (
    <KanbanProvider>
      <>
        <div
          className={
            "border-b border-muted p-3 w-full flex justify-between items-center"
          }
        >
          <TableToolbar
            table={table}
            groupBy={groupBy}
            onGroupByChange={setGroupBy}
          />
          <div className={"flex justify-end space-x-2 flex-row items-center"}>
            <CreateTaskButton icon={true} />
            <TableSettings
              showCompletedTasks={showCompletedTasks}
              onShowCompletedTasksChange={setShowCompletedTasks}
              statusVisibility={statusVisibility}
              onStatusVisibilityChange={handleStatusVisibilityChange}
              table={table}
              view={value}
              onViewChange={setValue}
              groupBy={groupBy}
              onGroupByChange={setGroupBy}
              user={user}
            />
          </div>
        </div>

        <div>
          {value === "list" && (
            <TaskList
              table={table}
              isLoading={isLoading}
              groupBy={groupBy}
              showCompletedTasks={showCompletedTasks}
              onStatusVisibilityChange={handleStatusVisibilityChange}
              columns={columns as ColumnDef<Task>[]}
            />
          )}
          {value === "kanban" && (
            <TasksBoard
              table={table}
              statusVisibility={statusVisibility}
              onStatusVisibilityChange={handleStatusVisibilityChange}
            />
          )}

          {value === "calendar" && <CalendarPage />}
        </div>

        <BottomBarTasks
          selectedTasks={selectedTasks}
          onDeselectAll={() => {
            setRowSelection({});
            setSelectedTasks([]);
          }}
        />
      </>
    </KanbanProvider>
  );
};

export default TaskContent;
