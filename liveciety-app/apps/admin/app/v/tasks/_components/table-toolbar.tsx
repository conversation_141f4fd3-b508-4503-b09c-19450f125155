import { Table } from "@tanstack/react-table";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { DataTableFacetedFilter } from "./faceted-filter";
import { TASK_STATUS, TASK_PRIORITY } from "@/lib/constants";
import { IconX } from "@tabler/icons-react";
import { Separator } from "@workspace/ui/components/separator";
import React from "react";
import { DataTableSort } from "./data-table-sort";
import { DataTableGroup } from "./data-table-group";
import {
  clearTaskPreferences,
  getTaskPreferences,
} from "@/lib/task-preferences";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  groupBy: string;
  onGroupByChange: (value: string) => void;
}

export function TableToolbar<TData>({
  table,
  groupBy,
  onGroupByChange,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;
  const sorting = table.getState().sorting;

  const handleReset = React.useCallback(() => {
    table.resetColumnFilters();
    table.resetSorting();
    onGroupByChange("status");
    clearTaskPreferences();
  }, [table, onGroupByChange]);

  const shouldShowReset = React.useMemo(() => {
    const preferences = getTaskPreferences();

    const defaultPreferences = {
      sortBy: "dueDate",
      sortOrder: "asc",
      groupBy: "status",
      filters: {
        status: [],
        priority: [],
        assignee: [],
        title: "",
      },
    };

    const currentSorting = sorting[0];
    const sortingDiffers =
      currentSorting &&
      (currentSorting.id !== defaultPreferences.sortBy ||
        currentSorting.desc !== (defaultPreferences.sortOrder === "desc"));

    const groupingDiffers = groupBy !== defaultPreferences.groupBy;

    const filtersDiffer = table.getState().columnFilters.some((filter) => {
      const defaultValue =
        defaultPreferences.filters[
          filter.id as keyof typeof defaultPreferences.filters
        ];
      if (Array.isArray(defaultValue)) {
        return (filter.value as string[])?.length > 0;
      }
      return filter.value !== defaultValue;
    });

    return sortingDiffers || groupingDiffers || filtersDiffer;
  }, [sorting, groupBy, table]);

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center">
        <Input
          placeholder="Filter tasks..."
          value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("title")?.setFilterValue(event.target.value)
          }
          className="h-8 w-[150px] lg:w-[250px]"
        />
        <Separator
          orientation="vertical"
          className="mx-4 data-[orientation=vertical]:h-4"
        />
        <div className="flex flex-1 items-center space-x-2">
          {table.getColumn("status") && (
            <DataTableFacetedFilter
              column={table.getColumn("status")}
              title="Status"
              options={TASK_STATUS}
            />
          )}
          {table.getColumn("priority") && (
            <DataTableFacetedFilter
              column={table.getColumn("priority")}
              title="Priority"
              options={TASK_PRIORITY}
            />
          )}

          {shouldShowReset && (
            <Button
              variant="ghost"
              onClick={handleReset}
              className="h-8 px-2 lg:px-3"
            >
              Reset
              <IconX className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
