"use client";

import React from "react";
import {
  ColumnDef,
  flexRender,
  Row,
  HeaderGroup,
  type Table,
} from "@tanstack/react-table";
import {
  Table as UITable,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import EmptyContainer from "@/components/empty-container";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { IconSquareRoundedCheck } from "@tabler/icons-react";
import { CreateTaskModal } from "@/components/tasks/create-task-modal";
import {
  startOfToday,
  endOfToday,
  startOfWeek,
  endOfWeek,
  isWithinInterval,
} from "date-fns";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { usePreloadedQuery, useQuery, useMutation } from "convex/react";
import { Task } from "@workspace/backend/convex/lib/types";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  DndContext,
  DragEndEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  DragOverlay,
  DragStartEvent,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { cn } from "@workspace/ui/lib/utils";
import { TaskContextMenu } from "@/app/v/tasks/_components/task-context-menu";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

interface TableProps {
  columns: ColumnDef<Task, unknown>[];
  table: Table<Task>;
  isLoading: boolean;
  groupBy: string;
  showCompletedTasks: boolean;
  onStatusVisibilityChange: (status: Task["status"], visible: boolean) => void;
}

export default function TaskTable({
  columns,
  table,
  isLoading,
  groupBy,
  showCompletedTasks,
  onStatusVisibilityChange,
}: TableProps) {
  return (
    <DataTable
      columns={columns}
      table={table}
      isLoading={isLoading}
      groupBy={groupBy}
      showCompletedTasks={showCompletedTasks}
    />
  );
}

interface DataTableProps {
  columns: ColumnDef<Task, unknown>[];
  table: Table<Task>;
  isLoading: boolean;
  groupBy: string;
  showCompletedTasks: boolean;
}

export function DataTable({
  columns,
  table,
  isLoading,
  groupBy,
  showCompletedTasks,
}: DataTableProps) {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [selectedTask, setSelectedTask] = React.useState<Task | null>(null);
  const [activeId, setActiveId] = React.useState<string | null>(null);
  const [activeTask, setActiveTask] = React.useState<Row<Task> | null>(null);
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);
  const updateTaskOrder = useMutation(api.tasks.updateOrder);

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200,
        tolerance: 5,
      },
    }),
  );

  const usersQuery = useQuery(api.users.searchUsers, {
    searchQuery: "",
    paginationOpts: {
      numItems: 1000,
      cursor: null,
    },
  });

  const users = React.useMemo(() => {
    return usersQuery || [];
  }, [usersQuery]);

  const sortTasks = React.useCallback(
    (rows: Row<Task>[]) => {
      return [...rows].sort((a, b) => {
        if (a.original.status === "done" && b.original.status !== "done")
          return 1;
        if (a.original.status !== "done" && b.original.status === "done")
          return -1;

        const currentSort = table.getState().sorting[0];
        if (!currentSort) return 0;

        const column = table.getColumn(currentSort.id);
        if (!column) return 0;

        const aValue = a.getValue(currentSort.id);
        const bValue = b.getValue(currentSort.id);

        if (typeof aValue === "string" && typeof bValue === "string") {
          return aValue.localeCompare(bValue) * (currentSort.desc ? -1 : 1);
        }

        if (typeof aValue === "number" && typeof bValue === "number") {
          return (aValue - bValue) * (currentSort.desc ? -1 : 1);
        }

        return 0;
      });
    },
    [table],
  );

  const getTaskGroup = React.useCallback(
    (task: Task) => {
      if (task.status === "done") {
        return "Done";
      }

      switch (groupBy) {
        case "status": {
          if (task.status === "in_progress") {
            return "In progress";
          }
          return task.status.charAt(0).toUpperCase() + task.status.slice(1);
        }
        case "dueDate": {
          const dueDate = task.dueDate ? new Date(task.dueDate) : null;
          if (!dueDate) return "No Due Date";

          const today = {
            start: startOfToday(),
            end: endOfToday(),
          };

          const thisWeek = {
            start: startOfWeek(new Date()),
            end: endOfWeek(new Date()),
          };

          if (isWithinInterval(dueDate, today)) {
            return "Today";
          } else if (isWithinInterval(dueDate, thisWeek)) {
            return "This Week";
          } else {
            return "Upcoming";
          }
        }
        case "assignee": {
          const assigneeUser = task.assignee as any;
          return assigneeUser &&
            typeof assigneeUser === "object" &&
            "name" in assigneeUser &&
            assigneeUser.name
            ? assigneeUser.name
            : "Unassigned";
        }
        case "createdAt": {
          const createdAt = new Date(task._creationTime);
          const today = {
            start: startOfToday(),
            end: endOfToday(),
          };

          const thisWeek = {
            start: startOfWeek(new Date()),
            end: endOfWeek(new Date()),
          };

          if (isWithinInterval(createdAt, today)) {
            return "Created Today";
          } else if (isWithinInterval(createdAt, thisWeek)) {
            return "Created This Week";
          } else {
            return "Created Earlier";
          }
        }
        case "createdBy": {
          const usersArr = Array.isArray(users)
            ? users
            : (users as any).users || [];
          const creator = usersArr.find(
            (user: any) => user._id === task.createdBy,
          );
          return creator?.name || "Unknown";
        }
        default:
          return "Ungrouped";
      }
    },
    [groupBy, users],
  );

  const groupTasks = React.useCallback(
    (rows: Row<Task>[], showCompleted: boolean) => {
      const groups: { [key: string]: Row<Task>[] } = {};

      rows.forEach((row) => {
        const group = getTaskGroup(row.original);
        if (!groups[group]) {
          groups[group] = [];
        }

        if (row.original.status === "done" && !showCompleted) {
          return;
        }
        groups[group].push(row);
      });

      return groups;
    },
    [getTaskGroup],
  );

  const handleModalOpenChange = (open: boolean) => {
    setIsModalOpen(open);
    if (!open) {
      setSelectedTask(null);
    }
  };

  const TableSkeleton = ({
    columns,
  }: {
    columns: ColumnDef<Task, unknown>[];
  }) => (
    <>
      {[...Array(5)].map((_, index) => (
        <TableRow key={index}>
          {columns.map((column, cellIndex) => (
            <TableCell key={cellIndex}>
              <Skeleton className="h-6 w-full" />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </>
  );

  const handleRowClick = (row: Row<Task>) => {
    setSelectedTask(row.original);
    setIsModalOpen(true);
  };

  const [isDragging, setIsDragging] = React.useState(false);
  const [activeGroup, setActiveGroup] = React.useState<string | null>(null);

  const handleDragStart = (event: DragStartEvent) => {
    setIsDragging(true);
    setActiveId(event.active.id as string);
    const activeRow = table.getRow(event.active.id as string);
    if (activeRow) {
      setActiveTask(activeRow);
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    setIsDragging(false);
    setActiveId(null);
    setActiveTask(null);
    setActiveGroup(null);
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const activeRow = table.getRow(active.id as string);
    const overRow = table.getRow(over.id as string);

    if (!activeRow || !overRow) {
      return;
    }

    try {
      await updateTaskOrder({
        taskId: activeRow.original._id,
        targetTaskId: overRow.original._id,
        newStatus: overRow.original.status,
      });
    } catch (error) {
      console.error("Error updating task order:", error);
      /**
       * TODO: Add toast notification for error
       */
    }
  };

  const sortedRows = sortTasks(table.getSortedRowModel().rows);
  const taskGroups = groupTasks(sortedRows, showCompletedTasks);
  const defaultGroupOrder = [
    "Today",
    "This Week",
    "Upcoming",
    "No Due Date",
    "Completed",
  ];
  const statusGroupOrder = ["Todo", "In progress", "Done", "Blocked"];

  const groupOrder =
    groupBy === "status" ? statusGroupOrder : defaultGroupOrder;

  const sortedGroups = Object.entries(taskGroups).sort(([a], [b]) => {
    const aIndex = groupOrder.indexOf(a);
    const bIndex = groupOrder.indexOf(b);
    if (aIndex === -1) return 1;
    if (bIndex === -1) return -1;
    return aIndex - bIndex;
  });

  return (
    <div className="space-y-4">
      {user && (
        <CreateTaskModal
          open={isModalOpen}
          onOpenChange={handleModalOpenChange}
          taskToEdit={
            selectedTask
              ? {
                  ...selectedTask,
                  assignee: selectedTask.assignee
                    ? {
                        _id: (selectedTask.assignee as { _id: Id<"users"> })
                          ._id,
                        name:
                          (
                            selectedTask.assignee as {
                              name?: string;
                              email?: string;
                            }
                          ).name ||
                          (selectedTask.assignee as { email?: string }).email ||
                          "",
                      }
                    : undefined,
                  related: selectedTask.related?.[0]
                    ? {
                        _id: (selectedTask.related[0] as { _id: string })._id,
                        name: `${(selectedTask.related[0] as { firstName?: string }).firstName || ""} ${(selectedTask.related[0] as { lastName?: string }).lastName || ""}`.trim(),
                        recordType: "contact",
                      }
                    : undefined,
                }
              : undefined
          }
        />
      )}
      <div className="overflow-auto">
        <DndContext
          sensors={sensors}
          onDragEnd={handleDragEnd}
          onDragStart={handleDragStart}
        >
          <UITable>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup: HeaderGroup<Task>) => (
                <TableRow
                  key={headerGroup.id}
                  className="border-b hover:bg-transparent"
                >
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        colSpan={header.colSpan}
                        className="h-10 px-4 text-xs font-medium text-zinc-500"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableSkeleton columns={columns} />
              ) : (
                <>
                  {sortedGroups.map(
                    ([groupName, rows]) =>
                      rows.length > 0 && (
                        <React.Fragment key={groupName}>
                          <TableRow
                            className={cn(
                              "group",
                              isDragging && activeGroup === groupName && "bg-blue-50/50 dark:bg-blue-900/30 border-t border-blue-500"
                            )}
                            onMouseEnter={() =>
                              isDragging && setActiveGroup(groupName)
                            }
                            onMouseLeave={() =>
                              isDragging && setActiveGroup(null)
                            }
                          >
                            <TableCell
                              colSpan={columns.length}
                              className={cn(
                                "bg-zinc-100 dark:bg-zinc-900 px-4 py-2",
                              )}
                            >
                              <div className="flex items-center gap-x-2">
                                <h2 className="text-xs">{groupName}</h2>
                                <span className="rounded-sm bg-zinc-100 dark:bg-zinc-900 px-1 py-0 text-xs font-medium border border-input font-mono">
                                  {rows.length}
                                </span>
                              </div>
                            </TableCell>
                          </TableRow>
                          <SortableContext
                            items={rows.map((row) => row.id)}
                            strategy={verticalListSortingStrategy}
                          >
                            {rows.map((row: Row<Task>, index) => (
                              <DraggableTableRow
                                key={row.id}
                                row={row}
                                onClick={() => handleRowClick(row)}
                                columns={columns}
                                table={table}
                                groupName={groupName}
                                isGroupActive={
                                  isDragging && activeGroup === groupName
                                }
                                isLastInGroup={index === rows.length - 1}
                                onMouseEnter={() =>
                                  isDragging && setActiveGroup(groupName)
                                }
                                onMouseLeave={() =>
                                  isDragging && setActiveGroup(null)
                                }
                              />
                            ))}
                          </SortableContext>
                        </React.Fragment>
                      ),
                  )}
                  {!table.getFilteredRowModel().rows?.length ||
                    (Object.values(taskGroups).flat().length === 0 && (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-24 text-center rounded-b-lg"
                        >
                          <EmptyContainer
                            title="No tasks found"
                            subtitle="You have no tasks for this workspace."
                            button="Create task"
                            icon={IconSquareRoundedCheck}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                </>
              )}
            </TableBody>
          </UITable>
          <DragOverlay>
            {activeTask && (
              <div className="bg-white dark:bg-zinc-900 shadow-lg rounded-md border border-input w-fit">
                <TableRow
                  className={cn(
                    "cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800/50 border-b data-[state=selected]:dark:bg-zinc-800/50",
                    activeTask.original.status === "done"
                      ? "opacity-50 bg-green-300/40"
                      : "bg-white dark:bg-transparent",
                    "ring-1 ring-blue-500",
                  )}
                >
                  {activeTask.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className={cn(
                        "px-4 py-2",
                        "bg-blue-50 dark:bg-blue-900/20",
                      )}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </div>
            )}
          </DragOverlay>
        </DndContext>
      </div>
    </div>
  );
}

interface DraggableTableRowProps {
  row: Row<Task>;
  onClick: () => void;
  columns: ColumnDef<Task, unknown>[];
  table: Table<Task>;
  groupName: string;
  isGroupActive: boolean;
  isLastInGroup: boolean;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

const DraggableTableRow = ({
  row,
  onClick,
  columns,
  table,
  groupName,
  isGroupActive,
  isLastInGroup,
  onMouseEnter,
  onMouseLeave,
}: DraggableTableRowProps) => {
  const {
    attributes,
    listeners,
    transform,
    transition,
    setNodeRef,
    isDragging,
    over,
  } = useSortable({
    id: row.id,
  });
  const updateTask = useMutation(api.tasks.update);
  const deleteTask = useMutation(api.tasks.deleteTask);
  const favorites = useQuery(api.favorites.get) || [];
  const toggleFavorite = useMutation(api.favorites.toggle);

  const isFavorite = React.useMemo(() => {
    return favorites.some((f) => f?.objectId === row.original._id);
  }, [favorites, row.original._id]);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const overRow = over?.id ? table.getRow(over.id as string) : null;
  const isMovingBetweenStatuses =
    isDragging && overRow && row.original.status !== overRow.original.status;

  const handleStatusChange = async (
    taskId: Id<"tasks">,
    newStatus: Task["status"],
  ) => {
    try {
      await updateTask({
        id: taskId,
        status: newStatus,
        statusHistory: [
          ...(row.original.statusHistory || []),
          {
            status: newStatus,
            timestamp: Date.now(),
            userId: row.original.createdBy,
          },
        ],
      });
    } catch (error) {
      console.error("Error updating task status:", error);
    }
  };

  const handlePriorityChange = async (
    taskId: Id<"tasks">,
    newPriority: Task["priority"],
  ) => {
    try {
      await updateTask({
        id: taskId,
        priority: newPriority,
      });
    } catch (error) {
      console.error("Error updating task priority:", error);
    }
  };

  const handleDelete = async (e: React.MouseEvent, taskId: Id<"tasks">) => {
    try {
      await deleteTask({
        id: taskId,
      });
    } catch (error) {
      console.error("Error deleting task:", error);
    }
  };

  const handleFavorite = async (e: React.MouseEvent, taskId: Id<"tasks">) => {
    try {
      await toggleFavorite({
        objectId: taskId,
      });
    } catch (error) {
      console.error("Error toggling task favorite status:", error);
    }
  };

  return (
    <TaskContextMenu
      task={row.original}
      isFavorite={isFavorite}
      onEdit={(e) => onClick()}
      onFavorite={(e, taskId) => handleFavorite(e, taskId as Id<"tasks">)}
      onStatusChange={(taskId, newStatus) =>
        handleStatusChange(taskId as Id<"tasks">, newStatus)
      }
      onPriorityChange={(taskId, newPriority) =>
        handlePriorityChange(taskId as Id<"tasks">, newPriority)
      }
      onDelete={(e, taskId) => handleDelete(e, taskId as Id<"tasks">)}
    >
      <TableRow
        ref={setNodeRef}
        style={style}
        data-state={row.getIsSelected() && "selected"}
        className={cn(
          "cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800/50 border-b data-[state=selected]:dark:bg-zinc-800/50",
          row.original.status === "done"
            ? "opacity-50 bg-green-300/40"
            : "bg-white dark:bg-transparent",
          isDragging && "opacity-50",
        )}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        {...attributes}
        {...listeners}
      >
        {row.getVisibleCells().map((cell, cellIndex) => (
          <TableCell
            key={cell.id}
            className={cn(
              "px-4 py-2",
              isMovingBetweenStatuses && "bg-blue-50 dark:bg-blue-900/20",
            )}
            onClick={onClick}
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </TableCell>
        ))}
      </TableRow>
    </TaskContextMenu>
  );
};
