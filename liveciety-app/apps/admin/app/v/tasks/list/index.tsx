import React from "react";
import TaskTable from "./_components/data-table";
import { Task } from "@workspace/backend/convex/lib/types";
import { Table } from "@tanstack/react-table";

interface TaskListProps {
  columns: import("@tanstack/react-table").ColumnDef<Task, unknown>[];
  table: Table<Task>;
  isLoading: boolean;
  groupBy: string;
  showCompletedTasks: boolean;
  onStatusVisibilityChange: (status: Task["status"], visible: boolean) => void;
}

const TaskList = ({
  columns,
  table,
  isLoading,
  groupBy,
  showCompletedTasks,
  onStatusVisibilityChange,
}: TaskListProps) => {
  return (
    <TaskTable
      columns={columns}
      table={table}
      isLoading={isLoading}
      groupBy={groupBy}
      showCompletedTasks={showCompletedTasks}
      onStatusVisibilityChange={onStatusVisibilityChange}
    />
  );
};

export default TaskList;
