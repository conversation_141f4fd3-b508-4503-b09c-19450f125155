"use client";

import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Textarea } from "@workspace/ui/components/textarea";
import { formatDistanceToNow } from "date-fns";
import { toast } from "sonner";

export default function FeedbackPage() {
  const feedback = useQuery(api.feedback.getFeedback, {}) || [];
  const updateFeedbackStatus = useMutation(api.feedback.updateFeedbackStatus);
  const [statusUpdates, setStatusUpdates] = useState<Record<string, string>>({});
  const [notes, setNotes] = useState<Record<string, string>>({});

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-100 text-blue-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "done":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleStatusChange = async (id: string, status: string, feedbackNotes?: string) => {
    try {
      await updateFeedbackStatus({
        feedbackId: id as any,
        status: status as "new" | "in_progress" | "done" | "rejected",
        notes: feedbackNotes
      });
      
      toast.success("Feedback status updated successfully");
      
      const newStatusUpdates = { ...statusUpdates };
      delete newStatusUpdates[id];
      setStatusUpdates(newStatusUpdates);
      
      const newNotes = { ...notes };
      delete newNotes[id];
      setNotes(newNotes);
    } catch (error) {
      console.error("Error updating feedback status:", error);
      toast.error("Failed to update feedback status");
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">User Feedback</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>All Feedback</CardTitle>
          <CardDescription>
            Review and manage user feedback and feature requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Message</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {feedback.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    No feedback items available
                  </TableCell>
                </TableRow>
              ) : (
                feedback.map((item: any) => (
                  <TableRow key={item._id}>
                    <TableCell>
                      <Badge variant="outline">
                        {item.type === "bug" ? "Bug Report" : "Feature Request"}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-md">
                      <div className="whitespace-normal break-words">
                        {item.message}
                      </div>
                      {item.url && (
                        <div className="mt-2 text-xs text-gray-500 truncate">
                          <span className="font-medium">URL:</span> {item.url}
                        </div>
                      )}
                      {item.metadata && (
                        <div className="mt-1 text-xs text-gray-500">
                          <span className="font-medium">Browser:</span> {item.metadata.browser?.substring(0, 50)}...
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {formatDistanceToNow(item.createdAt, { addSuffix: true })}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusBadgeColor(item.status)}>
                        {item.status.replace("_", " ")}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-xs">
                      {statusUpdates[item._id] ? (
                        <Textarea
                          placeholder="Add notes..."
                          value={notes[item._id] || item.notes || ""}
                          onChange={(e) => setNotes({ ...notes, [item._id]: e.target.value })}
                          className="h-20 text-sm"
                        />
                      ) : (
                        <div className="whitespace-normal break-words text-sm">
                          {item.notes || "No notes"}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {statusUpdates[item._id] ? (
                        <div className="flex flex-col gap-2">
                          <Select
                            value={statusUpdates[item._id]}
                            onValueChange={(value) => 
                              setStatusUpdates({ ...statusUpdates, [item._id]: value })
                            }
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="new">New</SelectItem>
                              <SelectItem value="in_progress">In Progress</SelectItem>
                              <SelectItem value="done">Done</SelectItem>
                              <SelectItem value="rejected">Rejected</SelectItem>
                            </SelectContent>
                          </Select>
                          <div className="flex gap-2 mt-1">
                            <Button 
                              size="sm" 
                              variant="default"
                              onClick={() => handleStatusChange(
                                item._id, 
                                statusUpdates[item._id] || "", 
                                notes[item._id]
                              )}
                            >
                              Save
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => {
                                const newStatusUpdates = { ...statusUpdates };
                                delete newStatusUpdates[item._id];
                                setStatusUpdates(newStatusUpdates);
                                
                                const newNotes = { ...notes };
                                delete newNotes[item._id];
                                setNotes(newNotes);
                              }}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setStatusUpdates({ 
                            ...statusUpdates, 
                            [item._id]: item.status 
                          })}
                        >
                          Update
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
} 