import React from "react";
import FeedbackTable from "./_components/data-table";
import { Feedback } from "@workspace/backend/convex/lib/types";
import { Table } from "@tanstack/react-table";
import { columns } from "./_components/columns";

interface FeedbackListProps {
  table: Table<Feedback>;
  isLoading: boolean;
  groupBy: string;
  showCompletedFeedback: boolean;
  onStatusVisibilityChange: (status: Feedback["status"], visible: boolean) => void;
}

const FeedbackList = ({
  table,
  isLoading,
  groupBy,
  showCompletedFeedback,
  onStatusVisibilityChange,
}: FeedbackListProps) => {
  return (
    <FeedbackTable
      columns={columns}
      table={table}
      isLoading={isLoading}
      groupBy={groupBy}
      showCompletedFeedback={showCompletedFeedback}
      onStatusVisibilityChange={onStatusVisibilityChange}
    />
  );
};

export default FeedbackList;
