"use client";

import React from "react";
import {
  ColumnDef,
  flexRender,
  Row,
  HeaderGroup,
  type Table,
} from "@tanstack/react-table";
import {
  Table as UITable,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import EmptyContainer from "@/components/empty-container";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { IconMessageCircle2 } from "@tabler/icons-react";
import {
  startOfToday,
  endOfToday,
  startOfWeek,
  endOfWeek,
  isWithinInterval,
} from "date-fns";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { usePreloadedQuery, useQuery, useMutation } from "convex/react";
import { Feedback } from "@workspace/backend/convex/lib/types";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  DndContext,
  DragEndEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  DragOverlay,
  DragStartEvent,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { cn } from "@workspace/ui/lib/utils";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@workspace/ui/components/dropdown-menu";
import { FeedbackSheet } from "./feedback-sheet";

const FeedbackContextMenu = ({ 
  feedback, 
  children, 
  onEdit, 
  onStatusChange, 
  onTypeChange, 
  onDelete 
}: { 
  feedback: Feedback;
  children: React.ReactNode;
  onEdit: (e: React.MouseEvent) => void;
  onStatusChange: (feedbackId: Id<"feedback">, newStatus: Feedback["status"]) => void;
  onTypeChange: (feedbackId: Id<"feedback">, newType: Feedback["type"]) => void;
  onDelete: (e: React.MouseEvent, feedbackId: Id<"feedback">) => void;
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {children}
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={onEdit}>
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onStatusChange(feedback._id, "new")}>
          Mark as New
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onStatusChange(feedback._id, "in_progress")}>
          Mark as In Progress
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onStatusChange(feedback._id, "done")}>
          Mark as Completed
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onStatusChange(feedback._id, "rejected")}>
          Mark as Rejected
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onTypeChange(feedback._id, "bug")}>
          Set as Bug
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onTypeChange(feedback._id, "feature_request")}>
          Set as Feature Request
        </DropdownMenuItem>
        <DropdownMenuItem onClick={(e) => onDelete(e, feedback._id)}>
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

interface TableProps {
  columns: ColumnDef<Feedback, unknown>[];
  table: Table<Feedback>;
  isLoading: boolean;
  groupBy: string;
  showCompletedFeedback: boolean;
  onStatusVisibilityChange: (status: Feedback["status"], visible: boolean) => void;
}

export default function FeedbackTable({
  columns,
  table,
  isLoading,
  groupBy,
  showCompletedFeedback,
  onStatusVisibilityChange,
}: TableProps) {
  return (
    <DataTable
      columns={columns}
      table={table}
      isLoading={isLoading}
      groupBy={groupBy}
      showCompletedFeedback={showCompletedFeedback}
    />
  );
}

interface DataTableProps {
  columns: ColumnDef<Feedback, unknown>[];
  table: Table<Feedback>;
  isLoading: boolean;
  groupBy: string;
  showCompletedFeedback: boolean;
}

export function DataTable({
  columns,
  table,
  isLoading,
  groupBy,
  showCompletedFeedback,
}: DataTableProps) {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [isSheetOpen, setIsSheetOpen] = React.useState(false);
  const [selectedFeedback, setSelectedFeedback] = React.useState<Feedback | null>(null);
  const [activeId, setActiveId] = React.useState<string | null>(null);
  const [activeFeedback, setActiveFeedback] = React.useState<Row<Feedback> | null>(null);
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);
  
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200,
        tolerance: 5,
      },
    }),
  );

  const usersQuery = useQuery(api.users.searchUsers, {
    searchQuery: "",
    paginationOpts: {
      numItems: 1000,
      cursor: null,
    },
  });

  const users = React.useMemo(() => {
    return usersQuery || [];
  }, [usersQuery]);

  const sortFeedbacks = React.useCallback(
    (rows: Row<Feedback>[]) => {
      return [...rows].sort((a, b) => {
        if (a.original.status === "done" && b.original.status !== "done")
          return 1;
        if (a.original.status !== "done" && b.original.status === "done")
          return -1;

        const currentSort = table.getState().sorting[0];
        if (!currentSort) return 0;

        const column = table.getColumn(currentSort.id);
        if (!column) return 0;

        const aValue = a.getValue(currentSort.id);
        const bValue = b.getValue(currentSort.id);

        if (typeof aValue === "string" && typeof bValue === "string") {
          return aValue.localeCompare(bValue) * (currentSort.desc ? -1 : 1);
        }

        if (typeof aValue === "number" && typeof bValue === "number") {
          return (aValue - bValue) * (currentSort.desc ? -1 : 1);
        }

        return 0;
      });
    },
    [table],
  );

  const getFeedbackGroup = React.useCallback(
    (feedback: Feedback) => {
      if (feedback.status === "done" || feedback.status === "rejected") {
        return feedback.status.charAt(0).toUpperCase() + feedback.status.slice(1);
      }

      switch (groupBy) {
        case "status": {
          if (feedback.status === "in_progress") {
            return "In progress";
          }
          return feedback.status.charAt(0).toUpperCase() + feedback.status.slice(1);
        }
        case "type": {
          return feedback.type === "bug" ? "Bug" : "Feature Request";
        }
        case "createdAt": {
          const createdAt = new Date(feedback.createdAt);
          const today = {
            start: startOfToday(),
            end: endOfToday(),
          };

          const thisWeek = {
            start: startOfWeek(new Date()),
            end: endOfWeek(new Date()),
          };

          if (isWithinInterval(createdAt, today)) {
            return "Created Today";
          } else if (isWithinInterval(createdAt, thisWeek)) {
            return "Created This Week";
          } else {
            return "Created Earlier";
          }
        }
        case "createdBy": {
          const usersArr = Array.isArray(users)
            ? users
            : (users as any).users || [];
          const creator = usersArr.find(
            (user: any) => user._id === (feedback.user as any)._id,
          );
          return creator?.name || "Unknown";
        }
        default:
          return "Ungrouped";
      }
    },
    [groupBy, users],
  );

  const groupFeedbacks = React.useCallback(
    (rows: Row<Feedback>[], showCompleted: boolean) => {
      const groups: { [key: string]: Row<Feedback>[] } = {};

      rows.forEach((row) => {
        const group = getFeedbackGroup(row.original);
        if (!groups[group]) {
          groups[group] = [];
        }
        if ((row.original.status === "done" || row.original.status === "rejected") && !showCompleted) {
          return;
        }
        groups[group].push(row);
      });

      return groups;
    },
    [getFeedbackGroup],
  );

  const TableSkeleton = ({
    columns,
  }: {
    columns: ColumnDef<Feedback, unknown>[];
  }) => (
    <>
      {[...Array(5)].map((_, index) => (
        <TableRow key={index}>
          {columns.map((column, cellIndex) => (
            <TableCell key={cellIndex}>
              <Skeleton className="h-6 w-full" />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </>
  );

  const handleRowClick = (row: Row<Feedback>) => {
    setSelectedFeedback(row.original);
    setIsSheetOpen(true);
  };

  const [isDragging, setIsDragging] = React.useState(false);
  const [activeGroup, setActiveGroup] = React.useState<string | null>(null);

  const handleDragStart = (event: DragStartEvent) => {
    setIsDragging(true);
    setActiveId(event.active.id as string);
    const activeRow = table.getRow(event.active.id as string);
    if (activeRow) {
      setActiveFeedback(activeRow);
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    setIsDragging(false);
    setActiveId(null);
    setActiveFeedback(null);
    setActiveGroup(null);
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const activeRow = table.getRow(active.id as string);
    const overRow = table.getRow(over.id as string);

    if (!activeRow || !overRow) {
      return;
    }
  };

  const sortedRows = sortFeedbacks(table.getSortedRowModel().rows);
  const feedbackGroups = groupFeedbacks(sortedRows, showCompletedFeedback);
  
  const defaultGroupOrder = [
    "New",
    "In progress", 
    "Done",
    "Rejected"
  ];
  
  const typeGroupOrder = [
    "Bug",
    "Feature Request"
  ];
  
  const createdAtGroupOrder = [
    "Created Today",
    "Created This Week",
    "Created Earlier"
  ];

  let groupOrder: string[] = defaultGroupOrder;
  
  if (groupBy === "type") {
    groupOrder = typeGroupOrder;
  } else if (groupBy === "createdAt") {
    groupOrder = createdAtGroupOrder;
  }

  const sortedGroups = Object.entries(feedbackGroups).sort(([a], [b]) => {
    const aIndex = groupOrder.indexOf(a);
    const bIndex = groupOrder.indexOf(b);
    if (aIndex === -1) return 1;
    if (bIndex === -1) return -1;
    return aIndex - bIndex;
  });

  return (
    <div className="space-y-4">
      <div className="overflow-auto">
        <DndContext
          sensors={sensors}
          onDragEnd={handleDragEnd}
          onDragStart={handleDragStart}
        >
          <UITable>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup: HeaderGroup<Feedback>) => (
                <TableRow
                  key={headerGroup.id}
                  className="border-b hover:bg-transparent"
                >
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        colSpan={header.colSpan}
                        className="h-10 px-4 text-xs font-medium text-zinc-500"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableSkeleton columns={columns} />
              ) : (
                <>
                  {sortedGroups.map(
                    ([groupName, rows]) =>
                      rows.length > 0 && (
                        <React.Fragment key={groupName}>
                          <TableRow
                            className={cn(
                              "group",
                            )}
                            onMouseEnter={() =>
                              isDragging && setActiveGroup(groupName)
                            }
                            onMouseLeave={() =>
                              isDragging && setActiveGroup(null)
                            }
                          >
                            <TableCell
                              colSpan={columns.length}
                              className={cn(
                                "bg-zinc-100 dark:bg-zinc-900 px-4 py-2",
                              )}
                            >
                              <div className="flex items-center gap-x-2">
                                <h2 className="text-xs">{groupName}</h2>
                                <span className="rounded-sm bg-zinc-100 dark:bg-zinc-900 px-1 py-0 text-xs font-medium border border-input font-mono">
                                  {rows.length}
                                </span>
                              </div>
                            </TableCell>
                          </TableRow>
                          <SortableContext
                            items={rows.map((row) => row.id)}
                            strategy={verticalListSortingStrategy}
                          >
                            {rows.map((row: Row<Feedback>, index) => (
                              <DraggableFeedbackRow
                                key={row.id}
                                row={row}
                                onClick={() => handleRowClick(row)}
                                columns={columns}
                                table={table}
                                groupName={groupName}
                                isGroupActive={
                                  isDragging && activeGroup === groupName
                                }
                                isLastInGroup={index === rows.length - 1}
                                onMouseEnter={() =>
                                  isDragging && setActiveGroup(groupName)
                                }
                                onMouseLeave={() =>
                                  isDragging && setActiveGroup(null)
                                }
                              />
                            ))}
                          </SortableContext>
                        </React.Fragment>
                      ),
                  )}
                  {!table.getFilteredRowModel().rows?.length ||
                    (Object.values(feedbackGroups).flat().length === 0 && (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-24 text-center rounded-b-lg"
                        >
                          <EmptyContainer
                            title="No feedback found"
                            subtitle="There is no feedback in the system yet."
                            icon={IconMessageCircle2}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                </>
              )}
            </TableBody>
          </UITable>
          <DragOverlay>
            {activeFeedback && (
              <div className="bg-white dark:bg-zinc-900 shadow-lg rounded-md border border-input w-fit">
                <TableRow
                  className={cn(
                    "cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800/50 border-b data-[state=selected]:dark:bg-zinc-800/50",
                    activeFeedback.original.status === "done"
                      ? "opacity-50 bg-green-300/40"
                      : "bg-white dark:bg-transparent",
                    "ring-1 ring-blue-500",
                  )}
                >
                  {activeFeedback.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className={cn(
                        "px-4 py-2",
                        "bg-blue-50 dark:bg-blue-900/20",
                      )}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </div>
            )}
          </DragOverlay>
        </DndContext>
      </div>
      <FeedbackSheet
        open={isSheetOpen && !!selectedFeedback}
        onOpenChange={(open) => {
          setIsSheetOpen(open);
          if (!open) setSelectedFeedback(null);
        }}
        feedback={selectedFeedback}
      />
    </div>
  );
}

interface DraggableFeedbackRowProps {
  row: Row<Feedback>;
  onClick: () => void;
  columns: ColumnDef<Feedback, unknown>[];
  table: Table<Feedback>;
  groupName: string;
  isGroupActive: boolean;
  isLastInGroup: boolean;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

const DraggableFeedbackRow = ({
  row,
  onClick,
  columns,
  table,
  groupName,
  isGroupActive,
  isLastInGroup,
  onMouseEnter,
  onMouseLeave,
}: DraggableFeedbackRowProps) => {
  const {
    attributes,
    listeners,
    transform,
    transition,
    setNodeRef,
    isDragging,
    over,
  } = useSortable({
    id: row.id,
  });
  const updateFeedbackStatus = useMutation(api.feedback.updateFeedbackStatus);
  const deleteFeedback = useMutation(api.feedback.deleteFeedback);
  const updateBatchFeedbacks = useMutation(api.feedback.updateBatchFeedbacks);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const overRow = over?.id ? table.getRow(over.id as string) : null;
  const isMovingBetweenStatuses =
    isDragging && overRow && row.original.status !== overRow.original.status;

  const handleStatusChange = async (
    feedbackId: Id<"feedback">,
    newStatus: Feedback["status"],
  ) => {
    try {
      await updateFeedbackStatus({
        feedbackId,
        status: newStatus,
      });
    } catch (error) {
      console.error("Error updating feedback status:", error);
    }
  };

  const handleTypeChange = async (
    feedbackId: Id<"feedback">,
    newType: Feedback["type"],
  ) => {
    try {
      await updateBatchFeedbacks({
        feedbackIds: [feedbackId],
        type: newType,
      });
    } catch (error) {
      console.error("Error updating feedback type:", error);
    }
  };

  const handleDelete = async (e: React.MouseEvent, feedbackId: Id<"feedback">) => {
    try {
      await deleteFeedback({
        feedbackId,
      });
    } catch (error) {
      console.error("Error deleting feedback:", error);
    }
  };

  return (
    <TableRow
      ref={setNodeRef}
      style={style}
      data-state={row.getIsSelected() && "selected"}
      className={cn(
        "cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800/50 border-b data-[state=selected]:dark:bg-zinc-800/50",
        (row.original.status === "done" || row.original.status === "rejected")
          ? "opacity-50 bg-green-300/40"
          : "bg-white dark:bg-transparent",
        isDragging && "opacity-50",
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      {...attributes}
      {...listeners}
    >
      {row.getVisibleCells().map((cell) => (
        <TableCell
          key={cell.id}
          className={cn(
            "px-4 py-2",
            isMovingBetweenStatuses && "bg-blue-50 dark:bg-blue-900/20",
          )}
          onClick={onClick}
        >
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
};
