"use client";

import React, { useEffect, useState } from "react";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Button } from "@workspace/ui/components/button";
import {
  IconTableOptions,
  IconLayoutList,
  IconLayoutColumns,
  IconQuestionMark,
  IconCalendar,
} from "@tabler/icons-react";
import { STORAGE_KEYS, FEEDBACK_STATUS } from "@/lib/constants";
import type { FeedbackStatus } from "@/lib/constants";
import { Table } from "@tanstack/react-table";
import { Feedback, User } from "@workspace/backend/convex/lib/types";
import { updateFeedbackPreferences } from "@/lib/feedback-preferences";
import { setLocalStorage } from "@/lib/local-storage";
import { DataTableSort } from "../../_components/data-table-sort";
import { DataTableGroup } from "../../_components/data-table-group";
import { Permissions } from "@/lib/permissions";
import { Switch } from "@workspace/ui/components/switch";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { UserAvatar } from "@workspace/ui/components/user-avatar";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

interface TableSettingsProps {
  showCompletedFeedbacks: boolean;
  onShowCompletedFeedbacksChange: (show: boolean) => void;
  statusVisibility: Record<FeedbackStatus, boolean>;
  onStatusVisibilityChange: (status: FeedbackStatus, visible: boolean) => void;
  table: Table<Feedback>;
  view: string;
  onViewChange: (view: string) => void;
  groupBy: string;
  onGroupByChange: (groupBy: string) => void;
  user: User;
}

export function TableSettings({
  showCompletedFeedbacks,
  onShowCompletedFeedbacksChange,
  statusVisibility,
  onStatusVisibilityChange,
  table,
  view,
  onViewChange,
  groupBy,
  onGroupByChange,
  user,
}: TableSettingsProps) {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    updateFeedbackPreferences({ view });
  }, [view]);

  useEffect(() => {
    setLocalStorage(STORAGE_KEYS.FEEDBACKS_VIEW ?? "", view);
  }, [view]);

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="h-7 rounded-md gap-1">
          <IconTableOptions className="h-4 w-4" />
          <span>View Settings</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[280px] p-4">
        <div className="space-y-4">
          {/* View Section */}
          <div>
            <div className="flex items-center justify-between mb-1">
              <span className="font-medium text-xs text-muted-foreground">
                View
              </span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <IconQuestionMark className="h-4 w-4 text-muted-foreground border rounded-full p-0.5" />
                </TooltipTrigger>
                <TooltipContent>
                  <span>Viewing options are applied just for you.</span>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className="grid grid-cols-3 gap-1 border border-input rounded-lg p-1">
              <Button
                variant={view === "list" ? "outline" : "default"}
                className="w-full h-auto py-2 px-4 flex flex-col items-center gap-1"
                onClick={() => {
                  onViewChange("list");
                  setIsOpen(false);
                }}
              >
                <IconLayoutList className="h-5 w-5" />
                <span className="text-xs">List</span>
              </Button>
              <Button
                variant={view === "kanban" ? "outline" : "default"}
                className="w-full h-auto py-2 px-4 flex flex-col items-center gap-1"
                onClick={() => {
                  onViewChange("kanban");
                  setIsOpen(false);
                }}
              >
                <IconLayoutColumns className="h-5 w-5" />
                <span className="text-xs">Board</span>
              </Button>
              <Button
                variant={view === "calendar" ? "outline" : "default"}
                className="w-full h-auto py-2 px-4 flex flex-col items-center gap-1 relative"
                onClick={() => {
                  onViewChange("calendar");
                  setIsOpen(false);
                }}
              >
                <IconCalendar className="h-5 w-5" />
                <span className="text-xs">Calendar</span>
                <span className="absolute top-0 right-0 bg-amber-500 text-white text-[10px] px-1 rounded">
                  Beta
                </span>
              </Button>
            </div>
          </div>

          {/* Completed Feedbacks Section */}
          <div className="flex items-center justify-between">
            <span className="text-sm">Show completed</span>
            <Switch
              checked={showCompletedFeedbacks}
              onCheckedChange={onShowCompletedFeedbacksChange}
            />
          </div>

          {/* Group Section */}
          <div>
            <div className="flex items-center justify-between mb-1">
              <span className="font-medium text-xs text-muted-foreground">
                Grouped by
              </span>
            </div>
            <div className="space-y-2">
              <div className="flex flex-col gap-1">
                <DataTableGroup
                  groupBy={groupBy}
                  onGroupByChange={onGroupByChange}
                  user={user}
                />
              </div>
            </div>
          </div>

          {/* Sort Section */}
          <div>
            <div className="flex items-center justify-between mb-1">
              <span className="font-medium text-xs text-muted-foreground">
                Sorted by
              </span>
            </div>
            <div className="space-y-2">
              <div className="flex flex-col gap-1">
                <DataTableSort table={table} />
              </div>
            </div>
          </div>

          {/* Visibility Section */}
          <div>
            <div>
              <span className="font-medium text-xs text-muted-foreground">
                Visibility
              </span>
            </div>

            {/* Columns Section */}
            <div className="flex flex-col gap-1">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="h-8 gap-2 rounded-lg flex items-center justify-start w-full"
                  >
                    Table Columns{" "}
                    <span className="ml-auto text-xs text-muted-foreground bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
                      {
                        table
                          .getAllColumns()
                          .filter((column) => column.getIsVisible()).length
                      }
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-70">
                  {table
                    .getAllColumns()
                    .filter(
                      (column) =>
                        typeof column.accessorFn !== "undefined" &&
                        column.getCanHide(),
                    )
                    .map((column) => {
                      return (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          className="capitalize"
                          checked={column.getIsVisible()}
                          onCheckedChange={(value) =>
                            column.toggleVisibility(!!value)
                          }
                        >
                          {column.id}
                        </DropdownMenuCheckboxItem>
                      );
                    })}
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="h-8 gap-2 rounded-lg flex items-center justify-start w-full"
                  >
                    Status Visibility
                    <span className="ml-auto text-xs text-muted-foreground bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
                      {
                        Object.keys(statusVisibility).filter(
                          (status) =>
                            statusVisibility[status as FeedbackStatus] !== false,
                        ).length
                      }
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-70">
                  {FEEDBACK_STATUS.map((status) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={status.value}
                        className="capitalize"
                        checked={statusVisibility[status.value] !== false}
                        onCheckedChange={(value) =>
                          onStatusVisibilityChange(status.value, value)
                        }
                      >
                        {status.label}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Filter Section */}
          {Permissions.canFilterTasks(user) && (
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="font-medium text-xs text-muted-foreground">
                  Filter by
                </span>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  {/* <Button variant="outline" className="h-8 gap-2 rounded-lg flex items-center justify-start w-full">
                    Assignee <span className="ml-auto text-xs text-muted-foreground bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
                      {Object.keys(users).length}
                    </span>
                  </Button> */}
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-70">
                  {/* {Object.values(users).map((user) => {
                    const typedUser = user as Users;
                    return (
                      <DropdownMenuCheckboxItem
                        key={typedUser._id}
                        className="capitalize"
                        checked={(table.getColumn('assignee')?.getFilterValue() as string[] | undefined)?.includes(typedUser._id)}
                        onCheckedChange={(checked) => {
                          const filterValues = table.getColumn('assignee')?.getFilterValue() as string[] || [];
                          if (checked) {
                            table.getColumn('assignee')?.setFilterValue([...filterValues, typedUser._id]);
                          } else {
                            table.getColumn('assignee')?.setFilterValue(
                              filterValues.filter((id) => id !== typedUser._id)
                            );
                          }
                        }}
                      >
                        <div className="flex items-center gap-2">
                          <UserAvatar user={typedUser} size="sm" />
                          {typedUser.name}
                        </div>
                      </DropdownMenuCheckboxItem>
                    );
                  })} */}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
