import React from "react";
import { Feedback } from "@workspace/backend/convex/lib/types";
import { ContextMenu, ContextMenuItemType } from "@/components/context-menu";
import { FEEDBACK_STATUS, FEEDBACK_TYPE } from "@/lib/constants";
import {
  IconAntennaBars5,
  IconEdit,
  IconProgress,
  IconStar,
  IconStarFilled,
  IconTrash,
} from "@tabler/icons-react";

interface FeedbackContextMenuProps {
  children: React.ReactNode;
  feedback: Feedback;
  isFavorite: boolean;
  onEdit: (e: React.MouseEvent, feedbackId: string) => void;
  onFavorite: (e: React.MouseEvent, feedbackId: string) => void;
  onStatusChange?: (feedbackId: string, newStatus: Feedback["status"]) => void;
  onTypeChange?: (feedbackId: string, newType: Feedback["type"]) => void;
  onDelete: (e: React.MouseEvent, feedbackId: string) => void;
}

export function FeedbackContextMenu({
  children,
  feedback,
  isFavorite,
  onEdit,
  onFavorite,
  onStatusChange,
  onTypeChange,
  onDelete,
}: FeedbackContextMenuProps) {
  const menuItems: (ContextMenuItemType | "separator")[] = [
    {
      id: "edit",
      label: "Edit",
      icon: IconEdit,
      onClick: (e) => onEdit(e, feedback._id),
    },
    {
      id: "favorite",
      label: isFavorite ? "Remove from favorites" : "Add to favorites",
      icon: isFavorite ? IconStarFilled : IconStar,
      iconClassName: isFavorite ? "text-yellow-400" : "",
      onClick: (e) => onFavorite(e, feedback._id),
    },
    "separator",
    {
      id: "status",
      label: "Status",
      icon: IconProgress,
      indicator: {
        show: true,
      },
      items: FEEDBACK_STATUS.map((status) => ({
        id: `status-${status.value}`,
        label: status.label,
        onClick: () => onStatusChange?.(feedback._id, status.value as Feedback["status"]),
        indicator: {
          show: feedback.status === status.value,
        },
      })),
    },
    {
      id: "type",
      label: "Type",
      icon: IconAntennaBars5,
      items: FEEDBACK_TYPE.map((type) => ({
        id: `type-${type.value}`,
        label: type.label,
        onClick: () => onTypeChange?.(feedback._id, type.value as Feedback["type"]),
        indicator: {
          show: feedback.type === type.value,
        },
      })),
    },
    "separator",
    {
      id: "delete",
      label: "Delete feedback",
      icon: IconTrash,
      iconClassName: "text-red-400",
      onClick: (e) => onDelete(e, feedback._id),
    },
  ];

  return <ContextMenu items={menuItems}>{children}</ContextMenu>;
}
