"use client";

import { use<PERSON><PERSON><PERSON>, useState, useEffect, useMemo } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  DragOverEvent,
  MeasuringStrategy,
} from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { Feedback, FeedbackStatus, FeedbackType } from "@workspace/backend/convex/lib/types";
import { FEEDBACK_STATUS } from "@/lib/constants";
import { useQuery, useMutation, usePreloadedQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { KanbanColumn } from "./_components/column";
import { KanbanCard } from "./_components/card";
import { useParams } from "next/navigation";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import BottomBarFeedback from "@/components/feedback/feedback-bottom-bar";
import { CreateFeedbackModal } from "@/components/feedback/create-feedback-modal";
import { Table } from "@tanstack/react-table";
import confetti from "canvas-confetti";
import { usePreloadedData } from "@/hooks/use-preloaded-data";

interface FeedbackToEdit {
  _id: Id<"feedback">;
  message: string;
  notes?: string;
  status: FeedbackStatus;
  type: FeedbackType;
  position?: number;
  metadata?: {
    browser?: string;
    device?: string;
    os?: string;
  };
  createdAt?: number;
  updatedAt?: number;
  userId?: Id<"users">;
  updatedBy?: Id<"users">;
  statusHistory?: Array<{
    status: string;
    timestamp: number;
    userId: Id<"users">;
  }>;
  url?: string;
  _creationTime?: number;
}

interface FeedbackBoardProps {
  table: Table<Feedback>;
  statusVisibility: Record<FeedbackStatus, boolean>;
  onStatusVisibilityChange: (status: FeedbackStatus, visible: boolean) => void;
}

export default function FeedbackBoard({
  table,
  statusVisibility,
  onStatusVisibilityChange,
}: FeedbackBoardProps) {
  const params = useParams();
  const feedbacks = useQuery(api.feedback.getFeedback, { favorites: false }) || [];
  const [activeFeedback, setActiveFeedback] = useState<Feedback | null>(null);
  const [selectedFeedbacks, setSelectedFeedbacks] = useState<Set<string>>(new Set());
  const [selectedFeedbackObjects, setSelectedFeedbackObjects] = useState<Feedback[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [defaultStatus, setDefaultStatus] = useState<Feedback["status"] | null>(
    null,
  );
  const [feedbackToEdit, setFeedbackToEdit] = useState<FeedbackToEdit | null>(null);
  const updateFeedback = useMutation(api.feedback.updateFeedbackStatus);
  const updateBatchFeedbacks = useMutation(api.feedback.updateBatchFeedbacks);
  const deleteFeedback = useMutation(api.feedback.deleteFeedback);

  const processedFeedbacks = useMemo(() => {
    const columnFilters = table.getState().columnFilters;
    const sorting = table.getState().sorting[0];

    let result = feedbacks.filter((feedback: any) => {
      if (!statusVisibility[feedback?.status as FeedbackStatus]) {
        return false;
      }

      return columnFilters.every((filter) => {
        const value = filter.value;

        if (filter.id === "message" && typeof value === "string") {
          return (feedback?.message || "").toLowerCase().includes(value.toLowerCase());
        }

        if (filter.id === "status" && Array.isArray(value)) {
          return value.length === 0 || value.includes(feedback.status);
        }

        if (filter.id === "type" && Array.isArray(value)) {
          return value.length === 0 || value.includes(feedback.type);
        }

        if (filter.id === "notes" && typeof value === "string") {
          return (feedback.notes || "").toLowerCase().includes(value.toLowerCase());
        }

        return true;
      });
    });

    if (sorting) {
      result = [...result].sort((a: any, b: any) => {
        let comparison = 0;

        switch (sorting.id) {
          case "message":
            comparison = ((a?.message || "") as string).localeCompare((b?.message || "") as string);
            break;
          case "type":
            comparison = ((a?.type || "") as string).localeCompare((b?.type || "") as string);
            break;
          case "status":
            comparison = ((a?.status || "") as string).localeCompare((b?.status || "") as string);
            break;
          case "notes":
            comparison = ((a?.notes || "") as string).localeCompare((b?.notes || "") as string);
            break;
          case "_creationTime":
            comparison = (a?._creationTime || 0) - (b?._creationTime || 0);
            break;
          default:
            comparison = 0;
        }

        return sorting.desc ? -comparison : comparison;
      });
    }

    result = result.sort((a: any, b: any) => {
      if (a.status === b.status) {
        return (a.position ?? 0) - (b.position ?? 0);
      }
      return 0;
    });

    return result;
  }, [
    feedbacks,
    table.getState().columnFilters,
    table.getState().sorting,
    statusVisibility,
  ]);

  const columnPreferences = useQuery(api.columnPreferences.getAll) || [];

  const columnPrefsMap = useMemo(() => {
    const prefsMap: Record<string, any> = {
      backlog: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
      todo: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
      in_progress: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
      review: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
      done: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
    };

    columnPreferences.forEach((pref) => {
      if (pref.column in prefsMap) {
        prefsMap[pref.column] = pref;
      }
    });

    return prefsMap;
  }, [columnPreferences]);

  const handleSelectFeedback = useCallback((feedbackId: string) => {
    setSelectedFeedbacks((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(feedbackId)) {
        newSet.delete(feedbackId);
      } else {
        newSet.add(feedbackId);
      }
      return newSet;
    });
  }, []);

  const handleEditFeedback = useCallback(
    (feedbackId: string) => {
      if (!feedbacks || feedbacks.length === 0) {
        console.warn("Feedbacks not loaded yet");
        return;
      }

      const feedback = feedbacks.find((f) => f?._id === feedbackId) as unknown as Feedback;
      if (!feedback) {
        console.warn("Feedback not found:", feedbackId);
        return;
      }

      try {
        setFeedbackToEdit(feedback as any);
        setIsCreateModalOpen(true);
      } catch (error) {
        console.error("Error formatting feedback for edit:", error);
      }
    },
    [feedbacks],
  );

  const handleCreateFeedback = useCallback((status: Feedback["status"]) => {
    setFeedbackToEdit(null);
    setDefaultStatus(status);
    setIsCreateModalOpen(true);
  }, []);

  const handleModalClose = useCallback((open: boolean) => {
    setIsCreateModalOpen(open);
    if (!open) {
      setFeedbackToEdit(null);
      setDefaultStatus(null);
    }
  }, []);

  useEffect(() => {
    const selectedFeedbacksList = Array.from(selectedFeedbacks)
      .map((feedbackId) => feedbacks.find((feedback) => feedback?._id === feedbackId))
      .filter((feedback): feedback is NonNullable<typeof feedback> => feedback !== undefined);

    setSelectedFeedbackObjects(selectedFeedbacksList as Feedback[]);
  }, [selectedFeedbacks, feedbacks]);

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 3,
    },
  });
  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 100,
      tolerance: 5,
    },
  });
  const sensors = useSensors(mouseSensor, touchSensor);

  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;

      const feedback = feedbacks.find((t) => t?._id === active.id);
      if (feedback) setActiveFeedback(feedback as Feedback);
    },
    [feedbacks],
  );

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event;
  }, []);

  const handleDragEnd = useCallback(
    async (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over) {
        return;
      }

      const activeFeedback: any = feedbacks.find((f) => f?._id === active.id);
      if (!activeFeedback) {
        return;
      }

      const feedbacksToMove = selectedFeedbacks.has(String(activeFeedback._id))
        ? feedbacks.filter((feedback) => selectedFeedbacks.has(String(feedback?._id)))
        : [activeFeedback];

      if (
        !over.id.toString().startsWith("column-") &&
        over.data.current?.type === "feedback"
      ) {
        const overFeedback: any = feedbacks.find((f) => f?._id === over.id);
        if (!overFeedback) return;

        if (activeFeedback.status !== overFeedback.status) {
          const positionedFeedbacks = feedbacks
            .filter((f) => f?.status === overFeedback.status)
            .sort((a: any, b: any) => ((a.position ?? 0) - (b.position ?? 0)));

          const newPosition = overFeedback?.position ?? 0;

          await Promise.all(
            feedbacksToMove.map((feedback: any) =>
              updateFeedback({
                feedbackId: feedback._id,
                status: overFeedback.status as FeedbackStatus,
                notes: feedback.notes
              })
            )
          );

          return;
        }

        const columnFeedbacks = feedbacks
          .filter((f) => f?.status === activeFeedback.status)
          .sort((a: any, b: any) => ((a.position ?? 0) - (b.position ?? 0)));

        const activeIndex = columnFeedbacks.findIndex(
          (f) => f?._id === activeFeedback._id
        );
        const overIndex = columnFeedbacks.findIndex((f) => f?._id === overFeedback._id);

        if (activeIndex === -1 || overIndex === -1) {
          return;
        }

        const newOrder = arrayMove(columnFeedbacks, activeIndex, overIndex);

        try {
          await Promise.all(
            newOrder.map((feedback: any) =>
              updateFeedback({
                feedbackId: feedback._id,
                status: feedback.status as FeedbackStatus,
                notes: feedback.notes
              })
            )
          );
          return;
        } catch (error) {
          console.error("[Board Debug] Error updating feedback positions:", error);
          return;
        }
      }

      let newStatus: FeedbackStatus | undefined;

      if (over.id.toString().startsWith("column-")) {
        newStatus = over.id.toString().replace("column-", "") as FeedbackStatus;
      } else {
        const overFeedback = feedbacks.find((f) => f?._id === over.id);
        if (overFeedback) {
          newStatus = overFeedback.status as FeedbackStatus;
        }
      }

      if (!newStatus) {
        return;
      }

      try {
        await Promise.all(
          feedbacksToMove.map((feedback) => {
            if (feedback.status !== newStatus) {
              return updateFeedback({
                feedbackId: feedback._id,
                status: newStatus,
                notes: feedback.notes
              });
            }
            return Promise.resolve();
          })
        );

        const columnPrefs = columnPrefsMap[newStatus];

        if (columnPrefs?.showConfetti) {
          const colors = ["#EE342F", "#F3B01C", "#8D2676"];
          const defaultOpts = {
            particleCount: 50,
            colors: colors,
            spread: 90,
            startVelocity: 45,
            decay: 0.92,
            scalar: 1,
          };

          const fireCorner = (x: number, y: number, opts: any = {}) => {
            confetti({
              ...defaultOpts,
              ...opts,
              origin: { x, y },
            });
          };

          fireCorner(0, 0);
          fireCorner(0.2, 0);

          fireCorner(0.8, 0);
          fireCorner(1, 0);

          fireCorner(0, 1);
          fireCorner(0.2, 1);

          fireCorner(0.8, 1);
          fireCorner(1, 1);

          confetti({
            ...defaultOpts,
            origin: { x: 0.5, y: 0.5 },
            particleCount: 100,
            spread: 360,
          });
        }
      } catch (error) {
        console.error("[Board Debug] Error updating feedback:", error);
      }

      setActiveFeedback(null);
    },
    [feedbacks, updateFeedback, selectedFeedbacks, columnPrefsMap],
  );

  const handleStatusChange = useCallback(
    async (feedbackId: string, newStatus: FeedbackStatus) => {
      const feedback: any = feedbacks.find((f) => f?._id === feedbackId);
      if (!feedback || feedback.status === newStatus) return;

      await updateFeedback({
        feedbackId: feedback._id as Id<"feedback">,
        status: newStatus,
        notes: feedback.notes
      });
    },
    [feedbacks, updateFeedback],
  );

  const handleTypeChange = useCallback(
    async (feedbackId: string, newType: FeedbackType) => {
      const feedback: any = feedbacks.find((f) => f?._id === feedbackId);
      if (!feedback || feedback.type === newType) return;

      await updateBatchFeedbacks({
        feedbackIds: [feedback._id as Id<"feedback">],
        type: newType
      });
    },
    [feedbacks, updateBatchFeedbacks],
  );

  const handleDeleteFeedback = useCallback(
    async (feedbackId: string) => {
      setSelectedFeedbacks((prev) => {
        const newSet = new Set(prev);
        newSet.delete(feedbackId);
        return newSet;
      });

      await deleteFeedback({ 
        feedbackId: feedbackId as Id<"feedback"> 
      });
    },
    [deleteFeedback],
  );

  return (
    <div className="h-full p-4">
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        measuring={{
          droppable: {
            strategy: MeasuringStrategy.Always,
          },
        }}
      >
        <div className="grid grid-cols-5 gap-2 h-[calc(100vh-10rem)] mx-auto w-full overflow-x-auto overflow-y-none">
          {FEEDBACK_STATUS.map(
            (status: any) =>
              statusVisibility[status.value as FeedbackStatus] && (
                <KanbanColumn
                  key={status.value}
                  id={`column-${status.value}`}
                  title={status.label}
                  icon={status.icon || (() => null)}
                  color={status.color || ""}
                  feedbacks={
                    processedFeedbacks.filter(
                      (feedback: any) => feedback.status === status.value,
                    ) as Feedback[]
                  }
                  selectedFeedbacks={selectedFeedbacks}
                  onSelectFeedback={handleSelectFeedback}
                  onStatusChange={handleStatusChange}
                  onPriorityChange={handleTypeChange as any}
                  onEdit={handleEditFeedback}
                  onCreateFeedback={handleCreateFeedback}
                  onDelete={handleDeleteFeedback}
                  onStatusVisibilityChange={onStatusVisibilityChange}
                />
              ),
          )}
        </div>

        <DragOverlay>
          {activeFeedback && (
            <>
              {selectedFeedbacks.has(String(activeFeedback._id)) && selectedFeedbacks.size > 1 ? (
                <div className="relative space-y-2">
                  <div className="relative rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 rotate-2 shadow-lg">
                    <KanbanCard feedback={activeFeedback} />
                  </div>
                  <div className="absolute inset-0 -rotate-2 rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 opacity-70 -translate-y-1" />
                  {selectedFeedbacks.size > 2 && (
                    <div className="absolute inset-0 rotate-1 rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 opacity-50 -translate-y-2" />
                  )}
                </div>
              ) : (
                <div className="relative rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 rotate-2 shadow-lg">
                  <KanbanCard feedback={activeFeedback} />
                </div>
              )}
            </>
          )}
        </DragOverlay>
      </DndContext>

      <BottomBarFeedback
        selectedFeedbacks={selectedFeedbackObjects}
        onDeselectAll={() => {
          setSelectedFeedbacks(new Set());
          setSelectedFeedbackObjects([]);
        }}
      />

      <CreateFeedbackModal
        open={isCreateModalOpen}
        onOpenChange={handleModalClose}
        defaultStatus={defaultStatus as Feedback["status"] | undefined}
        feedbackToEdit={feedbackToEdit as FeedbackToEdit | undefined}
      />
    </div>
  );
}
