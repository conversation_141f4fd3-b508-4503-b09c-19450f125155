import { cn } from "@workspace/ui/lib/utils";
import { Task } from "@workspace/backend/convex/lib/types";
import { format } from "date-fns";
import { UserAvatar } from "@workspace/ui/components/user-avatar";
import { IconClock } from "@tabler/icons-react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { PrioritySelector } from "@/components/tasks/priority-selector";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useMemo } from "react";
import { TaskContextMenu } from "@/app/v/tasks/_components/task-context-menu";

interface CalendarTaskProps {
  task: Task;
  className?: string;
  isDraggable?: boolean;
  onEdit?: () => void;
}

export function CalendarTask({
  task,
  className,
  isDraggable = true,
  onEdit,
}: CalendarTaskProps) {
  const updateTask = useMutation(api.tasks.update);
  const deleteTask = useMutation(api.tasks.deleteTask);
  const favorites = useQuery(api.favorites.get) || [];
  const toggleFavorite = useMutation(api.favorites.toggle);

  const isFavorite = useMemo(() => {
    return favorites.some((f) => f?.objectId === task._id);
  }, [favorites, task._id]);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: task._id,
    disabled: !isDraggable,
  });

  const style = useMemo(() => {
    const transformString = transform ? CSS.Transform.toString(transform) : "";
    const styleObj: React.CSSProperties = {
      transform: isDragging
        ? `rotate(2deg) ${transformString}`
        : transformString,
      transition,
      opacity: isDragging ? 0.5 : task.status === "done" ? 0.5 : 1,
      cursor: isDragging ? "grabbing" : isDraggable ? "grab" : "default",
      touchAction: "none",
      position: "relative",
      zIndex: isDragging ? 50 : "auto",
    };

    return styleObj;
  }, [transform, transition, isDragging, task.status, isDraggable]);

  const handleStatusChange = async (
    taskId: Id<"tasks">,
    newStatus: Task["status"],
  ) => {
    await updateTask({
      id: taskId,
      status: newStatus,
      statusHistory: [
        ...(task.statusHistory || []),
        {
          status: newStatus,
          timestamp: Date.now(),
          userId: task.createdBy,
        },
      ],
    });
  };

  const handlePriorityChange = async (
    taskId: Id<"tasks">,
    newPriority: Task["priority"],
  ) => {
    await updateTask({
      id: taskId,
      priority: newPriority,
    });
  };

  const handleDelete = async (e: React.MouseEvent, taskId: Id<"tasks">) => {
    try {
      await deleteTask({
        id: taskId,
      });
    } catch (error) {
      console.error("Error deleting task:", error);
    }
  };

  const handleFavorite = async (e: React.MouseEvent, taskId: Id<"tasks">) => {
    try {
      await toggleFavorite({
        objectId: taskId,
      });
    } catch (error) {
      console.error("Error toggling task favorite status:", error);
    }
  };

  return (
    <TaskContextMenu
      task={task}
      isFavorite={isFavorite}
      onEdit={(e) => onEdit?.()}
      onFavorite={(e, taskId) => handleFavorite(e, taskId as Id<"tasks">)}
      onStatusChange={(taskId, newStatus) =>
        handleStatusChange(taskId as Id<"tasks">, newStatus)
      }
      onPriorityChange={(taskId, newPriority) =>
        handlePriorityChange(taskId as Id<"tasks">, newPriority)
      }
      onDelete={(e, taskId) => handleDelete(e, taskId as Id<"tasks">)}
    >
      <div
        ref={setNodeRef}
        style={style}
        {...(isDraggable ? { ...attributes, ...listeners } : {})}
        className={cn(
          "group relative flex flex-col gap-1 rounded-lg border bg-zinc-100 dark:bg-accent/50 dark:hover:hover-bg hover:bg-zinc-100 p-2",
          isDragging ? "border-blue-500 shadow-lg" : "border-accent",
          task.status === "done" && "opacity-50",
          isDraggable && "hover:cursor-grab active:cursor-grabbing",
          className,
        )}
        onClick={(e) => {
          e.stopPropagation();
          onEdit?.();
        }}
      >
        <div className="flex items-start justify-between gap-2">
          <div className="flex flex-col min-w-0 truncate">
            {task.dueDate && (
              <div className="text-xs text-zinc-500 flex items-center gap-1">
                <IconClock className="h-3 w-3" />
                {format(new Date(task.dueDate), "h:mm a")}
              </div>
            )}
            <p className="text-sm text-zinc-800 dark:text-zinc-100 truncate">
              {task.title}
            </p>
          </div>
          <div className="flex items-center gap-1">
            <PrioritySelector
              priority={task.priority}
              taskId={task._id as Id<"tasks">}
              onPriorityChange={(taskId, newPriority) =>
                handlePriorityChange(taskId as Id<"tasks">, newPriority)
              }
            />
            {typeof task.assignee === "object" && task.assignee !== null && (
              <UserAvatar user={task.assignee} size="sm" />
            )}
          </div>
        </div>
      </div>
    </TaskContextMenu>
  );
}
