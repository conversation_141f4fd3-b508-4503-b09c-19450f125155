import { Task } from "@workspace/backend/convex/lib/types";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { CalendarTask } from "./calendar-task";

interface UnscheduledTaskProps {
  task: Task;
}

function UnscheduledTaskItem({ task }: UnscheduledTaskProps) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({
      id: task._id,
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <CalendarTask task={task} />
    </div>
  );
}

interface UnscheduledTasksListProps {
  tasks: Task[];
}

export function UnscheduledTasksList({ tasks }: UnscheduledTasksListProps) {
  return (
    <div className="h-full flex flex-col bg-zinc-100 dark:bg-zinc-900/50 rounded-lg">
      <h3 className="font-medium text-sm p-4 border-b border-zinc-200 dark:border-zinc-800">
        Unscheduled Tasks
      </h3>
      <div className="flex-1 min-h-0 overflow-y-auto p-4 space-y-2">
        {tasks.map((task) => (
          <UnscheduledTaskItem key={task._id} task={task} />
        ))}
      </div>
    </div>
  );
}
