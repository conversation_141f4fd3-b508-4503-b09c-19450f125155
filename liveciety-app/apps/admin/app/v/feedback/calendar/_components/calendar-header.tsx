import { Button } from "@workspace/ui/components/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import {
  addDays,
  addMonths,
  addWeeks,
  format,
  subDays,
  subMonths,
  subWeeks,
} from "date-fns";
import { useCalendar } from "./calendar-context";
import {
  IconCalendar,
  IconCalendarMonth,
  IconCalendarWeek,
} from "@tabler/icons-react";

export function CalendarHeader() {
  const { view, setView, currentDate, setCurrentDate } = useCalendar();

  const navigateToToday = () => setCurrentDate(new Date());

  const navigateBack = () => {
    switch (view) {
      case "day":
        setCurrentDate(subDays(currentDate, 1));
        break;
      case "week":
        setCurrentDate(subWeeks(currentDate, 1));
        break;
      case "month":
        setCurrentDate(subMonths(currentDate, 1));
        break;
    }
  };

  const navigateForward = () => {
    switch (view) {
      case "day":
        setCurrentDate(addDays(currentDate, 1));
        break;
      case "week":
        setCurrentDate(addWeeks(currentDate, 1));
        break;
      case "month":
        setCurrentDate(addMonths(currentDate, 1));
        break;
    }
  };

  return (
    <div className="flex items-center justify-between px-4 py-2 border-b">
      <div className="flex items-center space-x-1">
        <Button
          variant={view === "day" ? "outline" : "ghost"}
          size="sm"
          onClick={() => setView("day")}
          className="font-medium"
        >
          <IconCalendar className="w-4 h-4" />
          Day
        </Button>
        <Button
          variant={view === "week" ? "outline" : "ghost"}
          size="sm"
          onClick={() => setView("week")}
          className="font-medium"
        >
          <IconCalendarWeek className="w-4 h-4" />
          Week
        </Button>
        <Button
          variant={view === "month" ? "outline" : "ghost"}
          size="sm"
          onClick={() => setView("month")}
          className="font-medium"
        >
          <IconCalendarMonth className="w-4 h-4" />
          Month
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="text-sm font-medium">
          {format(currentDate, "MMMM yyyy")}
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="outline"
            size="sm"
            onClick={navigateBack}
            className="h-7 w-7"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={navigateToToday}
            className="font-medium"
          >
            Today
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={navigateForward}
            className="h-7 w-7"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
