"use client";

import React, { useEffect, useState, useMemo } from "react";
import FeedbackList from "./list";
import { getLocalStorage, setLocalStorage } from "@/lib/local-storage";
import {
  ColumnFiltersState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { columns } from "./list/_components/columns";
import { useQuery } from "convex/react";
import { Button } from "@workspace/ui/components/button";
import { IconPlus } from "@tabler/icons-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { api } from "@workspace/backend/convex/_generated/api";
import { Feedback } from "@workspace/backend/convex/lib/types";
import { TableSettings } from "./list/_components/table-settings";

import FeedbackBoard from "./board";
import FeedbackCalendar from "./calendar";

import type { User } from "@workspace/backend/convex/lib/types";

const STORAGE_KEY = "feedback_preferences";

const FeedbackToolbar = ({
  table,
  groupBy,
  onGroupByChange,
}: {
  table: any;
  groupBy: string;
  onGroupByChange: (value: string) => void;
}) => {
  return (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">Group by:</span>
        <Select value={groupBy} onValueChange={onGroupByChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select grouping" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="status">Status</SelectItem>
            <SelectItem value="type">Type</SelectItem>
            <SelectItem value="createdAt">Created Date</SelectItem>
            <SelectItem value="createdBy">Created By</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="rounded-sm">
          {table.getFilteredRowModel().rows.length} items
        </Badge>
      </div>
    </div>
  );
};

const FeedbackSettings = ({
  showCompletedFeedback,
  onShowCompletedFeedbackChange,
}: {
  showCompletedFeedback: boolean;
  onShowCompletedFeedbackChange: (value: boolean) => void;
}) => {
  return (
    <div className="flex items-center gap-2">
      <Button
        variant={showCompletedFeedback ? "default" : "outline"}
        size="sm"
        onClick={() => onShowCompletedFeedbackChange(!showCompletedFeedback)}
      >
        {showCompletedFeedback ? "Hide Completed" : "Show Completed"}
      </Button>
    </div>
  );
};

const FeedbackClient = () => {
  const feedbackQuery = useQuery(api.feedback.getFeedback, { favorites: false });
  const feedback = useMemo(() => feedbackQuery || [], [feedbackQuery]);
  
  const [view, setView] = useState("list");

  const [statusVisibility, setStatusVisibility] = useState<Record<string, boolean>>({
    new: true,
    in_progress: true,
    completed: true,
    rejected: true
  });

  const user = useMemo(() => ({
    _id: "user123" as any,
    name: "User",
    email: "<EMAIL>",
    activeWorkspace: { role: "org:admin" as const }
  }), []);

  const [isMounted, setIsMounted] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([
    { id: "createdAt", desc: true },
  ]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState({});
  const [groupBy, setGroupBy] = useState("status");
  const [showCompletedFeedback, setShowCompletedFeedback] = useState(true);

  useEffect(() => {
    const storedPrefs = getLocalStorage(STORAGE_KEY);
    if (storedPrefs) {
      try {
        const prefs = JSON.parse(storedPrefs);
        if (prefs.sorting) setSorting(prefs.sorting);
        if (prefs.columnVisibility) setColumnVisibility(prefs.columnVisibility);
        if (prefs.columnFilters) setColumnFilters(prefs.columnFilters);
        if (prefs.groupBy) setGroupBy(prefs.groupBy);
        if (prefs.showCompletedFeedback !== undefined) 
          setShowCompletedFeedback(prefs.showCompletedFeedback);
        if (prefs.view) setView(prefs.view);
        if (prefs.statusVisibility) setStatusVisibility(prefs.statusVisibility);
      } catch (e) {
        console.error("Error parsing stored preferences", e);
      }
    }
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;
    
    const preferences = {
      sorting,
      columnVisibility,
      columnFilters,
      groupBy,
      showCompletedFeedback,
      view,
      statusVisibility
    };
    setLocalStorage(STORAGE_KEY, JSON.stringify(preferences));
  }, [sorting, columnVisibility, columnFilters, groupBy, showCompletedFeedback, view, statusVisibility, isMounted]);

  const table = useReactTable({
    data: feedback as Feedback[],
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    enableSorting: true,
    enableMultiSort: false,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    if (feedback.length > 0 || feedback === null) {
      setIsLoading(false);
    }
  }, [feedback]);

  const handleStatusVisibilityChange = (status: string, visible: boolean) => {
    setStatusVisibility(prev => ({
      ...prev,
      [status]: visible
    }));
  };

  if (!isMounted) return null;

  return (
    <>
      <div className="border-b border-muted p-3 w-full flex justify-between items-center">
        <FeedbackToolbar
          table={table}
          groupBy={groupBy}
          onGroupByChange={setGroupBy}
        />
        <TableSettings
          showCompletedFeedbacks={showCompletedFeedback}
          onShowCompletedFeedbacksChange={setShowCompletedFeedback}
          statusVisibility={statusVisibility}
          onStatusVisibilityChange={handleStatusVisibilityChange}
          table={table}
          view={view}
          onViewChange={setView}
          groupBy={groupBy}
          onGroupByChange={setGroupBy}
          user={user as User}
        />
      </div>

      <div>
        {view === "list" && (
          <FeedbackList
            table={table}
            isLoading={isLoading}
            groupBy={groupBy}
            showCompletedFeedback={showCompletedFeedback}
            onStatusVisibilityChange={handleStatusVisibilityChange}
          />
        )}
        
        {view === "kanban" && (
          <FeedbackBoard
            table={table}
            statusVisibility={statusVisibility}
            onStatusVisibilityChange={handleStatusVisibilityChange}
          />
        )}

        {view === "calendar" && (
          <FeedbackCalendar />
        )}
      </div>
    </>
  );
};

export default FeedbackClient;
