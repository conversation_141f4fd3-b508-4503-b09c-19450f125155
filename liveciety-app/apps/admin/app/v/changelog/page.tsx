"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { Separator } from "@workspace/ui/components/separator";
import { format } from "date-fns";
import { PlusIcon, Pencil, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import adminPackageJson from "@workspace/app/package.json";

type ChangeType = "feature" | "improvement" | "bugfix";

type ChangeItem = {
  type: ChangeType;
  description: string;
};

type ChangelogEntry = {
  _id: string;
  version: string;
  date: string;
  isPublished: boolean;
  changes: ChangeItem[];
  summary?: string;
};

const ChangelogList = ({ entries }: { entries: ChangelogEntry[] }) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [entryToDelete, setEntryToDelete] = useState<string | null>(null);
  const [entryToEdit, setEntryToEdit] = useState<ChangelogEntry | null>(null);
  
  const deleteChangelogEntry = useMutation(api.changelog.deleteChangelogEntry);

  const handleDeleteClick = (id: string) => {
    setEntryToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleEditClick = (entry: ChangelogEntry) => {
    setEntryToEdit(entry);
    setEditDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!entryToDelete) return;

    try {
      await deleteChangelogEntry({ id: entryToDelete as Id<"changelog"> });
      toast.success("Changelog entry deleted successfully");
      setDeleteDialogOpen(false);
    } catch (error) {
      toast.error("Failed to delete changelog entry");
      console.error(error);
    }
  };

  const renderChangeTypeBadge = (type: ChangeType) => {
    const styles = {
      feature: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      improvement: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      bugfix: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
    };

    const labels = {
      feature: "New",
      improvement: "Improved",
      bugfix: "Fixed"
    };

    return (
      <Badge variant="outline" className={styles[type]}>
        {labels[type]}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {entries.length === 0 ? (
        <div className="text-center p-8 text-muted-foreground">
          No changelog entries found. Create one to get started.
        </div>
      ) : (
        entries.map((entry) => (
          <Card key={entry._id} className="relative">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    Version {entry.version}
                    {entry.isPublished ? (
                      <Badge variant="default" className="ml-2">Published</Badge>
                    ) : (
                      <Badge variant="outline" className="ml-2">Draft</Badge>
                    )}
                  </CardTitle>
                  <CardDescription>{entry.date}</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8"
                        onClick={() => handleEditClick(entry)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Edit entry</TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8 text-destructive hover:text-destructive"
                        onClick={() => handleDeleteClick(entry._id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Delete entry</TooltipContent>
                  </Tooltip>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {entry.summary && (
                <p className="text-sm mb-4">{entry.summary}</p>
              )}
              <ul className="space-y-2">
                {entry.changes.map((change, index) => (
                  <li key={index} className="flex gap-2 items-start">
                    {renderChangeTypeBadge(change.type)}
                    <span className="text-sm">{change.description}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))
      )}

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this changelog entry? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {entryToEdit && (
        <EditChangelogForm 
          entry={entryToEdit} 
          open={editDialogOpen} 
          onOpenChange={setEditDialogOpen}
        />
      )}
    </div>
  );
};

const EditChangelogForm = ({ 
  entry, 
  open, 
  onOpenChange, 
}: { 
  entry: ChangelogEntry; 
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  const [version, setVersion] = useState(entry.version);
  const [date, setDate] = useState(entry.date);
  const [summary, setSummary] = useState(entry.summary || "");
  const [isPublished, setIsPublished] = useState(entry.isPublished);
  const [changes, setChanges] = useState<ChangeItem[]>(entry.changes);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const updateChangelogEntry = useMutation(api.changelog.updateChangelogEntry);

  const handleAddChange = () => {
    setChanges([...changes, { type: "feature", description: "" }]);
  };

  const handleRemoveChange = (index: number) => {
    setChanges(changes.filter((_, i) => i !== index));
  };

  const handleChangeTypeChange = (value: string, index: number) => {
    const newChanges = [...changes];
    newChanges[index]!.type = value as ChangeType;
    setChanges(newChanges);
  };

  const handleDescriptionChange = (value: string, index: number) => {
    const newChanges = [...changes];
    newChanges[index]!.description = value;
    setChanges(newChanges);
  };

  const handleSubmit = async () => {
    if (!version.trim()) {
      toast.error("Version is required");
      return;
    }

    if (!date.trim()) {
      toast.error("Date is required");
      return;
    }

    const validChanges = changes.filter(change => change.description.trim());
    if (validChanges.length === 0) {
      toast.error("At least one change with description is required");
      return;
    }

    setIsSubmitting(true);

    try {
      await updateChangelogEntry({
        id: entry._id as Id<"changelog">,
        version,
        date,
        isPublished,
        changes: validChanges,
        summary: summary.trim() || undefined
      });

      toast.success("Changelog entry updated successfully");
      onOpenChange(false);
    } catch (error) {
      toast.error("Failed to update changelog entry");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Changelog Entry</DialogTitle>
          <DialogDescription>
            Update this changelog entry's details.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-version">Version</Label>
              <Input
                id="edit-version"
                placeholder="e.g., 1.2.0"
                value={version}
                onChange={(e) => setVersion(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-date">Date</Label>
              <Input
                id="edit-date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-summary">Summary (Optional)</Label>
            <Textarea
              id="edit-summary"
              placeholder="Brief overview of this release"
              value={summary}
              onChange={(e) => setSummary(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center mb-2">
              <Label>Changes</Label>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                onClick={handleAddChange}
              >
                Add Change
              </Button>
            </div>

            {changes.map((change, index) => (
              <div key={index} className="flex gap-2 items-start mb-2">
                <Select
                  value={change.type}
                  onValueChange={(value) => handleChangeTypeChange(value, index)}
                >
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="feature">New Feature</SelectItem>
                    <SelectItem value="improvement">Improvement</SelectItem>
                    <SelectItem value="bugfix">Bug Fix</SelectItem>
                  </SelectContent>
                </Select>
                
                <Input
                  placeholder="Description of the change"
                  value={change.description}
                  onChange={(e) => handleDescriptionChange(e.target.value, index)}
                  className="flex-1"
                />
                
                {changes.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => handleRemoveChange(index)}
                    className="h-10 w-10 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="edit-published"
              checked={isPublished}
              onChange={() => setIsPublished(!isPublished)}
              className="h-4 w-4 rounded border-gray-300"
            />
            <Label htmlFor="edit-published" className="text-sm font-normal">
              {isPublished ? "Published (visible to users)" : "Save as draft"}
            </Label>
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Update Changelog"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const ChangelogForm = ({ defaultVersion }: { defaultVersion: string }) => {
  const [version, setVersion] = useState(defaultVersion || "");
  const [date, setDate] = useState(format(new Date(), "yyyy-MM-dd"));
  const [summary, setSummary] = useState("");
  const [isPublished, setIsPublished] = useState(false);
  const [changes, setChanges] = useState<ChangeItem[]>([
    { type: "feature", description: "" }
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formOpen, setFormOpen] = useState(false);

  const addChangelogEntry = useMutation(api.changelog.addChangelogEntry);

  const handleAddChange = () => {
    setChanges([...changes, { type: "feature", description: "" }]);
  };

  const handleRemoveChange = (index: number) => {
    setChanges(changes.filter((_, i) => i !== index));
  };

  const handleChangeTypeChange = (value: string, index: number) => {
    const newChanges = [...changes];
    newChanges[index]!.type = value as ChangeType;
    setChanges(newChanges);
  };

  const handleDescriptionChange = (value: string, index: number) => {
    const newChanges = [...changes];
    newChanges[index]!.description = value;
    setChanges(newChanges);
  };

  const handleSubmit = async () => {
    if (!version.trim()) {
      toast.error("Version is required");
      return;
    }

    if (!date.trim()) {
      toast.error("Date is required");
      return;
    }

    const validChanges = changes.filter(change => change.description.trim());
    if (validChanges.length === 0) {
      toast.error("At least one change with description is required");
      return;
    }

    setIsSubmitting(true);

    try {
      await addChangelogEntry({
        version,
        date,
        isPublished,
        changes: validChanges,
        summary: summary.trim() || undefined
      });

      toast.success("Changelog entry added successfully");
      setFormOpen(false);
      
      setVersion("");
      setDate(format(new Date(), "yyyy-MM-dd"));
      setSummary("");
      setIsPublished(false);
      setChanges([{ type: "feature", description: "" }]);
    } catch (error) {
      toast.error("Failed to add changelog entry");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Button 
        onClick={() => setFormOpen(true)} 
        className="flex items-center gap-2"
      >
        <PlusIcon className="h-4 w-4" />
        Add Changelog Entry
      </Button>

      <Dialog open={formOpen} onOpenChange={setFormOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Changelog Entry</DialogTitle>
            <DialogDescription>
              Create a new changelog entry to inform users about updates and changes.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="version">Version</Label>
                <Input
                  id="version"
                  placeholder="e.g., 1.2.0"
                  value={version}
                  onChange={(e) => setVersion(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="summary">Summary (Optional)</Label>
              <Textarea
                id="summary"
                placeholder="Brief overview of this release"
                value={summary}
                onChange={(e) => setSummary(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center mb-2">
                <Label>Changes</Label>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm" 
                  onClick={handleAddChange}
                >
                  Add Change
                </Button>
              </div>

              {changes.map((change, index) => (
                <div key={index} className="flex gap-2 items-start mb-2">
                  <Select
                    value={change.type}
                    onValueChange={(value) => handleChangeTypeChange(value, index)}
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="feature">New Feature</SelectItem>
                      <SelectItem value="improvement">Improvement</SelectItem>
                      <SelectItem value="bugfix">Bug Fix</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Input
                    placeholder="Description of the change"
                    value={change.description}
                    onChange={(e) => handleDescriptionChange(e.target.value, index)}
                    className="flex-1"
                  />
                  
                  {changes.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveChange(index)}
                      className="h-10 w-10 text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="published"
                checked={isPublished}
                onChange={() => setIsPublished(!isPublished)}
                className="h-4 w-4 rounded border-gray-300"
              />
              <Label htmlFor="published" className="text-sm font-normal">
                Publish immediately (will be visible to users)
              </Label>
            </div>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setFormOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save Changelog"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

const ChangelogPage = ({ defaultVersion }: { defaultVersion: string }) => {
  const allChangelogs = useQuery(api.changelog.getAllChangelogsAdmin, { limit: 50 }) || [];
  
  return (
    <div className="container py-6 max-w-5xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Changelog Management</h1>
          <p className="text-muted-foreground">
            Manage application changelog entries that are shown to users
          </p>
        </div>
        <ChangelogForm defaultVersion={defaultVersion} />
      </div>
      
      <Separator className="my-6" />
      
      <ChangelogList entries={allChangelogs} />
    </div>
  );
};

export default function ChangelogPageWrapper() {
  const version = adminPackageJson.version;
  return <ChangelogPage defaultVersion={version} />;
} 