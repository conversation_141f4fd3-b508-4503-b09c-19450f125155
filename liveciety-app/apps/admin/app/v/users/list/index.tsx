import React from "react";
import UserTable from "./_components/data-table";
import { User } from "@workspace/backend/convex/lib/types";
import { Table } from "@tanstack/react-table";

interface UserListProps {
  columns: import("@tanstack/react-table").ColumnDef<User, unknown>[];
  table: Table<User>;
  isLoading: boolean;
  hasMore?: boolean;
  bottomRef?: React.RefObject<HTMLDivElement>;
  searchField: string;
  searchQuery: string;
  showNewSellerApplicants: boolean;
}

const UserList = ({
  columns,
  table,
  isLoading,
  hasMore,
  bottomRef,
  searchField,
  searchQuery,
  showNewSellerApplicants,
}: UserListProps) => {
  return (
    <UserTable
      columns={columns}
      table={table}
      isLoading={isLoading}
      hasMore={hasMore}
      bottomRef={bottomRef}
      searchField={searchField}
      searchQuery={searchQuery}
      showNewSellerApplicants={showNewSellerApplicants}
    />
  );
};

export default UserList;
