"use client";

import { notFound, useRouter, useSearchParams } from "next/navigation";
import { UserViewClient } from "../_components/user-view-client";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useQuery } from "convex/react";
import React from "react";

export default function UserViewPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = React.use(params);
  const user = useQuery(api.users.ADMIN_getUserById, { userId: id as Id<"users"> });
  const router = useRouter();
  const searchParams = useSearchParams();

  if (user === undefined) return <div>Loading...</div>;
  if (!user) return notFound();

  const handleBack = () => {
    const params = new URLSearchParams();
    if (searchParams.get("searchField")) params.set("searchField", searchParams.get("searchField")!);
    if (searchParams.get("searchQuery")) params.set("searchQuery", searchParams.get("searchQuery")!);
    if (searchParams.get("showNewSellerApplicants")) params.set("showNewSellerApplicants", searchParams.get("showNewSellerApplicants")!);
    if (searchParams.get("scroll")) params.set("scroll", searchParams.get("scroll")!);
    router.push(`/v/users${params.toString() ? `?${params.toString()}` : ""}`);
  };

  return (
    <>
      <button onClick={handleBack} className="mb-4 px-4 py-2 rounded bg-muted hover:bg-muted/80 border text-sm">← Back to users</button>
      <UserViewClient user={user} />
    </>
  );
} 