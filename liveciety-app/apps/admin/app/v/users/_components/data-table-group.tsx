import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  IconCalendar,
  IconUser,
  IconClock,
  IconUserCircle,
  IconColumns,
  IconListLetters,
  IconSquareRoundedCheckFilled,
} from "@tabler/icons-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import React from "react";
import type { User } from "@workspace/backend/convex/lib/types";
import { Permissions } from "@/lib/permissions";

type GroupingOption = {
  value: string;
  label: string;
  icon: React.ComponentType<any>;
  requiresPermission?: keyof typeof Permissions;
};

const groupingOptions: GroupingOption[] = [
  { value: "dueDate", label: "Due date", icon: IconCalendar },
  {
    value: "assignee",
    label: "Assignee",
    icon: IconUser,
    requiresPermission: "canGroupByAssignee",
  },
  { value: "createdAt", label: "Created At", icon: IconClock },
  { value: "createdBy", label: "Created by", icon: IconUserCircle },
  { value: "status", label: "Status", icon: IconColumns },
];

interface DataTableGroupProps {
  groupBy: string;
  onGroupByChange: (value: string) => void;
  user: User;
}

export function DataTableGroup({
  groupBy,
  onGroupByChange,
  user,
}: DataTableGroupProps) {
  const availableOptions = groupingOptions.filter((option) => {
    if (!option.requiresPermission) return true;
    return Permissions[option.requiresPermission](user);
  });

  const currentGroupLabel = availableOptions.find(
    (option) => option.value === groupBy,
  )?.label;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8 gap-2 rounded-lg flex items-center justify-start"
        >
          <span className="text-xs font-mono">{currentGroupLabel}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-70">
        {availableOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => onGroupByChange(option.value)}
          >
            <option.icon className="mr-2 h-4 w-4" />
            <span className={groupBy === option.value ? "font-medium" : ""}>
              {option.label}
            </span>
            {groupBy === option.value && (
              <span className="ml-auto">
                <IconSquareRoundedCheckFilled className="text-blue-400" />
              </span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
