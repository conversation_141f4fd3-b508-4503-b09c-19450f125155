import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { IconX } from "@tabler/icons-react";
import React, { useState, useRef, useEffect } from "react";
import { Table } from "@tanstack/react-table";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { DataTableFacetedFilter } from "./faceted-filter";
import { PUBLIC_USER_ROLES } from "@/lib/constants";

type SearchField = "username" | "name" | "email" | "_id";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  setSearch: (search: { field: SearchField; value: string }) => void;
  showNewSellerApplicants?: boolean;
  setShowNewSellerApplicants?: (show: boolean) => void;
  aggregatedUsers: number;
}

export function TableToolbar<TData>({
  table,
  setSearch,
  showNewSellerApplicants = false,
  aggregatedUsers,
  setShowNewSellerApplicants = () => {},
}: DataTableToolbarProps<TData>) {
  const [searchField, setSearchField] = useState<SearchField>("name");
  const [localSearchValue, setLocalSearchValue] = useState("");
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!setSearch) return;
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    debounceTimerRef.current = setTimeout(() => {
      setSearch({ field: searchField, value: localSearchValue });
    }, 300);
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [localSearchValue, searchField, setSearch]);

  return (
    <div className="flex items-center gap-2 w-full">
      <Select
        value={searchField}
        onValueChange={val => setSearchField(val as SearchField)}
        disabled={showNewSellerApplicants}
      >
        <SelectTrigger className="!h-8 w-[110px]">
          <SelectValue placeholder="Field" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="username">Username</SelectItem>
          <SelectItem value="name">Name</SelectItem>
          <SelectItem value="email">Email</SelectItem>
          <SelectItem value="_id">ID</SelectItem>
        </SelectContent>
      </Select>
      <Input
        placeholder={showNewSellerApplicants ? "Search disabled for seller applicants" : `Search by ${searchField}...`}
        value={localSearchValue}
        onChange={e => setLocalSearchValue(e.target.value)}
        className="h-8 w-[250px]"
        disabled={showNewSellerApplicants}
      />
      <div className="text-sm text-muted-foreground flex items-center gap-2">
        {aggregatedUsers} users
      </div>
       <DataTableFacetedFilter
          column={table.getColumn("role")}
          title="Role"
          options={PUBLIC_USER_ROLES}
        />
      <Button
        variant={showNewSellerApplicants ? "secondary" : "outline"}
        className="h-8 px-3"
        onClick={() => setShowNewSellerApplicants(!showNewSellerApplicants)}
      >
        {showNewSellerApplicants ? "Showing New Seller Applicants" : "Show New Seller Applicants"}
      </Button>
      {localSearchValue && (
        <Button
          variant="ghost"
          onClick={() => setLocalSearchValue("")}
          className="h-8 px-2"
        >
          <IconX className="h-4 w-4" />
          <span className="sr-only">Clear</span>
        </Button>
      )}
    </div>
  );
}
