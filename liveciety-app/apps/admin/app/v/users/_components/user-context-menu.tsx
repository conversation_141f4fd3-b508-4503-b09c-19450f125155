import React from "react";
import { User } from "@workspace/backend/convex/lib/types";
import { ContextMenu, ContextMenuItemType } from "@/components/context-menu";
import { TASK_STATUS, TASK_PRIORITY, PUBLIC_USER_ROLES } from "@/lib/constants";
import {
  IconAntennaBars5,
  IconEdit,
  IconProgress,
  IconStar,
  IconStarFilled,
  IconTrash,
} from "@tabler/icons-react";

interface UserContextMenuProps {
  children: React.ReactNode;
  user: User;
  isFavorite: boolean;
  onEdit: (e: React.MouseEvent, userId: string) => void;
  onFavorite: (e: React.MouseEvent, userId: string) => void;
  onRoleChange?: (userId: string, newRole: User["role"]) => void;
  onDelete: (e: React.MouseEvent, userId: string) => void;
}

export function UserContextMenu({
  children,
  user,
  isFavorite,
  onEdit,
  onFavorite,
  onRoleChange,
  onDelete,
}: UserContextMenuProps) {
  const menuItems: (ContextMenuItemType | "separator")[] = [
    {
      id: "edit",
      label: "Edit",
      icon: IconEdit,
      onClick: (e) => onEdit(e, user._id),
    },
    {
      id: "favorite",
      label: isFavorite ? "Remove from favorites" : "Add to favorites",
      icon: isFavorite ? IconStarFilled : IconStar,
      iconClassName: isFavorite ? "text-yellow-400" : "",
      onClick: (e) => onFavorite(e, user._id),
    },
    "separator",
    {
      id: "status",
      label: "Status",
      icon: IconProgress,
      indicator: {
        show: true,
        color: PUBLIC_USER_ROLES.find((s) => s.value === user.role)?.color,
      },
      items: PUBLIC_USER_ROLES.map((role) => ({
        id: `role-${role.value}`,
        label: role.label,
        icon: role.icon,
        iconClassName: role.color,
        onClick: () => onRoleChange?.(user._id, role.value as User["role"]),
        indicator: {
          show: user.role === role.value,
          color: role.color,
        },
      })),
    },
    "separator",
    {
      id: "delete",
      label: "Delete user",
      icon: IconTrash,
      iconClassName: "text-red-400",
      onClick: (e) => onDelete(e, user._id),
    },
  ];

  return <ContextMenu items={menuItems}>{children}</ContextMenu>;
}
