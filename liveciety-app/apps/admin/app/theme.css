body {
  @apply overscroll-none bg-transparent;
}

:root {
  --font-sans: var(--font-inter);
  --header-height: calc(var(--spacing) * 12 + 1px);
}

.theme-scaled {
  @media (min-width: 1024px) {
    --radius: 0.6rem;
    --text-lg: 1.05rem;
    --text-base: 0.85rem;
    --text-sm: 0.8rem;
    --spacing: 0.222222rem;
  }

  [data-slot="card"] {
    --spacing: 0.16rem;
  }

  [data-slot="select-trigger"],
  [data-slot="toggle-group-item"] {
    --spacing: 0.222222rem;
  }
}

.theme-default,
.theme-default-scaled {
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
  }
}

.theme-blue,
.theme-blue-scaled {
  --primary: var(--color-blue-600);
  --primary-foreground: var(--color-blue-50);

  @variant dark {
    --primary: var(--color-blue-500);
    --primary-foreground: var(--color-blue-50);
  }
}

.theme-green,
.theme-green-scaled {
  --primary: var(--color-lime-600);
  --primary-foreground: var(--color-lime-50);

  @variant dark {
    --primary: var(--color-lime-600);
    --primary-foreground: var(--color-lime-50);
  }
}

.theme-amber,
.theme-amber-scaled {
  --primary: var(--color-amber-600);
  --primary-foreground: var(--color-amber-50);

  @variant dark {
    --primary: var(--color-amber-500);
    --primary-foreground: var(--color-amber-50);
  }
}

.theme-mono,
.theme-mono-scaled {
  --font-sans: var(--font-mono);
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
  }

  .rounded-xs,
  .rounded-sm,
  .rounded-md,
  .rounded-lg,
  .rounded-xl,
  .rounded-2xl,
  .rounded-3xl,
  .rounded-4xl,
  .rounded-5xl,
  .rounded-6xl,
  .rounded-7xl,
  .rounded-8xl,
  .rounded-9xl,
  .rounded-tl-sm,
  .rounded-tr-sm,
  .rounded-bl-sm,
  .rounded-br-sm,
  .rounded-tl-md,
  .rounded-tr-md,
  .rounded-bl-md,
  .rounded-br-md,
  .rounded-tl-lg,
  .rounded-tr-lg,
  .rounded-bl-lg,
  .rounded-br-lg,
  .rounded-tl-xl,
  .rounded-tr-xl,
  .rounded-bl-xl,
  .rounded-br-xl,
  .rounded-tl-2xl,
  .rounded-tr-2xl,
  .rounded-bl-2xl,
  .rounded-br-2xl,
  .rounded-tl-3xl,
  .rounded-tr-3xl,
  .rounded-bl-3xl,
  .rounded-br-3xl {
    @apply !rounded-none;
    border-radius: 0;
  }

  .shadow-xs,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl {
    @apply !shadow-none;
  }

  [data-slot="toggle-group"],
  [data-slot="toggle-group-item"] {
    @apply !rounded-none !shadow-none;
  }
}