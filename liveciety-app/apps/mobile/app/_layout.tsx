import { Stack } from "expo-router";
import { DarkTheme, ThemeProvider } from "@react-navigation/native";
import { StatusBar } from "expo-status-bar";
import { AuthGuard } from "../components/guards/auth-guard";
import { TrackingPermissionGuard } from "../components/guards/tracking-permission-guard";
import { PortalProvider } from "../components/ui/portal";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { StyleSheet } from "react-native";

export default function RootLayout() {
  return (
    <GestureHandlerRootView style={styles.container}>
      <TrackingPermissionGuard>
        <AuthGuard>
          <ThemeProvider value={DarkTheme}>
            <PortalProvider>
              <Stack screenOptions={{ headerShown: false }}>
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              </Stack>
              <StatusBar style="light" />
            </PortalProvider>
          </ThemeProvider>
        </AuthGuard>
      </TrackingPermissionGuard>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
