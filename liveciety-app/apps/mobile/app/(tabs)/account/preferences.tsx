import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  FlatList,
  Alert,
  Linking,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import { useUser } from "../../../hooks/use-user";
import { useMutation, useAction } from "convex/react";
import { api } from "../../../convex/_generated/api";
// import { GooglePlacesAutocomplete } from "react-native-google-places-autocomplete";
import "react-native-get-random-values";
import { CustomBottomSheet } from "../../../components/ui/bottom-sheet";

export default function PreferencesScreen() {
  const router = useRouter();
  const { user, isLoading } = useUser();
  const updateUser = useMutation(api.users.update);
  const createConnectAccountLink = useAction(
    api.integration.stripe.createConnectAccountLink,
  );
  const [modalVisible, setModalVisible] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [form, setForm] = useState({
    street: "",
    city: "",
    state: "",
    country: "",
    zipCode: "",
    isDefault: false,
  });
  const [placeSelected, setPlaceSelected] = useState(false);

  const addresses = user?.preferences?.shippingAddresses || [];

  const openAddModal = () => {
    setEditingIndex(null);
    setForm({
      street: "",
      city: "",
      state: "",
      country: "",
      zipCode: "",
      isDefault: false,
    });
    setPlaceSelected(false);
    setModalVisible(true);
  };

  const openEditModal = (index: number) => {
    setEditingIndex(index);
    setForm(addresses[index]);
    setPlaceSelected(true);
    setModalVisible(true);
  };

  const handleSave = async () => {
    let newAddresses = [...addresses];
    if (form.isDefault) {
      newAddresses = newAddresses.map((addr: any) => ({
        ...addr,
        isDefault: false,
      }));
    }
    if (editingIndex === null) {
      newAddresses.push(form);
    } else {
      newAddresses[editingIndex] = form;
    }
    await updateUser({
      preferences: { 
        ...user.preferences, 
        shippingAddresses: newAddresses,
      },
      id: user._id,
    });
    setModalVisible(false);
  };

  const handleDelete = async (index: number) => {
    Alert.alert(
      "Delete Address",
      "Are you sure you want to delete this address?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            const newAddresses = addresses.filter(
              (_: any, i: number) => i !== index,
            );
            await updateUser({
              preferences: {
                ...user.preferences,
                shippingAddresses: newAddresses,
              },
              id: user._id,
            });
          },
        },
      ],
    );
  };

  const handleSetDefault = async (index: number) => {
    const newAddresses = addresses.map((addr: any, i: number) => ({
      ...addr,
      isDefault: i === index,
    }));
    await updateUser({
      preferences: {
        ...user.preferences,
        shippingAddresses: newAddresses,
      },
      id: user._id,
    });
  };

  const parseAddress = (details: any) => {
    const getComponent = (type: string) =>
      details.address_components?.find((c: any) => c.types.includes(type))
        ?.long_name || "";
    return {
      street:
        `${getComponent("street_number")} ${getComponent("route")}`.trim(),
      city:
        getComponent("locality") ||
        getComponent("sublocality") ||
        getComponent("administrative_area_level_2"),
      state: getComponent("administrative_area_level_1"),
      country: getComponent("country"),
      zipCode: getComponent("postal_code"),
    };
  };

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
            activeOpacity={0.7}
          >
            <Ionicons name="chevron-back" size={28} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.title}>Preferences</Text>
        </View>

        {/* Stripe Connect Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Setup</Text>
          <View style={styles.card}>
            <View style={styles.cardHeader}>
              <Ionicons name="card" size={24} color="#635BFF" />
              <View style={styles.cardHeaderText}>
                <Text style={styles.cardTitle}>Stripe Connect</Text>
                <Text style={styles.cardSubtitle}>
                  Connect your Stripe account to receive payments
                </Text>
              </View>
            </View>
            
            {user?.stripeAccountId ? (
              <View style={styles.connectedContainer}>
                <View style={styles.statusRow}>
                  <Ionicons name="checkmark-circle" size={20} color="#10B981" />
                  <Text style={styles.connectedText}>Account Connected</Text>
                </View>
                <Text style={styles.connectedSubtext}>
                  You're all set to receive payments
                </Text>
              </View>
            ) : (
              <TouchableOpacity
                style={styles.stripeButton}
                onPress={async () => {
                  try {
                    const res = await createConnectAccountLink({});
                    if (res?.url) {
                      Linking.openURL(res.url);
                    } else {
                      Alert.alert(
                        "Error",
                        "Could not get Stripe onboarding link.",
                      );
                    }
                  } catch (e) {
                    Alert.alert("Error", "Could not connect to Stripe.");
                  }
                }}
                activeOpacity={0.8}
              >
                <Ionicons name="link" size={20} color="#FFFFFF" />
                <Text style={styles.stripeButtonText}>Connect Stripe Account</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Shipping Addresses Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Shipping Addresses</Text>
            <TouchableOpacity
              onPress={openAddModal}
              style={styles.addButton}
              activeOpacity={0.8}
            >
              <Ionicons name="add" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          {addresses.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="location-outline" size={48} color="#6B7280" />
              <Text style={styles.emptyStateTitle}>No addresses yet</Text>
              <Text style={styles.emptyStateSubtitle}>
                Add your first shipping address to get started
              </Text>
              <TouchableOpacity
                onPress={openAddModal}
                style={styles.emptyStateButton}
                activeOpacity={0.8}
              >
                <Text style={styles.emptyStateButtonText}>Add Address</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={addresses}
              keyExtractor={(_: any, i: number) => i.toString()}
              scrollEnabled={false}
              renderItem={({ item, index }) => (
                <View style={styles.addressCard}>
                  <View style={styles.addressHeader}>
                    <View style={styles.addressInfo}>
                      <Text style={styles.addressText}>
                        {item.street}
                      </Text>
                      <Text style={styles.addressSubtext}>
                        {item.city}, {item.state} {item.zipCode}
                      </Text>
                      <Text style={styles.addressSubtext}>
                        {item.country}
                      </Text>
                    </View>
                    {item.isDefault && (
                      <View style={styles.defaultBadge}>
                        <Text style={styles.defaultBadgeText}>Default</Text>
                      </View>
                    )}
                  </View>
                  
                  <View style={styles.addressActions}>
                    {!item.isDefault && (
                      <TouchableOpacity
                        onPress={() => handleSetDefault(index)}
                        style={styles.actionButton}
                        activeOpacity={0.7}
                      >
                        <Ionicons name="star-outline" size={16} color="#10B981" />
                        <Text style={styles.actionButtonText}>Set Default</Text>
                      </TouchableOpacity>
                    )}
                    <TouchableOpacity
                      onPress={() => openEditModal(index)}
                      style={styles.actionButton}
                      activeOpacity={0.7}
                    >
                      <Ionicons name="pencil-outline" size={16} color="#3B82F6" />
                      <Text style={[styles.actionButtonText, { color: "#3B82F6" }]}>Edit</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => handleDelete(index)}
                      style={styles.actionButton}
                      activeOpacity={0.7}
                    >
                      <Ionicons name="trash-outline" size={16} color="#EF4444" />
                      <Text style={[styles.actionButtonText, { color: "#EF4444" }]}>Delete</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            />
          )}
        </View>
      </ScrollView>

      {/* Bottom Sheet for Add/Edit Address */}
      <CustomBottomSheet
        isOpen={modalVisible}
        onClose={() => setModalVisible(false)}
        snapPoints={["90%"]}
        enableDynamicHeight={false}
      >
        <View style={styles.sheetContainer}>
          <View style={styles.sheetHeader}>
            <Text style={styles.sheetTitle}>
              {editingIndex === null ? "Add New Address" : "Edit Address"}
            </Text>
            <TouchableOpacity 
              onPress={() => setModalVisible(false)}
              style={styles.sheetCloseButton}
              activeOpacity={0.7}
            >
              <Ionicons name="close" size={24} color="#9CA3AF" />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.sheetContent} showsVerticalScrollIndicator={false}>
            {editingIndex === null && (
              <View style={styles.autocompleteContainer}>
                <Text style={styles.inputLabel}>Search Address</Text>
                {/* TODO: Re-enable GooglePlacesAutocomplete when fixing web compatibility */}
                <TouchableOpacity
                  style={styles.textInput}
                  onPress={() => setPlaceSelected(true)}
                  activeOpacity={0.7}
                >
                  <Text style={{ color: "#9CA3AF", fontSize: 16 }}>
                    Start typing your address... (Manual entry mode)
                  </Text>
                </TouchableOpacity>
                {/* 
                <GooglePlacesAutocomplete
                  placeholder="Start typing your address..."
                  onPress={(data, details = null) => {
                    if (details) {
                      const parsed = parseAddress(details);
                      setForm((f) => ({ ...f, ...parsed }));
                      setPlaceSelected(true);
                    }
                  }}
                  fetchDetails={true}
                  query={{
                    key: "AIzaSyC7PLsSVuI8klCIVUoQpwrqeq7gEySwbUk",
                    language: "en",
                    types: "address",
                  }}
                  styles={{
                    textInput: {
                      backgroundColor: "#1F2937",
                      color: "#FFFFFF",
                      borderRadius: 12,
                      paddingHorizontal: 16,
                      paddingVertical: 14,
                      fontSize: 16,
                      borderWidth: 1,
                      borderColor: "#374151",
                    },
                    container: { flex: 0, marginBottom: 24 },
                    listView: {
                      backgroundColor: "#1F2937",
                      borderRadius: 12,
                      marginTop: 4,
                    },
                    row: {
                      backgroundColor: "#1F2937",
                      paddingHorizontal: 16,
                      paddingVertical: 12,
                    },
                    description: {
                      color: "#FFFFFF",
                    },
                  }}
                  enablePoweredByContainer={false}
                />
                */}
              </View>
            )}
            
            {(placeSelected || editingIndex !== null) && (
              <View style={styles.formContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Street Address</Text>
                  <TextInput
                    placeholder="Enter street address"
                    placeholderTextColor="#9CA3AF"
                    style={styles.textInput}
                    value={form.street}
                    onChangeText={(t) => setForm((f) => ({ ...f, street: t }))}
                  />
                </View>

                <View style={styles.inputRow}>
                  <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
                    <Text style={styles.inputLabel}>City</Text>
                    <TextInput
                      placeholder="City"
                      placeholderTextColor="#9CA3AF"
                      style={styles.textInput}
                      value={form.city}
                      onChangeText={(t) => setForm((f) => ({ ...f, city: t }))}
                    />
                  </View>
                  <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
                    <Text style={styles.inputLabel}>State</Text>
                    <TextInput
                      placeholder="State"
                      placeholderTextColor="#9CA3AF"
                      style={styles.textInput}
                      value={form.state}
                      onChangeText={(t) => setForm((f) => ({ ...f, state: t }))}
                    />
                  </View>
                </View>

                <View style={styles.inputRow}>
                  <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
                    <Text style={styles.inputLabel}>Zip Code</Text>
                    <TextInput
                      placeholder="12345"
                      placeholderTextColor="#9CA3AF"
                      style={styles.textInput}
                      value={form.zipCode}
                      onChangeText={(t) => setForm((f) => ({ ...f, zipCode: t }))}
                    />
                  </View>
                  <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
                    <Text style={styles.inputLabel}>Country</Text>
                    <TextInput
                      placeholder="Country"
                      placeholderTextColor="#9CA3AF"
                      style={styles.textInput}
                      value={form.country}
                      onChangeText={(t) => setForm((f) => ({ ...f, country: t }))}
                    />
                  </View>
                </View>

                <TouchableOpacity
                  onPress={() =>
                    setForm((f) => ({ ...f, isDefault: !f.isDefault }))
                  }
                  style={styles.checkboxContainer}
                  activeOpacity={0.7}
                >
                  <View style={styles.checkbox}>
                    {form.isDefault && (
                      <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                    )}
                  </View>
                  <Text style={styles.checkboxLabel}>Set as default address</Text>
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>

          {(placeSelected || editingIndex !== null) && (
            <View style={styles.sheetFooter}>
              <TouchableOpacity
                style={[
                  styles.saveButton,
                  {
                    opacity: !(
                      form.street &&
                      form.city &&
                      form.state &&
                      form.country &&
                      form.zipCode
                    ) ? 0.5 : 1
                  }
                ]}
                onPress={handleSave}
                disabled={
                  !(
                    form.street &&
                    form.city &&
                    form.state &&
                    form.country &&
                    form.zipCode
                  )
                }
                activeOpacity={0.8}
              >
                <Text style={styles.saveButtonText}>
                  {editingIndex === null ? "Add Address" : "Save Changes"}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </CustomBottomSheet>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#0F0F0F",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 32,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#1F2937",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: "700",
    color: "#FFFFFF",
    letterSpacing: -0.5,
  },
  section: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 16,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#10B981",
    alignItems: "center",
    justifyContent: "center",
  },
  card: {
    backgroundColor: "#1F2937",
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#374151",
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 20,
  },
  cardHeaderText: {
    marginLeft: 12,
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    color: "#9CA3AF",
    lineHeight: 20,
  },
  connectedContainer: {
    backgroundColor: "#065F46",
    borderRadius: 12,
    padding: 16,
  },
  statusRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  connectedText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#10B981",
    marginLeft: 8,
  },
  connectedSubtext: {
    fontSize: 14,
    color: "#6EE7B7",
    marginTop: 2,
  },
  stripeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#635BFF",
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  stripeButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 48,
    paddingHorizontal: 32,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#FFFFFF",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    color: "#9CA3AF",
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: "#10B981",
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  emptyStateButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  addressCard: {
    backgroundColor: "#1F2937",
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#374151",
  },
  addressHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  addressInfo: {
    flex: 1,
  },
  addressText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  addressSubtext: {
    fontSize: 14,
    color: "#9CA3AF",
    lineHeight: 20,
  },
  defaultBadge: {
    backgroundColor: "#10B981",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  defaultBadgeText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  addressActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: "#374151",
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#10B981",
    marginLeft: 4,
  },
  sheetContainer: {
    flex: 1,
    backgroundColor: "#0F0F0F",
  },
  sheetHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#374151",
  },
  sheetTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  sheetCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#1F2937",
    alignItems: "center",
    justifyContent: "center",
  },
  sheetContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  autocompleteContainer: {
    marginBottom: 24,
  },
  formContainer: {
    gap: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputRow: {
    flexDirection: "row",
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#FFFFFF",
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: "#1F2937",
    color: "#FFFFFF",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "#374151",
  },
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 8,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    backgroundColor: "#374151",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
    borderWidth: 1,
    borderColor: "#10B981",
  },
  checkboxLabel: {
    fontSize: 16,
    color: "#FFFFFF",
    fontWeight: "500",
  },
  sheetFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: "#374151",
  },
  saveButton: {
    backgroundColor: "#10B981",
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
  },
  saveButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});
