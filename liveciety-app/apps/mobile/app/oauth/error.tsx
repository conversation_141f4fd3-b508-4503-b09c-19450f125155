import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

export default function OAuthErrorScreen() {
  const params = useLocalSearchParams();
  const [errorMessage, setErrorMessage] = useState<string>('');
  
  useEffect(() => {
    console.log('🔴 OAuth error screen - Params received:', JSON.stringify(params));
    
    const error = typeof params.error === 'string' 
      ? params.error 
      : Array.isArray(params.error) 
        ? params.error[0] || 'Unknown error' 
        : 'Unknown error';
    
    let friendlyMessage = '';
    switch (error) {
      case 'access_denied':
        friendlyMessage = 'You cancelled the authentication. Please try again if you want to sign in.';
        break;
      case 'no_code':
        friendlyMessage = 'Authentication failed - no authorization code received. Please try again.';
        break;
      case 'no_session_token':
        friendlyMessage = 'Authentication completed but session creation failed. Please try again.';
        break;
      default:
        friendlyMessage = `Authentication failed: ${error}`;
    }
    
    setErrorMessage(friendlyMessage);
  }, [params]);

  const handleRetry = () => {
    console.log('🔵 OAuth error screen - Redirecting to login');
    router.replace('/');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name="warning-outline" size={80} color="#ff6b6b" />
        </View>
        
        <Text style={styles.title}>Authentication Failed</Text>
        <Text style={styles.message}>{errorMessage}</Text>
        
        <TouchableOpacity 
          style={styles.retryButton}
          onPress={handleRetry}
        >
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.cancelButton}
          onPress={() => router.replace('/')}
        >
          <Text style={styles.cancelButtonText}>Back to Login</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#18181B',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    marginBottom: 30,
  },
  title: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  message: {
    color: '#aaaaaa',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 8,
    marginBottom: 16,
    width: '100%',
    maxWidth: 300,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#444444',
    width: '100%',
    maxWidth: 300,
  },
  cancelButtonText: {
    color: '#aaaaaa',
    fontSize: 16,
    textAlign: 'center',
  },
}); 