import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, StyleSheet, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useConvexAuth } from "convex/react";
import { useAuthActions } from "@convex-dev/auth/react";
import { SafeAreaView } from 'react-native-safe-area-context';
import * as SecureStore from 'expo-secure-store';
import { useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import * as Updates from 'expo-updates';

export default function OAuthSuccessScreen() {
  console.log('🟢 OAuth success screen - Component created/rendered!');
  
  const params = useLocalSearchParams();
  const { isAuthenticated, isLoading: authLoading } = useConvexAuth();
  const { signIn } = useAuthActions();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [debugInfo, setDebugInfo] = useState<any>({});

  const exchangeAuthCode = useAction(api.auth.exchangeMobileAuthCode);
  
  console.log('🟢 OAuth success screen - Component initialized with params:', JSON.stringify(params));
  
  useEffect(() => {
    // Log all params for debugging
    console.log('🔵 OAuth success screen - Component mounted');
    console.log('🔵 OAuth success screen - Params received:', JSON.stringify(params));
    setDebugInfo({
      params: JSON.stringify(params),
      hasAuthCode: !!params.authCode,
      hasEmail: !!params.email,
      authLoading,
      convexIsAuthenticated: isAuthenticated
    });
    
    const completeAuth = async () => {
      if (!params.authCode || !params.email) {
        console.error('🔴 Missing authentication parameters in URL');
        setError('Authentication information missing. Please try again.');
        setIsLoading(false);
        return;
      }
      
      try {
        console.log('🔵 OAuth success screen - Exchanging auth code for session...');
        console.log('🔵 OAuth success screen - Email:', params.email);
        
        // Ensure we have a string value for the authCode
        const authCodeValue = typeof params.authCode === 'string' 
          ? params.authCode 
          : Array.isArray(params.authCode) 
            ? params.authCode[0] || '' 
            : String(params.authCode);
            
        if (!authCodeValue) {
          throw new Error('Empty auth code parameter');
        }
        
        // Exchange the auth code for a proper session
        console.log('🔵 OAuth success screen - Calling exchangeAuthCode with:', authCodeValue);
        const result = await exchangeAuthCode({ tempCode: authCodeValue });
        console.log('🔵 OAuth success screen - Exchange result:', result);

        if (!result.success) {
          throw new Error('Failed to exchange auth code for session');
        }

        console.log('🔵 OAuth success screen - Auth code exchanged successfully');

        // Check if we got JWT tokens from the store function
        const tokens = result.tokens || (result.sessionId && result.sessionId.tokens);
        if (tokens) {
          console.log('🔵 OAuth success screen - JWT tokens received, storing in SecureStore...');

          try {
            // Store the JWT token in SecureStore with the same key that ConvexAuthProvider expects
            const jwtKey = '__convexAuthJWT_httpsdecisiveperch342convexcloud';
            await SecureStore.setItemAsync(jwtKey, tokens.token);
            console.log('🔵 OAuth success screen - JWT token stored in SecureStore');

            // Also store the refresh token if needed
            if (tokens.refreshToken) {
              const refreshKey = '__convexAuthRefreshToken_httpsdecisiveperch342convexcloud';
              await SecureStore.setItemAsync(refreshKey, tokens.refreshToken);
              console.log('🔵 OAuth success screen - Refresh token stored in SecureStore');
            }

            console.log('🔵 OAuth success screen - Authentication tokens stored, forcing app reload...');

            // Force a complete app reload to refresh the ConvexAuthProvider state
            if (__DEV__) {
              console.log('🔵 Development mode - forcing app reload to refresh auth state');
              // In development, we can use router.replace to force a refresh
              setIsLoading(false);
              router.replace('/');
            } else {
              // In production, force a complete app reload
              const Updates = require('expo-updates');
              await Updates.reloadAsync();
            }
            return;

          } catch (tokenError) {
            console.error('🔴 OAuth success screen - Failed to store JWT tokens:', tokenError);
            // Fall through to the backup approach
          }
        }

        console.log('🔵 OAuth success screen - No JWT tokens received or failed to store, falling back to basic flow...');
        console.log('🔵 OAuth success screen - Attempting to trigger Convex Auth recognition...');

        // The OAuth flow has completed successfully and the user exists in the database
        // The issue is that the client-side auth state is not recognizing the authentication
        // Let's try a different approach: just redirect and let the auth system handle it
        console.log('🔵 OAuth success screen - OAuth flow complete, redirecting to trigger auth state refresh...');

        try {
          // Store the user information as well
          await SecureStore.setItemAsync('pendingOAuthUserId', result.user._id);
          await SecureStore.setItemAsync('pendingOAuthEmail', result.user.email);
          await SecureStore.setItemAsync('pendingOAuthName', result.user.name);
          await SecureStore.setItemAsync('pendingOAuthImage', result.user.image);
          await SecureStore.setItemAsync('pendingOAuthUsername', result.user.username);

          console.log('🔵 OAuth success screen - User info stored, forcing app reload...');

          // Force the app to completely restart to refresh the auth state
          if (__DEV__) {
            // In development, just navigate and hope for the best
            console.log('🔵 Development mode - redirecting to home');
            setIsLoading(false);
            router.replace('/');
          } else {
            // In production, force a complete app reload
            await Updates.reloadAsync();
          }

        } catch (storageError) {
          console.error('🔴 OAuth success screen - Failed to store session info:', storageError);
          console.log('🔵 OAuth success screen - Redirecting anyway to refresh auth state...');

          // Final fallback: redirect anyway
          setIsLoading(false);
          router.replace('/');
        }
        
      } catch (error) {
        console.error('🔴 OAuth completion error:', error);
        setError(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setIsLoading(false);
      }
    };
    
    completeAuth();
  }, [params.authCode, params.email]); // Only depend on the specific params we need
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.content}>
          {isLoading ? (
            <>
              <ActivityIndicator size="large" color="#ffffff" />
              <Text style={styles.loadingText}>Completing authentication...</Text>
              
              {/* Debug info */}
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>Debug Info:</Text>
                {Object.entries(debugInfo).map(([key, value]) => (
                  <Text key={key} style={styles.debugText}>
                    {key}: {JSON.stringify(value)}
                  </Text>
                ))}
              </View>
            </>
          ) : error ? (
            <>
              <Text style={styles.errorText}>{error}</Text>
              <Text 
                style={styles.linkText}
                onPress={() => router.replace('/')}
              >
                Return to login
              </Text>
              
              {/* Debug info */}
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>Debug Info:</Text>
                {Object.entries(debugInfo).map(([key, value]) => (
                  <Text key={key} style={styles.debugText}>
                    {key}: {JSON.stringify(value)}
                  </Text>
                ))}
              </View>
            </>
          ) : null}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#18181B',
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    color: '#ffffff',
    marginTop: 20,
    fontSize: 16,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  linkText: {
    color: '#3b82f6',
    fontSize: 16,
    textDecorationLine: 'underline',
    marginBottom: 20,
  },
  debugContainer: {
    marginTop: 30,
    padding: 15,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 8,
    width: '100%',
  },
  debugTitle: {
    color: '#ffcc00',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  debugText: {
    color: '#aaaaaa',
    fontSize: 12,
    marginBottom: 5,
  }
});