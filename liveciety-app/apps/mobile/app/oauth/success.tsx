import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, StyleSheet, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useConvexAuth } from "convex/react";
import { SafeAreaView } from 'react-native-safe-area-context';
import * as SecureStore from 'expo-secure-store';
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import * as Updates from 'expo-updates';

export default function OAuthSuccessScreen() {
  console.log('🟢 OAuth success screen - Component created/rendered!');
  
  const params = useLocalSearchParams();
  const { isAuthenticated, isLoading: authLoading } = useConvexAuth();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [debugInfo, setDebugInfo] = useState<any>({});

  const exchangeAuthCode = useMutation(api.auth.exchangeMobileAuthCode);
  
  console.log('🟢 OAuth success screen - Component initialized with params:', JSON.stringify(params));
  
  useEffect(() => {
    // Log all params for debugging
    console.log('🔵 OAuth success screen - Component mounted');
    console.log('🔵 OAuth success screen - Params received:', JSON.stringify(params));
    setDebugInfo({
      params: JSON.stringify(params),
      hasAuthCode: !!params.authCode,
      hasEmail: !!params.email,
      authLoading,
      convexIsAuthenticated: isAuthenticated
    });
    
    const completeAuth = async () => {
      if (!params.authCode || !params.email) {
        console.error('🔴 Missing authentication parameters in URL');
        setError('Authentication information missing. Please try again.');
        setIsLoading(false);
        return;
      }
      
      try {
        console.log('🔵 OAuth success screen - Exchanging auth code for session...');
        console.log('🔵 OAuth success screen - Email:', params.email);
        
        // Ensure we have a string value for the authCode
        const authCodeValue = typeof params.authCode === 'string' 
          ? params.authCode 
          : Array.isArray(params.authCode) 
            ? params.authCode[0] || '' 
            : String(params.authCode);
            
        if (!authCodeValue) {
          throw new Error('Empty auth code parameter');
        }
        
        // Exchange the auth code for a proper session
        console.log('🔵 OAuth success screen - Calling exchangeAuthCode with:', authCodeValue);
        const result = await exchangeAuthCode({ tempCode: authCodeValue });
        console.log('🔵 OAuth success screen - Exchange result:', result);
        
        if (!result.success) {
          throw new Error('Failed to exchange auth code for session');
        }
        
        console.log('🔵 OAuth success screen - Auth code exchanged successfully');

        // Store the user information and force a complete app restart
        // This is the most reliable way to ensure the auth state is properly updated
        console.log('🔵 OAuth success screen - Storing user session and restarting app...');

        try {
          // Store the user ID in SecureStore so we can retrieve it after restart
          await SecureStore.setItemAsync('pendingOAuthUserId', result.user._id);
          await SecureStore.setItemAsync('pendingOAuthEmail', result.user.email);
          await SecureStore.setItemAsync('pendingOAuthName', result.user.name);
          await SecureStore.setItemAsync('pendingOAuthImage', result.user.image);
          await SecureStore.setItemAsync('pendingOAuthUsername', result.user.username);

          console.log('🔵 OAuth success screen - User info stored, forcing app reload...');

          // Force the app to completely restart
          if (__DEV__) {
            // In development, just navigate and hope for the best
            console.log('🔵 Development mode - redirecting to home');
            setIsLoading(false);
            router.replace('/');
          } else {
            // In production, force a complete app reload
            await Updates.reloadAsync();
          }

        } catch (storageError) {
          console.error('🔴 OAuth success screen - Failed to store user info:', storageError);
          console.log('🔵 OAuth success screen - Redirecting anyway to refresh auth state...');

          // Final fallback: redirect anyway
          setIsLoading(false);
          router.replace('/');
        }
        
      } catch (error) {
        console.error('🔴 OAuth completion error:', error);
        setError(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setIsLoading(false);
      }
    };
    
    completeAuth();
  }, [params.authCode, params.email]); // Only depend on the specific params we need
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.content}>
          {isLoading ? (
            <>
              <ActivityIndicator size="large" color="#ffffff" />
              <Text style={styles.loadingText}>Completing authentication...</Text>
              
              {/* Debug info */}
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>Debug Info:</Text>
                {Object.entries(debugInfo).map(([key, value]) => (
                  <Text key={key} style={styles.debugText}>
                    {key}: {JSON.stringify(value)}
                  </Text>
                ))}
              </View>
            </>
          ) : error ? (
            <>
              <Text style={styles.errorText}>{error}</Text>
              <Text 
                style={styles.linkText}
                onPress={() => router.replace('/')}
              >
                Return to login
              </Text>
              
              {/* Debug info */}
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>Debug Info:</Text>
                {Object.entries(debugInfo).map(([key, value]) => (
                  <Text key={key} style={styles.debugText}>
                    {key}: {JSON.stringify(value)}
                  </Text>
                ))}
              </View>
            </>
          ) : null}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#18181B',
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    color: '#ffffff',
    marginTop: 20,
    fontSize: 16,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  linkText: {
    color: '#3b82f6',
    fontSize: 16,
    textDecorationLine: 'underline',
    marginBottom: 20,
  },
  debugContainer: {
    marginTop: 30,
    padding: 15,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 8,
    width: '100%',
  },
  debugTitle: {
    color: '#ffcc00',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  debugText: {
    color: '#aaaaaa',
    fontSize: 12,
    marginBottom: 5,
  }
});