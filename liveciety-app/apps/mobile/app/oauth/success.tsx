import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, StyleSheet, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useConvexAuth } from "convex/react";
import { useAuthActions } from "@convex-dev/auth/react";
import { SafeAreaView } from 'react-native-safe-area-context';
import * as SecureStore from 'expo-secure-store';
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import * as Updates from 'expo-updates';

export default function OAuthSuccessScreen() {
  console.log('🟢 OAuth success screen - Component created/rendered!');
  
  const params = useLocalSearchParams();
  const { isAuthenticated, isLoading: authLoading } = useConvexAuth();
  const { signIn } = useAuthActions();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [debugInfo, setDebugInfo] = useState<any>({});
  
  const exchangeAuthCode = useMutation(api.auth.exchangeMobileAuthCode);
  const createManualSession = useMutation(api.auth.createManualSession);
  
  console.log('🟢 OAuth success screen - Component initialized with params:', JSON.stringify(params));
  
  useEffect(() => {
    // Log all params for debugging
    console.log('🔵 OAuth success screen - Component mounted');
    console.log('🔵 OAuth success screen - Params received:', JSON.stringify(params));
    setDebugInfo({
      params: JSON.stringify(params),
      hasAuthCode: !!params.authCode,
      hasEmail: !!params.email,
      authLoading,
      convexIsAuthenticated: isAuthenticated
    });
    
    const completeAuth = async () => {
      if (!params.authCode || !params.email) {
        console.error('🔴 Missing authentication parameters in URL');
        setError('Authentication information missing. Please try again.');
        setIsLoading(false);
        return;
      }
      
      try {
        console.log('🔵 OAuth success screen - Exchanging auth code for session...');
        console.log('🔵 OAuth success screen - Email:', params.email);
        
        // Ensure we have a string value for the authCode
        const authCodeValue = typeof params.authCode === 'string' 
          ? params.authCode 
          : Array.isArray(params.authCode) 
            ? params.authCode[0] || '' 
            : String(params.authCode);
            
        if (!authCodeValue) {
          throw new Error('Empty auth code parameter');
        }
        
        // Exchange the auth code for a proper session
        console.log('🔵 OAuth success screen - Calling exchangeAuthCode with:', authCodeValue);
        const result = await exchangeAuthCode({ tempCode: authCodeValue });
        console.log('🔵 OAuth success screen - Exchange result:', result);
        
        if (!result.success) {
          throw new Error('Failed to exchange auth code for session');
        }
        
        console.log('🔵 OAuth success screen - Auth code exchanged successfully');
        
        // Create a manual session using the validated user
        console.log('🔵 OAuth success screen - Creating manual session...');
        try {
          const sessionResult = await createManualSession({ userId: result.user._id });
          if (sessionResult.success) {
            console.log('🔵 OAuth success screen - Manual session created successfully:', sessionResult.sessionId);
            
            // Force the app to completely restart to pick up the new session
            console.log('🔵 OAuth success screen - Forcing complete app reload...');
            
            // Use Expo Updates to reload the entire app
            try {
              if (__DEV__) {
                // In development, just navigate and hope for the best
                console.log('🔵 Development mode - redirecting to home');
                setIsLoading(false);
                router.replace('/');
              } else {
                // In production, force a complete app reload
                await Updates.reloadAsync();
              }
            } catch (updateError) {
              console.error('🔴 Failed to reload app:', updateError);
              // Fallback to normal redirect
              setIsLoading(false);
              router.replace('/');
            }
            
          } else {
            throw new Error('Manual session creation failed');
          }
        } catch (sessionError) {
          console.error('🔴 OAuth success screen - Manual session creation failed:', sessionError);
          console.log('🔵 OAuth success screen - Redirecting anyway to refresh auth state...');
          
          // Fallback: redirect anyway 
          setIsLoading(false);
          router.replace('/');
        }
        
      } catch (error) {
        console.error('🔴 OAuth completion error:', error);
        setError(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setIsLoading(false);
      }
    };
    
    completeAuth();
  }, [params.authCode, params.email]); // Only depend on the specific params we need
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.content}>
          {isLoading ? (
            <>
              <ActivityIndicator size="large" color="#ffffff" />
              <Text style={styles.loadingText}>Completing authentication...</Text>
              
              {/* Debug info */}
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>Debug Info:</Text>
                {Object.entries(debugInfo).map(([key, value]) => (
                  <Text key={key} style={styles.debugText}>
                    {key}: {JSON.stringify(value)}
                  </Text>
                ))}
              </View>
            </>
          ) : error ? (
            <>
              <Text style={styles.errorText}>{error}</Text>
              <Text 
                style={styles.linkText}
                onPress={() => router.replace('/')}
              >
                Return to login
              </Text>
              
              {/* Debug info */}
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>Debug Info:</Text>
                {Object.entries(debugInfo).map(([key, value]) => (
                  <Text key={key} style={styles.debugText}>
                    {key}: {JSON.stringify(value)}
                  </Text>
                ))}
              </View>
            </>
          ) : null}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#18181B',
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    color: '#ffffff',
    marginTop: 20,
    fontSize: 16,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  linkText: {
    color: '#3b82f6',
    fontSize: 16,
    textDecorationLine: 'underline',
    marginBottom: 20,
  },
  debugContainer: {
    marginTop: 30,
    padding: 15,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 8,
    width: '100%',
  },
  debugTitle: {
    color: '#ffcc00',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  debugText: {
    color: '#aaaaaa',
    fontSize: 12,
    marginBottom: 5,
  }
});