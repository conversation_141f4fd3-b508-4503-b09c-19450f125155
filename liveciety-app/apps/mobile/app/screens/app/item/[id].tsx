import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Dimensions,
  Share,
  Alert,
  Modal,
  TextInput,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useQuery, useAction } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Image } from "expo-image";
import { Ionicons } from "@expo/vector-icons";
import { Id } from "../../../../convex/_generated/dataModel";
import { TabView, SceneMap, TabBar } from "react-native-tab-view";
import { <PERSON>eProvider, CardField, useStripe } from "@stripe/stripe-react-native";

const { width } = Dimensions.get("window");

function formatDate(ts: number) {
  const d = new Date(ts);
  return `${d.getMonth() + 1}/${d.getDate()}/${d.getFullYear()}`;
}

function getStorageIdFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    return urlObj.searchParams.get('storageId');
  } catch {
    return null;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#18181A",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#18181A",
  },
  emptyText: {
    color: "#fff",
    fontSize: 18,
    marginTop: 16,
  },
  imageContainer: {
    width: "100%",
    height: width * 0.7,
    backgroundColor: "#232325",
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  image: {
    width: width,
    height: width * 0.7,
    backgroundColor: "#232325",
  },
  noImage: {
    justifyContent: "center",
    alignItems: "center",
  },
  closeButton: {
    position: "absolute",
    top: 60,
    right: 16,
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 20,
    padding: 4,
    zIndex: 10,
  },
  headerCard: {
    backgroundColor: "#232325",
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    padding: 20,
    marginBottom: 8,
    alignItems: "center",
  },
  title: {
    color: "#fff",
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 4,
    textAlign: "center",
  },
  available: {
    color: "#1a96d2",
    fontSize: 15,
    marginBottom: 4,
  },
  price: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 4,
  },
  shipping: {
    color: "#aaa",
    fontSize: 14,
    fontWeight: "400",
  },
  actionRow: {
    flexDirection: "row",
    gap: 24,
    marginTop: 12,
    justifyContent: "center",
  },
  iconButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    backgroundColor: "#29292b",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  iconLabel: {
    color: "#fff",
    fontSize: 14,
    marginLeft: 4,
  },
  sellerCard: {
    backgroundColor: "#232325",
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 12,
    marginBottom: 8,
    marginTop: 8,
  },
  sellerRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  sellerAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 8,
  },
  sellerAvatarFallback: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#444",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  sellerName: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
  },
  sellerStatsRow: {
    flexDirection: "row",
    gap: 16,
    marginTop: 2,
  },
  sellerStat: {
    color: "#1a96d2",
    fontSize: 14,
    fontWeight: "600",
  },
  messageButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#18181A",
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 8,
    marginLeft: 8,
  },
  tabsContainer: {
    marginHorizontal: 12,
    marginTop: 8,
    backgroundColor: "#232325",
    borderRadius: 16,
    overflow: "hidden",
    minHeight: 200,
  },
  tabView: {
    backgroundColor: "#232325",
    borderRadius: 16,
  },
  tabContent: {
    padding: 16,
  },
  sectionLabel: {
    color: "#aaa",
    fontSize: 15,
    fontWeight: "bold",
    marginBottom: 2,
  },
  sectionValue: {
    color: "#fff",
    fontSize: 16,
    marginBottom: 8,
  },
  sectionRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  bottomActions: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 24,
    paddingHorizontal: 12,
    gap: 8,
  },
  offerButton: {
    backgroundColor: "#333",
    paddingHorizontal: 28,
    paddingVertical: 14,
    borderRadius: 12,
    width: "50%",
  },
  offerButtonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
    textAlign: "center",
  },
  buyButton: {
    backgroundColor: "#1a96d2",
    paddingHorizontal: 28,
    paddingVertical: 14,
    borderRadius: 12,
    width: "50%",
  },
  buyButtonText: {
    color: "#000",
    fontWeight: "bold",
    fontSize: 16,
    textAlign: "center",
  },
  backButton: {
    backgroundColor: "#1a96d2",
    paddingHorizontal: 28,
    paddingVertical: 14,
    borderRadius: 12,
    width: "50%",
    marginTop: 16,
  },
  backButtonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
    textAlign: "center",
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#232325',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    minHeight: '70%',
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalBody: {
    flex: 1,
  },
  input: {
    backgroundColor: '#18181A',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    color: '#fff',
    fontSize: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
});

interface PurchaseModalProps {
  visible: boolean;
  onClose: () => void;
  item: any;
  onPurchase: (args: {
    productId: Id<"products">;
    quantity: number;
    shippingAddress: {
      street: string;
      city: string;
      state: string;
      country: string;
      zipCode: string;
    };
    isDirect: boolean;
  }) => Promise<{ orderId: string; clientSecret: string | null }>;
}

const PurchaseModal = ({ visible, onClose, item, onPurchase }: PurchaseModalProps) => {
  const { initPaymentSheet, presentPaymentSheet } = useStripe();
  const [loading, setLoading] = useState(false);
  const [address, setAddress] = useState({
    street: "",
    city: "",
    state: "",
    country: "",
    zipCode: "",
  });

  const handlePurchase = async () => {
    if (!address.street || !address.city || !address.state || !address.country || !address.zipCode) {
      Alert.alert("Error", "Please fill in all address fields");
      return;
    }

    setLoading(true);
    try {
      const { orderId, clientSecret } = await onPurchase({
        productId: item._id,
        quantity: 1,
        shippingAddress: address,
        isDirect: true
      });

      if (!clientSecret) {
        throw new Error("Failed to create payment intent");
      }

      const { error: initError } = await initPaymentSheet({
        paymentIntentClientSecret: clientSecret,
        merchantDisplayName: "Liveciety",
      });

      if (initError) {
        throw new Error(initError.message);
      }

      const { error: presentError } = await presentPaymentSheet();
      
      if (presentError) {
        throw new Error(presentError.message);
      }

      Alert.alert("Success", "Your purchase was successful!");
      onClose();
    } catch (error) {
      Alert.alert("Error", error instanceof Error ? error.message : "Failed to process payment");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Complete Purchase</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            <Text style={styles.sectionLabel}>Shipping Address</Text>
            <TextInput
              style={styles.input}
              placeholder="Street Address"
              placeholderTextColor="#666"
              value={address.street}
              onChangeText={(text) => setAddress({ ...address, street: text })}
            />
            <TextInput
              style={styles.input}
              placeholder="City"
              placeholderTextColor="#666"
              value={address.city}
              onChangeText={(text) => setAddress({ ...address, city: text })}
            />
            <TextInput
              style={styles.input}
              placeholder="State"
              placeholderTextColor="#666"
              value={address.state}
              onChangeText={(text) => setAddress({ ...address, state: text })}
            />
            <TextInput
              style={styles.input}
              placeholder="Country"
              placeholderTextColor="#666"
              value={address.country}
              onChangeText={(text) => setAddress({ ...address, country: text })}
            />
            <TextInput
              style={styles.input}
              placeholder="ZIP Code"
              placeholderTextColor="#666"
              value={address.zipCode}
              onChangeText={(text) => setAddress({ ...address, zipCode: text })}
            />

            <TouchableOpacity
              style={[styles.buyButton, loading && styles.buttonDisabled]}
              onPress={handlePurchase}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#000" />
              ) : (
                <Text style={styles.buyButtonText}>Complete Purchase</Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default function ItemProfileScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{ id: string }>();
  const id = params.id as Id<"products">;
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [purchaseLoading, setPurchaseLoading] = useState(false);
  const [shippingAddress, setShippingAddress] = useState({
    street: "",
    city: "",
    state: "",
    country: "",
    zipCode: "",
  });
  
  const createOrderWithStripe = useAction(api.orders.createOrderWithStripe);

  // Add error handling for invalid ID
  if (!id) {
    return (
      <View style={styles.loadingContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#666" />
        <Text style={styles.emptyText}>Invalid product ID</Text>
      </View>
    );
  }

  // Wrap queries in try-catch to handle potential errors
  const item = useQuery(api.products.getProduct, { productId: id });
  
  // Only attempt to fetch seller data if we have a valid item with sellerId
  const seller = useQuery(
    api.users.getUser,
    item && item.sellerId ? { userId: item.sellerId } : "skip"
  );

  const userReviews = useQuery(
    api.users.getUserReviews,
    item && item.sellerId ? { reviewedUserId: item.sellerId, limit: 3 } : "skip"
  );

  const [tabIndex, setTabIndex] = useState(0);
  const [routes] = useState([
    { key: "details", title: "Details" },
    { key: "seller", title: "Seller Info" },
  ]);

  // Loading state
  if (item === undefined) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1a96d2" />
      </View>
    );
  }

  // Error state
  if (!item) {
    return (
      <View style={styles.loadingContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#666" />
        <Text style={styles.emptyText}>Item not found</Text>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // --- Tabs ---
  const renderDetailsTab = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionLabel}>Description</Text>
      <Text style={styles.sectionValue}>
        {item?.description || "No description provided."}
      </Text>
      <View style={styles.sectionRow}>
        <Text style={styles.sectionLabel}>Category</Text>
        <Text style={styles.sectionValue}>
          {item?.category || "Uncategorized"}
        </Text>
      </View>
      <View style={styles.sectionRow}>
        <Text style={styles.sectionLabel}>Created on</Text>
        <Text style={styles.sectionValue}>
          {item?._creationTime ? formatDate(item._creationTime) : "N/A"}
        </Text>
      </View>
    </View>
  );

  const renderSellerTab = () => {
    // Handle case where seller data failed to load
    if (seller === undefined && item.sellerId) {
      return (
        <View style={styles.tabContent}>
          <ActivityIndicator size="small" color="#1a96d2" />
          <Text style={[styles.sectionValue, { textAlign: 'center', marginTop: 8 }]}>
            Loading seller information...
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.tabContent}>
        <Text style={styles.sectionLabel}>Store Name</Text>
        <Text style={styles.sectionValue}>
          {item?.sellerName || "Unknown Seller"}
        </Text>
        <Text style={styles.sectionLabel}>Bio</Text>
        <Text style={styles.sectionValue}>
          {seller?.bio || "No bio provided."}
        </Text>
        <View style={styles.sectionRow}>
          <Text style={styles.sectionLabel}>Premier Shop</Text>
          <Ionicons
            name="ribbon-outline"
            size={18}
            color="#1a96d2"
            style={{ marginLeft: 4 }}
          />
        </View>
        <View style={styles.sectionRow}>
          <Text style={styles.sectionLabel}>Rating</Text>
          <Text style={styles.sectionValue}>
            {"5.0"}
          </Text>
        </View>
        <View style={styles.sectionRow}>
          <Text style={styles.sectionLabel}>Sold</Text>
          <Text style={styles.sectionValue}>
            {"0"}
          </Text>
        </View>
      </View>
    );
  };

  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: "#1a96d2" }}
      style={{ backgroundColor: "#232325" }}
      labelStyle={{
        color: "#fff",
        fontWeight: "bold",
        fontSize: 16,
        textTransform: "none",
      }}
      pressColor="#1a96d2"
    />
  );

  // --- Main Render ---
  const handleBuyNowPress = () => {
    setShowPurchaseModal(true);
  };

  return (
    <StripeProvider
      publishableKey={process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || ""}
      urlScheme="your-url-scheme" // Add your URL scheme here
    >
      <ScrollView style={styles.container}>
        {/* Image */}
        <View style={styles.imageContainer}>
          {(() => {
            const images = item?.images;
            if (Array.isArray(images) && images.length > 0) {
              return (
                <Image
                  source={{ uri: images[0] }}
                  style={styles.image}
                  contentFit="cover"
                />
              );
            }
            return (
              <View style={[styles.image, styles.noImage]}>
                <Ionicons name="image-outline" size={64} color="#666" />
              </View>
            );
          })()}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => router.back()}
          >
            <Ionicons name="close" size={28} color="#fff" />
          </TouchableOpacity>
        </View>
        {/* Title, Price, Available */}
        <View style={styles.headerCard}>
          <Text style={styles.title}>{item?.name || "Untitled Item"}</Text>
          <Text style={styles.available}>{item?.quantity || 0} Available</Text>
          <Text style={styles.price}>
            ${item?.price?.toFixed(2) || "0.00"}{" "}
            <Text style={styles.shipping}>+ $35.37 shipping + taxes</Text>
          </Text>
          {/* Save/Share Row */}
          <View style={styles.actionRow}>
            <TouchableOpacity style={styles.iconButton}>
              <Ionicons name="bookmark-outline" size={24} color="#fff" />
              <Text style={styles.iconLabel}>1</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() =>
                Share.share({ message: `Check out this item: ${item?.name || ""}` })
              }
            >
              <Ionicons name="share-outline" size={24} color="#fff" />
              <Text style={styles.iconLabel}>Share</Text>
            </TouchableOpacity>
          </View>
        </View>
        {/* Seller Card */}
        <View style={styles.sellerCard}>
          <View style={styles.sellerRow}>
            {(() => {
              const avatarUrl = seller?.avatarUrl;
              if (avatarUrl) {
                // If it's already a full URL, use it directly
                if (avatarUrl.startsWith('http')) {
                  return (
                    <Image
                      source={{ uri: avatarUrl }}
                      style={styles.sellerAvatar}
                    />
                  );
                }
                // If it's a storage ID, use it directly
                return (
                  <Image
                    source={{ uri: avatarUrl }}
                    style={styles.sellerAvatar}
                  />
                );
              }
              return (
                <View style={styles.sellerAvatarFallback}>
                  <Ionicons name="person" size={28} color="#fff" />
                </View>
              );
            })()}
            <View style={{ flex: 1 }}>
              <Text style={styles.sellerName}>{item?.sellerName || "Unknown Seller"}</Text>
              <View style={styles.sellerStatsRow}>
                <Text style={styles.sellerStat}>
                  ⭐ {"5.0"}
                </Text>
                <Text style={styles.sellerStat}>
                  {"0"} Sold
                </Text>
                <Text style={styles.sellerStat}>
                  {userReviews?.length || 0} Reviews
                </Text>
              </View>
            </View>
            <TouchableOpacity style={styles.messageButton}>
              <Ionicons name="chatbubble-outline" size={20} color="#1a96d2" />
            </TouchableOpacity>
          </View>
        </View>
        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <TabView
            navigationState={{ index: tabIndex, routes }}
            renderScene={SceneMap({
              details: renderDetailsTab,
              seller: renderSellerTab,
            })}
            onIndexChange={setTabIndex}
            initialLayout={{ width: width - 32 }}
            renderTabBar={renderTabBar}
            style={styles.tabView}
          />
        </View>
        {/* Action Buttons */}
        <View style={styles.bottomActions}>
          <TouchableOpacity style={styles.offerButton}>
            <Text style={styles.offerButtonText}>Make Offer</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.buyButton}
            onPress={handleBuyNowPress}
          >
            <Text style={styles.buyButtonText}>Buy Now</Text>
          </TouchableOpacity>
        </View>

        {/* Add the purchase modal */}
        <PurchaseModal
          visible={showPurchaseModal}
          onClose={() => setShowPurchaseModal(false)}
          item={item}
          onPurchase={createOrderWithStripe}
        />
      </ScrollView>
    </StripeProvider>
  );
}
