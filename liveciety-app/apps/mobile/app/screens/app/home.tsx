import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
  FlatList,
  Dimensions,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import UserSearch from "../../../components/ui/user-search";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import LiveStreamCard from "../../../components/live-stream-card";
import LiveStreamCardSkeleton from "../../../components/skeletons/live-stream-card-skeleton";
import { categories as allCategories } from "../../../lib/constants";
import { getImageUrl } from "../../../lib/utils";

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("For You");

  const streams = useQuery(api.streams.listActiveStreams, {});
  const scheduledResult = useQuery(api.streams.listByCategory, {
    category: undefined,
    paginationOpts: { numItems: 10, cursor: null },
  });
  const scheduledStreams = (scheduledResult?.page || []).filter(
    (s: any) => s.status === "scheduled" && !s.isLive,
  );

  const currentUser = useQuery(api.users.viewer, {});
  const followedUsers =
    useQuery(
      api.users.getFollowing,
      currentUser?._id ? { userId: currentUser._id } : "skip",
    ) || [];

  const followedSellers = (Array.isArray(followedUsers) ? followedUsers : []).filter(
    (u: any) => u.role === "seller",
  );

  // Get followed categories from user preferences
  const followedCategoryIds = currentUser?.preferences?.categories || [];
  // Map category ids to display names
  const followedCategories = followedCategoryIds
    .map((catId: string) => allCategories.find((c) => c.id === catId)?.title)
    .filter(Boolean);

  // Compose the display categories with unique values
  const displayCategories = Array.from(new Set([
    "For You",
    "Followed Hosts",
    "Sports Memorabilia", 
    "Football Cards",
    ...followedCategories,
  ]));

  // Loading states
  const isLiveStreamsLoading = streams === undefined;
  const isScheduledStreamsLoading = scheduledResult === undefined;

  // Event handlers
  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: "Check out Liveciety!",
        url: "https://liveciety.com",
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleNotificationsPress = () => {
    router.push("/screens/app/notifications");
  };

  // Helper: get category id from title
  const getCategoryIdByTitle = (title: string) => {
    const cat = allCategories.find((c) => c.title === title);
    return cat ? cat.id : undefined;
  };

  // Filter streams based on selected tab and status
  let filteredLiveStreams = (streams || []).filter((stream: any) => {
    // Include streams that are live, regardless of visibility
    return stream.status === "live";
  });

  let filteredScheduledStreams = (scheduledStreams || []).filter((stream: any) => {
    // Include streams that are scheduled and not ended
    return stream.status === "scheduled" && stream.status !== "ended";
  });

  if (selectedCategory === "Followed Hosts") {
    const followedSellerIds = new Set(followedSellers.map((s: any) => s._id));
    filteredLiveStreams = filteredLiveStreams.filter((stream: any) =>
      followedSellerIds.has(stream.userId)
    );
    filteredScheduledStreams = filteredScheduledStreams.filter((stream: any) =>
      followedSellerIds.has(stream.userId)
    );
  } else if (selectedCategory !== "For You") {
    const selectedCategoryId = getCategoryIdByTitle(selectedCategory);
    if (selectedCategoryId) {
      filteredLiveStreams = filteredLiveStreams.filter(
        (stream: any) => stream.category === selectedCategoryId
      );
      filteredScheduledStreams = filteredScheduledStreams.filter(
        (stream: any) => stream.category === selectedCategoryId
      );
    }
  }

  // Add some debug logging
  console.log("Filtered Live Streams:", filteredLiveStreams);
  console.log("Filtered Scheduled Streams:", filteredScheduledStreams);

  // Combine all streams for grid display
  const allStreams = [...filteredLiveStreams, ...filteredScheduledStreams];

  // Render stream card item
  const renderStreamCard = ({ item, index }: { item: any, index: number }) => {
    if (!item) return null;
    
    return (
      <View style={[
        styles.cardContainer,
        index % 2 === 0 ? { marginRight: 8 } : { marginLeft: 8 }
      ]}>
        <LiveStreamCard
          streamId={item._id}
          isBookmarked={item.isBookmarked || false}
          username={item.streamer?.username || item.username || ""}
          title={item.title}
          category={item.category || ""}
          subcategory={item.subcategory || ""}
          viewerCount={typeof item.viewerCount === "number" ? item.viewerCount : 0}
          thumbnailUrl={item.thumbnail}
          profileImageUrl={getImageUrl(item.streamer?.image || item.userAvatar || "")}
          userColor={item.userColor || "#2C2C2E"}
          onPress={() => router.push(`/screens/stream/${item._id}`)}
        />
      </View>
    );
  };

  // Render skeleton for loading
  const renderSkeleton = ({ index }: { index: number }) => (
    <View style={[
      styles.cardContainer,
      index % 2 === 0 ? { marginRight: 8 } : { marginLeft: 8 }
    ]}>
      <LiveStreamCardSkeleton />
    </View>
  );

  console.log("allStreams", allStreams);

  return (
    <SafeAreaView edges={["top"]} style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <UserSearch
            placeholder="Search Liveciety"
            style={styles.userSearch}
          />
        </View>
        {!isSearchActive && (
          <View style={styles.headerIcons}>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={handleNotificationsPress}
            >
              <View>
                <Ionicons name="notifications-outline" size={24} color="#fff" />
              </View>
            </TouchableOpacity>
            <TouchableOpacity style={styles.iconButton} onPress={handleShare}>
              <Ionicons name="share-social-outline" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Category Tabs */}
      {!isSearchActive && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesScroll}
          contentContainerStyle={styles.categoriesContent}
        >
          {displayCategories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryButton,
                selectedCategory === category && styles.categoryButtonActive
              ]}
              onPress={() => setSelectedCategory(category || "")}
            >
              <Text
                style={[
                  styles.categoryButtonText,
                  selectedCategory === category && styles.categoryButtonTextActive
                ]}
              >
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}

      {/* Streams Grid */}
      {!isSearchActive && (
        <View style={styles.content}>
          {isLiveStreamsLoading || isScheduledStreamsLoading ? (
            <FlatList
              data={Array(6).fill(null)} // Show 6 skeleton items
              renderItem={renderSkeleton}
              numColumns={2}
              keyExtractor={(_, index) => `skeleton-${index}`}
              contentContainerStyle={styles.gridContainer}
              columnWrapperStyle={styles.row}
              showsVerticalScrollIndicator={false}
            />
          ) : allStreams.length > 0 ? (
            <FlatList
              data={allStreams}
              renderItem={renderStreamCard}
              numColumns={2}
              keyExtractor={(item) => item?._id || ""}
              contentContainerStyle={styles.gridContainer}
              columnWrapperStyle={styles.row}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.noStreamsContainer}>
              <Text style={styles.noStreamsText}>No current streams</Text>
            </View>
          )}
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    zIndex: 1,
  },
  searchContainer: {
    flex: 1,
    height: 44,
  },
  headerIcons: {
    flexDirection: "row",
    marginLeft: 12,
  },
  userSearch: {
    flex: 1,
  },
  iconButton: {
    marginLeft: 16,
    padding: 4,
  },
  notificationBadge: {
    position: "relative",
  },
  redDot: {
    position: "absolute",
    top: -2,
    right: -2,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#FF3B30",
    borderWidth: 1,
    borderColor: "#1C1C1E",
  },
  categoriesScroll: {
    maxHeight: 50,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)",
  },
  categoriesContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  categoryButton: {
    marginRight: 24,
    paddingVertical: 8,
    minHeight: 32,
    justifyContent: "center",
  },
  categoryButtonText: {
    color: "#8E8E93",
    fontSize: 16,
    fontWeight: "600",
  },
  categoryButtonActive: {
    borderBottomWidth: 2,
    borderBottomColor: "#fff",
    paddingBottom: 12,
    marginBottom: -10,
  },
  categoryButtonTextActive: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  cardContainer: {
    width: (width - 40) / 2,
  },
  row: {
    justifyContent: "space-between",
  },
  gridContainer: {
    padding: 16,
    paddingBottom: 100, // Extra padding for tab bar
  },
  noStreamsContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 64,
  },
  noStreamsText: {
    color: "#8E8E93",
    fontSize: 18,
    fontWeight: "500",
    textAlign: "center",
  },
});
