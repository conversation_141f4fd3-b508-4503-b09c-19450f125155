import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  View,
  StyleSheet,
  Text,
  Image,
  TouchableOpacity,
  FlatList,
  Platform,
  StatusBar,
  ImageBackground,
  SafeAreaView,
  KeyboardAvoidingView,
  Dimensions,
  TextInput,
  Animated,
  Share,
  Alert,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { Ionicons, MaterialCommunityIcons, Feather } from "@expo/vector-icons";
import { useUser } from "../../../hooks/use-user";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import StreamSkeleton from "../../../components/skeletons/stream-skeleton";
import { getImageUrl } from "../../../lib/utils";
import {
  LiveKitRoom,
  useTracks,
  VideoTrack,
  AudioSession,
  isTrackReference,
  useRoomInfo,
  useParticipants,
  useLocalParticipant,
} from "@livekit/react-native";
import { Track, ConnectionState } from "livekit-client";
import StreamControlsSheet from "../../../components/ui/stream-controls-sheet";
import ProductSelectionSheet from "../../../components/ui/product-selection-sheet";
import PinnedProductsDisplay from "../../../components/ui/pinned-products-display";
import { useEnhancedViewerCount } from "../../../hooks/use-enhanced-viewer-count";

const { height, width } = Dimensions.get("window");

const LIVEKIT_WS_URL = process.env.EXPO_PUBLIC_LIVEKIT_URL;

  // Colors
  const Colors = {
    primary: "#FF6B6B",
    white: "#FFFFFF",
    lightGrey: "#B0B0B0",
    transparent: "transparent",
    lightBlue: "rgba(0, 123, 255, 0.1)",
    red: "#FF4444",
    yellow: "#FFD700",
    black: "#000000",
  };

// Fonts
const Fonts = {
  SemiBold16white: { fontSize: 16, fontWeight: "600", color: Colors.white } as const,
  SemiBold14primary: { fontSize: 14, fontWeight: "600", color: Colors.primary } as const,
  Medium16white: { fontSize: 16, fontWeight: "500", color: Colors.white } as const,
  Regular14lightGrey: { fontSize: 14, fontWeight: "400", color: Colors.lightGrey } as const,
  Regular14white: { fontSize: 14, fontWeight: "400", color: Colors.white } as const,
  SemiBold18white: { fontSize: 18, fontWeight: "600", color: Colors.white } as const,
  Bold24white: { fontSize: 24, fontWeight: "bold", color: Colors.white } as const,
  SemiBold16yellow: { fontSize: 16, fontWeight: "600", color: Colors.yellow } as const,
};

// Default spacing
const Default = {
  fixPadding: 10,
};

function getUniqueID() {
  return Math.floor(Math.random() * Date.now()).toString();
}

const getRandomSignedNum = () => (Math.random() < 0.5 ? -1 : 1);
const getRandomXOutput = () => {
  return getRandomSignedNum() < 0
    ? -Math.random() * width * 0.7
    : Math.random();
};
const getRandomRotateOutput = () => {
  return [getRandomSignedNum() < 0 ? "-60deg" : "60deg", "0deg"];
};

interface AnimatedHeartProps {
  id: string;
  userId?: string;
  color?: string;
  onCompleteAnimation: (id: string) => void;
}

const AnimatedHeart = ({ id, userId, color, onCompleteAnimation }: AnimatedHeartProps) => {
  const animatedValueY = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(animatedValueY, {
      toValue: -height,
      duration: 3000,
      useNativeDriver: true,
    }).start(() => onCompleteAnimation(id));
  }, [animatedValueY, onCompleteAnimation, id]);

  const randomXOutput = useRef(getRandomXOutput()).current;
  const randomRotateOutput = useRef(getRandomRotateOutput()).current;

  return (
    <Animated.View
      style={[
        { right: 30 },
        styles.heartIcon,
        {
          transform: [
            {
              translateY: animatedValueY.interpolate({
                inputRange: [-height, 0],
                outputRange: [-height, 0],
              }),
            },
            {
              translateX: animatedValueY.interpolate({
                inputRange: [-height, 0],
                outputRange: [randomXOutput, 0],
              }),
            },
            {
              rotate: animatedValueY.interpolate({
                inputRange: [-height, 0],
                outputRange: randomRotateOutput,
              }),
            },
            {
              scale: animatedValueY.interpolate({
                inputRange: [-50, 0],
                outputRange: [1, 0.5],
                extrapolate: "clamp",
              }),
            },
          ],
          opacity: animatedValueY.interpolate({
            inputRange: [-height, 0],
            outputRange: [0, 1],
          }),
        },
      ]}
    >
      <Ionicons name="heart" size={25} color={color || Colors.primary} />
    </Animated.View>
  );
};

const StreamScreen = () => {
  const params = useLocalSearchParams<{ streamId: string }>();
  const streamId = params.streamId;
  const { user } = useUser();
  const router = useRouter();

  // Basic state
  const [isHost, setIsHost] = useState(false);
  const [comment, setComment] = useState("");
  const [hearts, setHearts] = useState<{ id: string; userId?: string; color?: string }[]>([]);
  const [liked, setLiked] = useState(false);
  const [showControlsSheet, setShowControlsSheet] = useState(false);
  const [showProductSelection, setShowProductSelection] = useState(false);
  const [heartCooldown, setHeartCooldown] = useState(false);
  const [lastHeartTime, setLastHeartTime] = useState(0);

  // LiveKit state
  const [livekitToken, setLivekitToken] = useState<string | null>(null);
  const [connecting, setConnecting] = useState(false);
  const [livekitError, setLivekitError] = useState<string | null>(null);
  const [ingestInfo, setIngestInfo] = useState<{ ingestUrl: string; streamKey: string } | null>(null);
  const [hostConnected, setHostConnected] = useState(true);
  const [showWaitingScreen, setShowWaitingScreen] = useState(false);
  const [isMicEnabled, setIsMicEnabled] = useState(true);
  const [isCameraEnabled, setIsCameraEnabled] = useState(true);
  const [hostWasDisconnected, setHostWasDisconnected] = useState(false);
  const [showRestartOverlay, setShowRestartOverlay] = useState(false);

  const countAnimatedValue = useRef(new Animated.Value(0)).current;
  const timeout = useRef<NodeJS.Timeout | null>(null);

  // Stream data and messages
  const stream = useQuery(
    api.streams.getStream,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip",
  );

  const messages = useQuery(
    api.streams.listMessages,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip",
  );

  // Listen for new heart messages and animate them
  const lastProcessedMessageId = useRef<string | null>(null);
  
  useEffect(() => {
    if (!messages || !user || messages.length === 0) return;
    
    // Get the latest message
    const latestMessage = messages[0];
    
    // Only process if this is a new message we haven't seen before
    if (latestMessage && 
        latestMessage._id !== lastProcessedMessageId.current &&
        latestMessage.type === "heart" && 
        latestMessage.userId !== user._id) { // Don't animate our own hearts
      
      // Update the last processed message ID
      lastProcessedMessageId.current = latestMessage._id;
      
      // Generate color for the sender
      const userColors = [
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", 
        "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
      ];
      const senderColor = userColors[latestMessage.userId.charCodeAt(0) % userColors.length];
      
      // Add heart animation for other user's heart
      const heartId = getUniqueID();
      setHearts((oldHearts) => {
        // Limit to maximum 20 hearts to prevent memory issues
        const newHearts = [...oldHearts, { 
          id: heartId, 
          userId: latestMessage.userId,
          color: senderColor 
        }];
        return newHearts.slice(-20); // Keep only last 20 hearts
      });
    }
  }, [messages, user]);

  // Mutations and Actions
  const sendMessage = useMutation(api.streams.sendMessage);
  const goLive = useMutation(api.streams.goLive);
  const endStream = useMutation(api.streams.endStream);
  const generateHostToken = useAction(api.integration.livekit.generateHostToken);
  const generateViewerToken = useAction(api.integration.livekit.generateViewerToken);
  const generateIngestInfo = useAction(api.integration.livekit.generateIngestInfo);

  // Determine if user is host
  useEffect(() => {
    if (user && stream) {
      setIsHost(stream.hostId === user._id);
    }
  }, [user, stream]);

  // Use enhanced viewer count system
  const { 
    viewerCount, 
    isTracking, 
    peak, 
    totalUnique, 
    error: viewerError 
  } = useEnhancedViewerCount({
    streamId: streamId as Id<"streams">,
    isHost,
    enabled: !!streamId && !!stream && stream.status === "live",
  });

  // Fetch LiveKit token - Enhanced for cross-device support
  useEffect(() => {
    if (!streamId || !user || !stream) return;
    setLivekitToken(null);
    setLivekitError(null);
    setConnecting(true);
    
    // Allow token generation for live streams or if user is host (for cross-device support)
    if (stream.status === "live" || (isHost && stream.status !== "ended")) {
      const fn = isHost ? generateHostToken : generateViewerToken;
      fn({ streamId: streamId as Id<"streams"> })
        .then((token) => {
          setLivekitToken(token);
          setConnecting(false);
        })
        .catch((err) => {
          setLivekitError("Failed to get LiveKit token: " + (err?.message || err));
          setConnecting(false);
        });
    } else {
      setConnecting(false);
    }
  }, [streamId, user, stream, isHost]);

  // Fetch ingest info for OBS if host
  useEffect(() => {
    if (isHost && stream && !ingestInfo && stream.status !== "live") {
      generateIngestInfo({ streamId: streamId as Id<"streams"> })
        .then(setIngestInfo)
        .catch(() => {});
    }
  }, [isHost, stream, ingestInfo, streamId, generateIngestInfo]);

  // Audio session management
  useEffect(() => {
    if (!livekitToken) return;
    AudioSession.startAudioSession();
    return () => {
      AudioSession.stopAudioSession();
    };
  }, [livekitToken]);

  // Get background image: thumbnail first, then host image, then default
  const getBackgroundImage = useCallback(() => {
    if (stream?.thumbnail) {
      return { uri: stream.thumbnail };
    }
    if (stream?.streamer?.image) {
      return { uri: getImageUrl(stream.streamer.image) };
    }
    return require('../../../assets/adaptive-icon.png');
  }, [stream?.thumbnail, stream?.streamer?.image]);

  const handleCompleteAnimation = useCallback((id: string) => {
    setHearts((oldHearts) => {
      return oldHearts.filter((heart) => heart.id !== id);
    });
  }, []);

  const handleHeartPress = async () => {
    // Rate limiting: Max 3 hearts per second
    const currentTime = Date.now();
    const timeSinceLastHeart = currentTime - lastHeartTime;
    const minInterval = 333; // ~3 hearts per second (1000ms / 3)

    if (heartCooldown || timeSinceLastHeart < minInterval) {
      // Too fast, ignore the press
      return;
    }

    if (!streamId || !user) return;

    try {
      // Set cooldown
      setHeartCooldown(true);
      setLastHeartTime(currentTime);
      
      // Generate a unique color for this user
      const userColors = [
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", 
        "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
      ];
      const userColor = userColors[user._id.charCodeAt(0) % userColors.length];
      
      // Add local heart immediately for responsiveness
      const heartId = getUniqueID();
      setHearts((oldHearts) => {
        // Limit to maximum 20 hearts to prevent memory issues
        const newHearts = [...oldHearts, { 
          id: heartId, 
          userId: user._id,
          color: userColor 
        }];
        return newHearts.slice(-20); // Keep only last 20 hearts
      });
      
      // Send heart message to other users
      await sendMessage({
        streamId: streamId as Id<"streams">,
        text: "❤️", // Heart emoji as message content
        type: "heart", // Special type for heart messages
      });

      setLiked(!liked);
      
      // Heart count animation
      timeout.current = setTimeout(() => {
        Animated.spring(countAnimatedValue, {
          toValue: 0,
          speed: 48,
          useNativeDriver: true,
        }).start();
      }, 100) as any;
      Animated.spring(countAnimatedValue, {
        toValue: -64,
        speed: 48,
        useNativeDriver: true,
      }).start();

      // Reset cooldown after minimum interval
      setTimeout(() => {
        setHeartCooldown(false);
      }, minInterval);

    } catch (error) {
      console.error("Failed to send heart:", error);
      setHeartCooldown(false);
    }
  };

  const handleSendMessage = async () => {
    if (!comment.trim() || !streamId) return;
    
    try {
      await sendMessage({
        streamId: streamId as Id<"streams">,
        text: comment.trim(),
      });
      setComment("");
    } catch (error) {
      Alert.alert("Error", "Failed to send message");
    }
  };

  const handleStartShow = async () => {
    if (!streamId) return;
    
    try {
      await goLive({ streamId: streamId as Id<"streams"> });
      Alert.alert("Success", "Stream started!");
    } catch (error) {
      Alert.alert("Error", "Failed to start stream");
    }
  };

  const shareStream = async () => {
    try {
      await Share.share({
        message: `Watch ${stream?.streamer?.username || 'this'} live on Liveciety! ${stream?.title}`,
        url: `https://liveciety.com/stream/${streamId}`,
      });
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  const handleStopStream = async () => {
    if (!streamId) return;
    
    try {
      await endStream({ streamId: streamId as Id<"streams"> });
      Alert.alert("Success", "Stream ended! Chat will remain active.");
    } catch (error) {
      Alert.alert("Error", "Failed to end stream");
      console.error("Failed to end stream:", error);
    }
  };

  const handleRestartStream = async () => {
    if (!streamId) return;
    
    try {
      await goLive({ streamId: streamId as Id<"streams"> });
      setHostWasDisconnected(false);
      setShowRestartOverlay(false);
      Alert.alert("Success", "Stream restarted!");
    } catch (error) {
      Alert.alert("Error", "Failed to restart stream");
    }
  };

  const handleMorePress = () => {
    setShowControlsSheet(true);
  };

  const handleManageProducts = () => {
    setShowProductSelection(true);
  };

  const handleProductPress = (product: any) => {
    // Handle product press - could navigate to product details or start purchase flow
    Alert.alert(
      product.name,
      `Price: $${product.price?.toFixed(2) || "0.00"}\n\nWould you like to view more details?`,
      [
        { text: "Cancel", style: "cancel" },
        { text: "View Details", onPress: () => console.log("Navigate to product details") },
      ]
    );
  };

  const handleRotateCamera = async () => {
    try {
      const localParticipant = localParticipantRef.current;
      if (!localParticipant) {
        Alert.alert("Error", "Not connected to stream yet");
        return;
      }

      // For now, we'll restart the camera as a fallback since direct camera switching
      // in LiveKit React Native requires complex device enumeration and can be unstable
      if (typeof localParticipant.setCameraEnabled === 'function') {
        Alert.alert(
          "Restart Camera", 
          "Camera switching will restart your camera. Continue?",
          [
            { text: "Cancel", style: "cancel" },
            { text: "Restart", onPress: async () => {
              try {
                await localParticipant.setCameraEnabled(false);
                setTimeout(async () => {
                  try {
                    await localParticipant.setCameraEnabled(true);
                    Alert.alert("Camera Restarted", "Your camera has been restarted");
                  } catch (err) {
                    console.error("Error re-enabling camera:", err);
                    Alert.alert("Error", "Failed to restart camera");
                  }
                }, 500);
              } catch (error) {
                console.error("Error disabling camera:", error);
                Alert.alert("Error", "Failed to restart camera");
              }
            }}
          ]
        );
      } else {
        Alert.alert("Error", "Camera controls not available in this mode");
      }
    } catch (error) {
      console.error("Camera rotation error:", error);
      Alert.alert("Error", "Camera function not available");
    }
  };

  const handleToggleMic = async () => {
    try {
      const localParticipant = localParticipantRef.current;
      if (localParticipant && typeof localParticipant.setMicrophoneEnabled === 'function') {
        const newMicState = !isMicEnabled;
        await localParticipant.setMicrophoneEnabled(newMicState);
        setIsMicEnabled(newMicState);
        Alert.alert(
          newMicState ? "Microphone Enabled" : "Microphone Disabled",
          newMicState ? "Your microphone is now on" : "Your microphone is now muted"
        );
      } else {
        Alert.alert("Info", "Microphone control not available");
      }
    } catch (error) {
      Alert.alert("Error", "Failed to toggle microphone");
      console.error("Microphone toggle error:", error);
    }
  };

  useEffect(() => {
    return () => {
      if (timeout.current) clearTimeout(timeout.current);
    };
  }, []);

  // Video rendering using LiveKitRoom and VideoTrack
  const VideoArea = useCallback(() => {
    if (!livekitToken || !LIVEKIT_WS_URL || !stream?.roomName) {
      return (
        <View style={{ 
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: '#000', 
          alignItems: 'center', 
          justifyContent: 'center' 
        }}>
          {connecting ? (
            <Text style={{ color: '#fff' }}>Connecting to stream...</Text>
          ) : livekitError ? (
            <Text style={{ color: '#fff' }}>Connection error</Text>
          ) : (
            <Text style={{ color: '#fff' }}>Waiting for stream...</Text>
          )}
        </View>
      );
    }
    return (
      <View style={{ 
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}>
        <LiveKitRoom
          serverUrl={LIVEKIT_WS_URL}
          token={livekitToken}
          connect={true}
          audio={true}
          video={true}
          options={{ adaptiveStream: { pixelDensity: 'screen' } }}
        >
          <RoomView />
        </LiveKitRoom>
      </View>
    );
  }, [livekitToken, LIVEKIT_WS_URL, stream?.roomName, connecting, livekitError]);

  // Host Away/Waiting Screen Component
  const HostWaitingScreen = () => {
    const backgroundSource = stream?.thumbnail 
      ? { uri: stream.thumbnail }
      : stream?.streamer?.image 
        ? { uri: getImageUrl(stream.streamer.image) }
        : require('../../../assets/adaptive-icon.png');

    return (
      <View style={styles.waitingContainer}>
        <ImageBackground
          source={backgroundSource}
          style={styles.waitingBackground}
          blurRadius={8}
        >
          <View style={styles.waitingOverlay}>
            <View style={styles.waitingContent}>
              <View style={styles.hostAvatarLarge}>
                <Image
                  source={stream?.streamer?.image ? { uri: getImageUrl(stream.streamer.image) } : require('../../../assets/adaptive-icon.png')}
                  style={styles.hostAvatarLargeImage}
                />
              </View>
              <Text style={styles.waitingTitle}>Host will be right back</Text>
              <Text style={styles.waitingSubtitle}>
                {stream?.streamer?.username || stream?.streamer?.name || "The host"} stepped away briefly
              </Text>
              <View style={styles.waitingIndicator}>
                <View style={styles.dot} />
                <View style={styles.dot} />
                <View style={styles.dot} />
              </View>
            </View>
          </View>
        </ImageBackground>
      </View>
    );
  };

  // Host Connection Tracker Component
  const HostConnectionTracker = () => {
    const participants = useParticipants();
    const roomInfo = useRoomInfo();

    useEffect(() => {
      if (!stream) return;
      
      // Find host participant by matching user ID or identity
      const hostParticipant = participants.find(p => 
        p.identity === stream.hostId || 
        p.identity === stream.streamer?.username ||
        p.metadata?.includes(stream.hostId)
      );

      const isHostPresent = !!hostParticipant;
      const previouslyConnected = hostConnected;
      
      setHostConnected(isHostPresent);
      
      // Track if host was disconnected from a live stream
      if (stream.status === "live" && !isHostPresent && previouslyConnected) {
        setHostWasDisconnected(true);
      }
      
      // Show waiting screen if host is not connected and stream is live
      if (stream.status === "live" && !isHostPresent) {
        setShowWaitingScreen(true);
        setShowRestartOverlay(false);
      } else {
        setShowWaitingScreen(false);
        
        // Show restart overlay if host comes back after being disconnected from live stream
        if (isHost && hostWasDisconnected && isHostPresent && stream.status === "live") {
          setShowRestartOverlay(true);
        } else {
          setShowRestartOverlay(false);
        }
      }
    }, [participants, stream, hostConnected, hostWasDisconnected, isHost]);

    return null; // This component only tracks, doesn't render
  };

  // Store local participant reference
  const localParticipantRef = useRef<any>(null);

  // RoomView for rendering video tracks
  const RoomView = () => {
    const tracks = useTracks([Track.Source.Camera]);
    const { localParticipant } = useLocalParticipant();
    
    // Store local participant reference for external access
    React.useEffect(() => {
      localParticipantRef.current = localParticipant;
    }, [localParticipant]);

    // Monitor microphone and camera state changes
    React.useEffect(() => {
      if (localParticipant) {
        // Initialize states based on current track states
        const audioTracks = localParticipant.audioTrackPublications;
        const videoTracks = localParticipant.videoTrackPublications;
        
        // Check initial microphone state
        if (audioTracks && audioTracks.size > 0) {
          const audioTrackArray = Array.from(audioTracks.values());
          const audioTrack = audioTrackArray[0];
          if (audioTrack && typeof audioTrack.isMuted !== 'undefined') {
            setIsMicEnabled(!audioTrack.isMuted);
          }
        }
        
        // Check initial camera state
        if (videoTracks && videoTracks.size > 0) {
          const videoTrackArray = Array.from(videoTracks.values());
          const videoTrack = videoTrackArray[0];
          if (videoTrack && typeof videoTrack.isMuted !== 'undefined') {
            setIsCameraEnabled(!videoTrack.isMuted);
          }
        }

        // Listen for track muted/unmuted events
        const handleTrackMuted = (publication: any) => {
          if (publication.source === Track.Source.Microphone) {
            setIsMicEnabled(false);
          } else if (publication.source === Track.Source.Camera) {
            setIsCameraEnabled(false);
          }
        };
        
        const handleTrackUnmuted = (publication: any) => {
          if (publication.source === Track.Source.Microphone) {
            setIsMicEnabled(true);
          } else if (publication.source === Track.Source.Camera) {
            setIsCameraEnabled(true);
          }
        };

        // Add event listeners if they exist
        if (typeof localParticipant.on === 'function') {
          localParticipant.on('trackMuted', handleTrackMuted);
          localParticipant.on('trackUnmuted', handleTrackUnmuted);

          return () => {
            if (typeof localParticipant.off === 'function') {
              localParticipant.off('trackMuted', handleTrackMuted);
              localParticipant.off('trackUnmuted', handleTrackUnmuted);
            }
          };
        }
      }
    }, [localParticipant]);
    
    return (
      <View style={{ flex: 1 }}>
        <HostConnectionTracker />
        
        {showWaitingScreen ? (
          <HostWaitingScreen />
        ) : (
          <>
            {tracks.map((trackRef) =>
              isTrackReference(trackRef) ? (
                <VideoTrack 
                  key={trackRef.sid} 
                  trackRef={trackRef} 
                  style={{ 
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                  }} 
                />
              ) : null
            )}
          </>
        )}
      </View>
    );
  };

  // Loading states
  if (!streamId) {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.errorText}>No stream ID provided.</Text>
      </SafeAreaView>
    );
  }

  if (stream === undefined) {
    return <StreamSkeleton />;
  }

  if (!stream) {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.errorText}>Stream not found.</Text>
      </SafeAreaView>
    );
  }

  const statusBar = () => {
    return (
      <StatusBar
        translucent
        backgroundColor={Colors.transparent}
        barStyle={"light-content"}
      />
    );
  };

  // Host Ready to Start Overlay - positioned at top, doesn't block chat
  const hostReadyOverlay = () => {
    if (!isHost || stream.status === "live" || stream.status === "ended") return null;

    const scheduledTime = stream.scheduledTime ? new Date(stream.scheduledTime) : new Date();
    const timeString = scheduledTime.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });

    return (
      <View style={styles.hostOverlay} pointerEvents="box-none">
        <View style={styles.hostOverlayContent}>
          <Text style={styles.showStartsText}>Show Starts at {timeString}</Text>
          <Text style={styles.readyToStartText}>Ready to Start?</Text>
          
          <TouchableOpacity style={styles.shareButton} onPress={shareStream}>
            <Feather name="share" size={18} color={Colors.white} />
            <Text style={styles.shareButtonText}>Share Show</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Host Restart Overlay - shows when host comes back after disconnecting
  const hostRestartOverlay = () => {
    if (!showRestartOverlay) return null;

    return (
      <View style={styles.hostOverlay} pointerEvents="box-none">
        <View style={styles.hostOverlayContent}>
          <Ionicons name="wifi-outline" size={32} color={Colors.white} style={{ marginBottom: Default.fixPadding }} />
          <Text style={styles.readyToStartText}>Welcome Back!</Text>
          <Text style={styles.showStartsText}>You were disconnected from the stream</Text>
          
          <View style={styles.restartButtonContainer}>
            <TouchableOpacity style={styles.restartButton} onPress={handleRestartStream}>
              <Ionicons name="play" size={18} color={Colors.black} />
              <Text style={styles.restartButtonText}>Restart Stream</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.dismissButton} onPress={() => setShowRestartOverlay(false)}>
              <Text style={styles.dismissButtonText}>Dismiss</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  const detailAndClose = () => {
    // Use the real viewer count from enhanced tracking system
    const displayViewerCount = stream?.status === "live" ? viewerCount : 0;
    
    return (
      <View>
        <SafeAreaView />
        <View
          style={{
            marginTop: (StatusBar.currentHeight || 0) + Default.fixPadding * 2,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          {/* Header with user info and close button */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: Default.fixPadding,
            }}
          >
            <View
              style={{
                flex: 1,
                flexDirection: "row",
                alignItems: "center",
              }}
            >
              <View
                style={{
                  borderTopLeftRadius: 10,
                  borderTopRightRadius: 10,
                  borderBottomRightRadius: 10,
                  ...styles.userBorderStyle,
                }}
              >
                <Image
                  source={stream.streamer?.image ? { uri: getImageUrl(stream.streamer.image) } : require('../../../assets/adaptive-icon.png')}
                  style={{
                    borderTopLeftRadius: 10,
                    borderTopRightRadius: 10,
                    borderBottomRightRadius: 10,
                    ...styles.imageStyle,
                  }}
                />
              </View>
              <View
                style={{
                  flex: 1,
                  alignItems: "flex-start",
                  marginHorizontal: Default.fixPadding,
                }}
              >
                <Text numberOfLines={1} style={Fonts.SemiBold16white}>
                  {stream.streamer?.username || stream.streamer?.name || "Unknown Host"}
                </Text>
                <Text numberOfLines={1} style={Fonts.SemiBold14primary}>
                  {stream.status === "live" ? "LIVE" : stream.status === "ended" ? "ENDED" : "SCHEDULED"}
                </Text>
                <Text numberOfLines={1} style={Fonts.Regular14lightGrey}>
                  {displayViewerCount} {displayViewerCount === 1 ? 'viewer' : 'viewers'}
                  {peak > 0 && peak > displayViewerCount && (
                    <Text style={{ fontSize: 12 }}> • Peak: {peak}</Text>
                  )}
                  {isTracking && stream?.status === "live" && (
                    <Text style={{ color: Colors.primary }}> • Live</Text>
                  )}
                </Text>
              </View>
            </View>
            <TouchableOpacity onPress={() => router.back()}>
              <Ionicons
                name="close-sharp"
                size={24}
                color={Colors.white}
              />
            </TouchableOpacity>
          </View>

          {/* Start Show Button for Host (when not live and not ended) */}
          {isHost && stream.status !== "live" && stream.status !== "ended" && (
            <TouchableOpacity style={styles.topStartShowButton} onPress={handleStartShow}>
              <Text style={styles.topStartShowText}>Start Show</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  interface MessageItem {
    _id: string;
    text: string;
    userName: string;
    user?: {
      username: string;
      image?: string;
      color?: string;
    };
  }

  const renderItemComment = ({ item }: { item: MessageItem }) => {
    const displayName = item.user?.username || item.userName || "Anonymous";
    const userImage = item.user?.image ? { uri: getImageUrl(item.user.image) } : require('../../../assets/adaptive-icon.png');
    
    return (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: Default.fixPadding * 2,
          marginHorizontal: Default.fixPadding * 2.8,
        }}
      >
        <View
          style={{
            borderTopLeftRadius: 10,
            borderTopRightRadius: 10,
            borderBottomRightRadius: 10,
            ...styles.userBorderStyle,
          }}
        >
          <Image
            source={userImage}
            style={{
              borderTopLeftRadius: 10,
              borderTopRightRadius: 10,
              borderBottomRightRadius: 10,
              ...styles.imageStyle,
            }}
          />
        </View>
        <View
          style={{
            flex: 1,
            alignItems: "flex-start",
            marginHorizontal: Default.fixPadding,
          }}
        >
          <Text numberOfLines={1} style={Fonts.Medium16white}>
            {displayName}
          </Text>
          <Text
            numberOfLines={2}
            style={{
              textAlign: "left",
              ...Fonts.Regular14lightGrey,
            }}
          >
            {item.text}
          </Text>
        </View>
      </View>
    );
  };

  // Side Action Menu (like video screen)
  const sideActionMenu = () => {
    return (
      <View style={styles.sideActionContainer}>
                 <TouchableOpacity
           onPress={handleHeartPress}
           style={[
             styles.commonTouchableStyle,
             heartCooldown && { opacity: 0.5 }
           ]}
           disabled={heartCooldown}
         >
           <MaterialCommunityIcons
             name={liked ? "cards-heart" : "cards-heart-outline"}
             size={20}
             color={heartCooldown ? Colors.lightGrey : (liked ? Colors.red : Colors.white)}
           />
         </TouchableOpacity>

        <TouchableOpacity
          onPress={shareStream}
          style={styles.commonTouchableStyle}
        >
          <Ionicons
            name={"arrow-redo-outline"}
            size={20}
            color={Colors.white}
          />
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleMorePress}
          style={styles.commonTouchableStyle}
        >
          <Ionicons
            name={"ellipsis-horizontal"}
            size={20}
            color={Colors.white}
          />
        </TouchableOpacity>

        {/* Animated Hearts */}
        <View style={{ justifyContent: "flex-end", marginTop: 20 }}>
          {hearts.map(({ id, userId, color }) => (
            <AnimatedHeart
              key={id}
              id={id}
              userId={userId}
              color={color}
              onCompleteAnimation={handleCompleteAnimation}
            />
          ))}
        </View>
      </View>
    );
  };

  const commentAndList = () => {
    return (
      <View
        style={{
          flexDirection: "row",
          height: height / 2.9,
        }}
      >
        <View style={{ flex: 1 }}>
          <FlatList
            inverted={true}
            data={(messages || []).filter(msg => msg.type !== "heart")} // Filter out heart messages
            renderItem={renderItemComment}
            keyExtractor={(item) => item._id}
            showsVerticalScrollIndicator={false}
          />
        </View>
        {sideActionMenu()}
      </View>
    );
  };

  const commentAndLike = () => {
    return (
      <View
        style={{
          flexDirection: "row",
          ...styles.commentAndLikeViewStyle,
        }}
      >
        <View
          style={{
            flexDirection: "row",
            marginRight: Default.fixPadding,
            ...styles.commentViewStyle,
          }}
        >
          <TextInput
            value={comment}
            onChangeText={setComment}
            placeholder="Add a comment..."
            placeholderTextColor={Colors.lightGrey}
            selectionColor={Colors.primary}
            style={{
              textAlign: "left",
              marginRight: Default.fixPadding,
              ...styles.textInputStyle,
            }}
          />
          <TouchableOpacity 
            disabled={!comment.trim()}
            onPress={handleSendMessage}
          >
            <Ionicons
              name="send"
              size={20}
              color={comment.trim() ? Colors.white : Colors.lightGrey}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const imageBackground = () => {
    const showLiveVideo = stream?.status === "live" && (livekitToken || connecting);
    
    return (
      <View style={{ flex: 1 }}>
        {/* Background - either live video or static image */}
        {showLiveVideo ? (
          <VideoArea />
        ) : (
          <ImageBackground
            style={{ 
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            }}
            source={getBackgroundImage()}
            blurRadius={2}
          />
        )}
        
        {/* Dark overlay for readability */}
        <View style={styles.overlay} />
        
        {/* Stream Ended Indicator */}
        {stream?.status === "ended" && (
          <View style={styles.streamEndedOverlay}>
            <View style={styles.streamEndedBadge}>
              <Ionicons name="stop-circle" size={24} color={Colors.white} />
              <Text style={styles.streamEndedText}>Stream Ended</Text>
            </View>
          </View>
        )}
        
        {/* Host Ready Overlay */}
        {hostReadyOverlay()}
        
        {/* Host Restart Overlay */}
        {hostRestartOverlay()}
        
        {/* UI Content */}
        <View style={{ flex: 1, justifyContent: "space-between" }}>
          {detailAndClose()}
          <View>
            {commentAndList()}
            {commentAndLike()}
          </View>
        </View>
        
        {/* Pinned Products Display */}
        <PinnedProductsDisplay
          streamId={streamId}
          onProductPress={handleProductPress}
          style={{ bottom: 100 }}
        />
      </View>
    );
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={{ flex: 1 }}>
        {statusBar()}
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "height" : undefined}
          style={{ flex: 1 }}
        >
          {imageBackground()}
        </KeyboardAvoidingView>
      </View>
      
      <StreamControlsSheet
        isOpen={showControlsSheet}
        onClose={() => setShowControlsSheet(false)}
        onStopStream={handleStopStream}
        onShareStream={shareStream}
        onRotateCamera={handleRotateCamera}
        onToggleMic={handleToggleMic}
        onManageProducts={handleManageProducts}
        isHost={isHost}
        streamStatus={stream?.status || ""}
        isMicEnabled={isMicEnabled}
      />
      
      <ProductSelectionSheet
        isOpen={showProductSelection}
        onClose={() => setShowProductSelection(false)}
        streamId={streamId}
      />
      
      {/* Debug info in development */}
      {__DEV__ && viewerError && (
        <View style={{
          position: "absolute" as const,
          bottom: 100,
          right: Default.fixPadding * 2,
          backgroundColor: "rgba(239, 68, 68, 0.9)",
          padding: Default.fixPadding,
          borderRadius: 8,
          maxWidth: 280,
          zIndex: 1000,
        }}>
          <Text style={{
            color: Colors.white,
            fontSize: 12,
            fontWeight: "500" as const,
          }}>
            Viewer Count Error: {viewerError}
          </Text>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  },
  hostOverlay: {
    position: "absolute",
    top: "30%",
    left: 0,
    right: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 2,
    pointerEvents: "box-none",
  },
  hostOverlayContent: {
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    borderRadius: 40,
    padding: Default.fixPadding * 2.5,
    alignItems: "center",
    marginHorizontal: Default.fixPadding * 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
    width: "90%",
  },
  showStartsText: {
    ...Fonts.Regular14lightGrey,
    marginBottom: Default.fixPadding,
  },
  readyToStartText: {
    ...Fonts.Bold24white,
    marginBottom: Default.fixPadding * 2,
    textAlign: "center",
  },
  shareButton: {
    backgroundColor: Colors.yellow,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Default.fixPadding * 3,
    paddingVertical: Default.fixPadding * 1.5,
    borderRadius: 25,
  },
  shareButtonText: {
    ...Fonts.SemiBold16white,
    color: Colors.black,
    marginLeft: Default.fixPadding,
  },
  userBorderStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: 50,
    height: 50,
    borderWidth: 1,
    borderColor: Colors.white,
  },
  imageStyle: {
    width: 42,
    height: 42,
  },
  sideActionContainer: {
    alignItems: "center",
    justifyContent: "flex-end",
    paddingBottom: Default.fixPadding * 2,
    paddingRight: Default.fixPadding,
  },
  commonTouchableStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.transparent,
    marginBottom: Default.fixPadding * 1.5,
  },
  rightImageStyle: {
    width: 35,
    height: 35,
  },
  rightImageViewStyle: {
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.white,
    width: 40,
    height: 40,
  },
  commentViewStyle: {
    flex: 1,
    alignItems: "center",
    paddingVertical: Default.fixPadding * 1.2,
    paddingHorizontal: Default.fixPadding * 1.4,
    backgroundColor: Colors.transparent,
    borderRadius: 30,
    borderWidth: 1,
    borderColor: "rgba(148, 148, 148, 0.5)",
  },
  commentAndLikeViewStyle: {
    alignItems: "center",
    marginBottom: Default.fixPadding * 1.6,
    marginTop: Default.fixPadding * 0.5,
    marginHorizontal: Default.fixPadding * 2,
  },
  textInputStyle: {
    flex: 1,
    ...Fonts.Regular14white,
  },
  heartIcon: {
    position: "absolute",
    resizeMode: "contain",
    width: 25,
    height: 25,
  },
  topStartShowButton: {
    backgroundColor: Colors.yellow,
    paddingVertical: Default.fixPadding * 1.2,
    paddingHorizontal: Default.fixPadding * 2,
    borderRadius: 20,
    alignItems: "center",
    marginTop: Default.fixPadding,
  },
  topStartShowText: {
    ...Fonts.SemiBold16white,
    color: Colors.black,
  },
  errorText: {
    color: "#fff",
    fontSize: 18,
    textAlign: "center",
    marginTop: 40,
  },
  // Waiting Screen Styles
  waitingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  waitingBackground: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  waitingOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    justifyContent: "center",
    alignItems: "center",
  },
  waitingContent: {
    alignItems: "center",
    paddingHorizontal: Default.fixPadding * 3,
  },
  hostAvatarLarge: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: Colors.white,
    marginBottom: Default.fixPadding * 3,
    overflow: "hidden",
  },
  hostAvatarLargeImage: {
    width: "100%",
    height: "100%",
    borderRadius: 56,
  },
  waitingTitle: {
    ...Fonts.Bold24white,
    fontSize: 28,
    textAlign: "center",
    marginBottom: Default.fixPadding,
  },
  waitingSubtitle: {
    ...Fonts.Regular14lightGrey,
    fontSize: 16,
    textAlign: "center",
    marginBottom: Default.fixPadding * 3,
  },
  waitingIndicator: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.primary,
    marginHorizontal: 4,
    opacity: 0.7,
  },
  // Restart Overlay Styles
  restartButtonContainer: {
    flexDirection: "column",
    alignItems: "center",
    marginTop: Default.fixPadding * 2,
    width: "100%",
  },
  restartButton: {
    backgroundColor: Colors.primary,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Default.fixPadding * 3,
    paddingVertical: Default.fixPadding * 1.5,
    borderRadius: 25,
    marginBottom: Default.fixPadding,
  },
  restartButtonText: {
    ...Fonts.SemiBold16white,
    color: Colors.black,
    marginLeft: Default.fixPadding,
  },
  dismissButton: {
    paddingHorizontal: Default.fixPadding * 2,
    paddingVertical: Default.fixPadding,
  },
  dismissButtonText: {
    ...Fonts.Regular14lightGrey,
    textDecorationLine: "underline",
  },
  // Stream Ended Styles
  streamEndedOverlay: {
    position: "absolute",
    top: "20%",
    left: 0,
    right: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1,
    pointerEvents: "none",
  },
  streamEndedBadge: {
    backgroundColor: "rgba(220, 38, 38, 0.9)", // Red background with transparency
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Default.fixPadding * 2,
    paddingVertical: Default.fixPadding,
    borderRadius: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  streamEndedText: {
    ...Fonts.SemiBold16white,
    marginLeft: Default.fixPadding,
  },
});

export default StreamScreen; 