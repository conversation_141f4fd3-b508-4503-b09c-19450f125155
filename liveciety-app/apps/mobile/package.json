{"name": "@workspace/mobile", "main": "expo-router/entry", "version": "1.0.10", "scripts": {"start": "expo start", "dev": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "doctor": "expo doctor"}, "jest": {"preset": "jest-expo"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["@livekit/react-native", "@livekit/react-native-webrtc"], "listUnknownPackages": false}}, "ios": {"jsEngine": "hermes", "enableHermes": true, "hermesBuildFlags": ["-O", "-<PERSON><PERSON>"]}}, "dependencies": {"@auth/core": "0.37.4", "@babel/runtime": "^7.27.1", "@config-plugins/react-native-webrtc": "^10.0.0", "@convex-dev/aggregate": "^0.1.21", "@convex-dev/auth": "^0.0.87", "@convex-dev/migrations": "^0.2.8", "@convex-dev/polar": "^0.4.5", "@convex-dev/react-query": "0.0.0-alpha.8", "@convex-dev/twilio": "^0.1.7", "@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.1.2", "@hookform/resolvers": "^5.0.1", "@livekit/react-native": "^2.7.4", "@livekit/react-native-expo-plugin": "^1.0.1", "@livekit/react-native-webrtc": "^125.0.9", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@react-email/components": "^0.0.36", "@react-email/render": "^1.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/elements": "^2.4.3", "@react-navigation/native": "^7.0.14", "@rn-primitives/accordion": "^1.1.0", "@rn-primitives/alert-dialog": "^1.1.0", "@rn-primitives/aspect-ratio": "^1.1.0", "@rn-primitives/avatar": "^1.1.0", "@rn-primitives/checkbox": "^1.1.0", "@rn-primitives/collapsible": "^1.1.0", "@rn-primitives/context-menu": "^1.1.0", "@rn-primitives/dialog": "^1.1.0", "@rn-primitives/dropdown-menu": "^1.1.0", "@rn-primitives/hover-card": "^1.1.0", "@rn-primitives/label": "^1.1.0", "@rn-primitives/menubar": "^1.1.0", "@rn-primitives/navigation-menu": "^1.1.0", "@rn-primitives/popover": "^1.1.0", "@rn-primitives/progress": "^1.1.0", "@rn-primitives/radio-group": "^1.1.0", "@rn-primitives/select": "^1.1.0", "@rn-primitives/separator": "^1.1.0", "@rn-primitives/slot": "^1.1.0", "@rn-primitives/switch": "^1.1.0", "@rn-primitives/table": "^1.1.0", "@rn-primitives/tabs": "^1.1.0", "@rn-primitives/toggle": "^1.1.0", "@rn-primitives/toggle-group": "^1.1.0", "@rn-primitives/tooltip": "^1.1.0", "@rn-primitives/types": "^1.1.0", "@stream-io/node-sdk": "^0.4.24", "@stripe/stripe-react-native": "^0.48.0", "@types/bcryptjs": "^3.0.0", "bcryptjs": "^3.0.2", "chrono-node": "2.7.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.5", "convex-helpers": "^0.1.89", "crypto": "^1.0.1", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "expo": "~53.0.12", "expo-blur": "~14.1.5", "expo-build-properties": "^0.14.6", "expo-camera": "~16.1.8", "expo-constants": "~17.1.5", "expo-dev-client": "~5.2.1", "expo-font": "~13.3.1", "expo-haptics": "~14.1.0", "expo-image": "~2.3.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.0", "expo-router": "~5.1.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-tracking-transparency": "~5.2.0", "expo-updates": "~0.28.15", "expo-web-browser": "~14.2.0", "jszip": "^3.10.1", "livekit-client": "^2.13.1", "livekit-server-sdk": "^2.13.0", "lodash": "^4.17.21", "loops": "^5.0.1", "nativewind": "^4.1.23", "openai": "^4.104.0", "oslo": "^1.2.1", "react": "19.0.0", "react-async-hook": "^4.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.1", "react-native": "0.79.4", "react-native-feather": "^1.1.2", "react-native-floating-action": "^1.22.0", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-svg-transformer": "^1.5.0", "react-native-tab-view": "^4.0.10", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "standardwebhooks": "^1.0.0", "stream-browserify": "^3.0.0", "stripe": "^18.1.0", "tailwind-merge": "^3.2.0", "twilio": "^5.5.2", "zod": "^3.25.42"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/istanbul-lib-coverage": "^2.0.6", "@types/istanbul-lib-report": "^3.0.3", "@types/jest": "^29.5.12", "@types/json-schema": "^7.0.15", "@types/lodash": "^4.17.16", "@types/node": "^22.15.17", "@types/prop-types": "^15.7.14", "@types/react": "~19.0.10", "@types/react-navigation": "^3.0.8", "@types/react-test-renderer": "^19.0.0", "@types/stack-utils": "^2.0.3", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~53.0.7", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "private": true}