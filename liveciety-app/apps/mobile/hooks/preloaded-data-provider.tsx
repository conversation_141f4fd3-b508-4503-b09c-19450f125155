import React, { useEffect, useState } from "react";
import { ActivityIndicator } from "react-native";
import { PreloadedDataContext } from "./use-preloaded-data";
import { useUser } from "./use-user";

export function PreloadedDataProvider({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useUser();
  const [preloaded, setPreloaded] = useState<any>(null);

  useEffect(() => {
    if (!isLoading && user) {
      setPreloaded({ user });
    }
  }, [user, isLoading]);

  if (isLoading || !preloaded) {
    return <ActivityIndicator size="large" color="#fff" />;
  }

  return (
    <PreloadedDataContext.Provider value={preloaded}>
      {children}
    </PreloadedDataContext.Provider>
  );
} 