{"expo": {"name": "Liveciety", "slug": "mobile-liveciety", "version": "1.0.12", "orientation": "portrait", "icon": "./assets/icon.png", "scheme": "liveciety", "userInterfaceStyle": "automatic", "newArchEnabled": true, "jsEngine": "hermes", "ios": {"supportsTablet": true, "bundleIdentifier": "com.liveciety.app", "buildNumber": "12", "infoPlist": {"NSCameraUsageDescription": "We need camera access to let you live stream and broadcast to your audience.", "NSMicrophoneUsageDescription": "We need microphone access to let you live stream with audio and broadcast to your audience.", "NSPhotoLibraryUsageDescription": "We need photo library access to let you select and share existing photos in your chat conversations.", "NSPhotoLibraryAddUsageDescription": "We need photo library access to save photos shared in your conversations to your device.", "NSUserTrackingUsageDescription": "We use this data to provide you with a better and more personalized experience, and to help our app grow and improve.", "ITSAppUsesNonExemptEncryption": false}, "associatedDomains": ["applinks:decisive-perch-342.convex.site"]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.liveciety.app", "permissions": ["com.google.android.gms.permission.AD_ID"], "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "decisive-perch-342.convex.site", "pathPrefix": "/api/auth/mobile/callback/google"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#18181B"}], "expo-tracking-transparency", "expo-updates", "expo-secure-store", "@livekit/react-native-expo-plugin", "@config-plugins/react-native-webrtc", "expo-web-browser", ["@stripe/stripe-react-native", {"merchantIdentifier": "merchant.com.liveciety.app", "enableGooglePay": true}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "7bcea97d-6ff5-4f1e-9a97-3f3150166491"}}, "owner": "liveciety", "runtimeVersion": "1.0.10", "updates": {"enabled": false}}}