import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { CustomBottomSheet } from "./bottom-sheet";
import { Id } from "../../convex/_generated/dataModel";

interface SafetyGuidelinesSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onAgree: () => void;
  participantId?: Id<"users">;
}

export const SafetyGuidelinesSheet: React.FC<SafetyGuidelinesSheetProps> = ({
  isOpen,
  onClose,
  onAgree,
  participantId,
}) => {
  const handleAgree = () => {
    onAgree();
    onClose();
  };

  return (
    <CustomBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      snapPoints={["60%"]}
      enableDynamicHeight={false}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Safety & Guidelines</Text>
        </View>
        <Text style={styles.subtitle}>
          Before you get started, please review our messaging rules and
          guidelines:
        </Text>
        <View style={styles.guidelinesList}>
          <View style={styles.guidelineItem}>
            <View style={styles.checkmark}>
              <Text style={styles.checkmarkText}>✓</Text>
            </View>
            <Text style={styles.guidelineText}>
              Chats will be recorded for everyone's safety and continued
              improvement.
            </Text>
          </View>
          <View style={styles.guidelineItem}>
            <View style={styles.checkmark}>
              <Text style={styles.checkmarkText}>✓</Text>
            </View>
            <Text style={styles.guidelineText}>
              We have no tolerance for using messaging to solicit sales outside
              of the App.
            </Text>
          </View>
          <View style={styles.guidelineItem}>
            <View style={styles.checkmark}>
              <Text style={styles.checkmarkText}>✓</Text>
            </View>
            <Text style={styles.guidelineText}>
              We have no tolerance for inappropriate comments, bullying, etc.
            </Text>
          </View>
          <View style={styles.guidelineItem}>
            <View style={styles.checkmark}>
              <Text style={styles.checkmarkText}>✓</Text>
            </View>
            <Text style={styles.guidelineText}>
              You understand our{" "}
              <Text style={styles.link}>community guidelines</Text> and{" "}
              <Text style={styles.link}>terms</Text>.
            </Text>
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.goBackButton} onPress={onClose}>
            <Text style={styles.goBackText}>Go Back</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.agreeButton}
            onPress={handleAgree}
            disabled={!participantId}
          >
            <Text style={styles.agreeText}>I Agree</Text>
          </TouchableOpacity>
        </View>
      </View>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#18181B",
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: "#fff",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#A1A1AA",
    marginBottom: 24,
  },
  guidelinesList: {
    gap: 20,
  },
  guidelineItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
  },
  checkmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#22C55E",
    alignItems: "center",
    justifyContent: "center",
  },
  checkmarkText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  guidelineText: {
    flex: 1,
    fontSize: 16,
    color: "#fff",
    lineHeight: 24,
  },
  link: {
    color: "#3B82F6",
    textDecorationLine: "underline",
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 12,
    marginTop: "auto",
    paddingTop: 24,
  },
  goBackButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 8,
    backgroundColor: "#27272A",
    alignItems: "center",
  },
  goBackText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  agreeButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 8,
    backgroundColor: "#EAB308",
    alignItems: "center",
  },
  agreeText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
});
