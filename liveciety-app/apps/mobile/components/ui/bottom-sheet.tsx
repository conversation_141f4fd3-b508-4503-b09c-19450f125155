import React, { useCallback, useMemo, useRef } from "react";
import { View, StyleSheet, Text } from "react-native";
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetView,
  BottomSheetProps as GorhomBottomSheetProps,
} from "@gorhom/bottom-sheet";
import { Portal } from "./portal";

interface BottomSheetProps extends Partial<GorhomBottomSheetProps> {
  children: React.ReactNode;
  isOpen?: boolean;
  onClose?: () => void;
  enableDynamicHeight?: boolean;
  initialSnapPoint?: string | number;
  snapPoints?: (string | number)[];
}

export const CustomBottomSheet: React.FC<BottomSheetProps> = React.memo(
  ({
    children,
    isOpen = false,
    onClose,
    enableDynamicHeight = false,
    initialSnapPoint = "50%",
    snapPoints: propSnapPoints,
    ...props
  }) => {
    const bottomSheetRef = useRef<BottomSheet>(null);

    const snapPoints = useMemo(() => {
      // If no snapPoints provided, only allow 15% height
      return propSnapPoints || ["50%"];
    }, [propSnapPoints]);

    const renderBackdrop = useCallback(
      (props: any) => (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          pressBehavior="close"
        />
      ),
      [],
    );

    const handleSheetChanges = useCallback(
      (index: number) => {
        if (index === -1 && onClose) {
          onClose();
        }
      },
      [onClose],
    );

    const bottomSheetContent = useMemo(
      () => (
        <BottomSheet
          ref={bottomSheetRef}
          index={0}
          snapPoints={snapPoints}
          enablePanDownToClose
          backdropComponent={renderBackdrop}
          onChange={handleSheetChanges}
          enableDynamicSizing={enableDynamicHeight}
          backgroundStyle={styles.sheetBackground}
          handleIndicatorStyle={styles.handleIndicator}
          {...props}
        >
          <BottomSheetView style={styles.contentContainer}>
            {children}
          </BottomSheetView>
        </BottomSheet>
      ),
      [
        snapPoints,
        renderBackdrop,
        handleSheetChanges,
        enableDynamicHeight,
        props,
        children,
      ],
    );

    if (!isOpen) return null;

    return (
      <Portal>
        <View style={StyleSheet.absoluteFill} pointerEvents="box-none">
          {bottomSheetContent}
        </View>
      </Portal>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  sheetBackground: {
    backgroundColor: "#18181B",
  },
  handleIndicator: {
    backgroundColor: "#666666",
  },
});
