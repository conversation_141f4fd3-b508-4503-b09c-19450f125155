import React, { useMemo, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  useWindowDimensions,
  TouchableOpacity,
} from "react-native";
import { Doc, Id } from "../../convex/_generated/dataModel";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import ChatMessageSkeleton from "../skeletons/chat-message-skeleton";

const formatMessageTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const isToday = date.toDateString() === now.toDateString();

  // If today, show time only
  if (isToday) {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  }

  // If this year, show date without year
  if (date.getFullYear() === now.getFullYear()) {
    return (
      date.toLocaleDateString([], { month: "short", day: "numeric" }) +
      " " +
      date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    );
  }

  // If different year, show full date
  return (
    date.toLocaleDateString([], {
      year: "numeric",
      month: "short",
      day: "numeric",
    }) +
    " " +
    date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  );
};

const UserAvatar: React.FC<{
  name?: string;
  image?: string;
  size: number;
  color?: string;
}> = ({ name, image, size, color }) => {
  const initial = name ? name.charAt(0).toUpperCase() : "?";

  if (image) {
    return (
      <Image
        source={{ uri: image }}
        style={[
          styles.senderAvatar,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            backgroundColor: color,
          },
        ]}
      />
    );
  }

  return (
    <View
      style={[
        styles.initialAvatar,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: color,
        },
      ]}
    >
      <Text style={[styles.initialText, { fontSize: size * 0.5 }]}>
        {initial}
      </Text>
    </View>
  );
};

interface ChatMessageProps {
  message: {
    _id: Id<"messages">;
    text: string;
    image?: Id<"_storage">;
    senderId: Id<"users">;
    _creationTime: number;
  };
  isOwnMessage: boolean;
  showSender?: boolean;
  senderName?: string;
  senderImage?: string;
  senderColor?: string;
  onLongPress?: () => void;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isOwnMessage,
  showSender,
  senderName,
  senderImage,
  senderColor,
  onLongPress,
}) => {
  const [showTimestamp, setShowTimestamp] = useState(false);
  const { width: windowWidth } = useWindowDimensions();
  const storageUrl = useQuery(api.files.getImageUrl, {
    storageId: message.image,
  });

  const imageUrl = useMemo(() => {
    if (!storageUrl || !message.image) return null;
    return storageUrl;
  }, [message.image, storageUrl]);

  const maxImageWidth = windowWidth * 0.6;
  const imageHeight = maxImageWidth * 0.75;

  // Show skeleton while image is loading
  if (message.image && !imageUrl) {
    return <ChatMessageSkeleton />;
  }

  const toggleTimestamp = () => {
    setShowTimestamp(!showTimestamp);
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        isOwnMessage
          ? styles.ownMessageContainer
          : styles.otherMessageContainer,
      ]}
      onLongPress={onLongPress}
      delayLongPress={500}
    >
      {showSender && !isOwnMessage && (
        <View style={styles.senderContainer}>
          <UserAvatar
            name={senderName}
            image={senderImage}
            size={18}
            color={senderColor}
          />
          <Text style={styles.senderName}>{senderName}</Text>
        </View>
      )}
      <TouchableOpacity
        onPress={toggleTimestamp}
        activeOpacity={0.7}
        style={[
          styles.messageWrapper,
          isOwnMessage ? styles.ownMessageWrapper : styles.otherMessageWrapper,
        ]}
      >
        <View
          style={[
            styles.bubble,
            isOwnMessage ? styles.ownMessage : styles.otherMessage,
            message.image ? styles.imageBubble : null,
          ]}
        >
          {message.image && imageUrl && (
            <Image
              source={{ uri: imageUrl }}
              style={[
                styles.messageImage,
                {
                  width: maxImageWidth,
                  height: imageHeight,
                },
              ]}
              resizeMode="cover"
            />
          )}
          {message.text && (
            <Text
              style={[
                styles.messageText,
                message.image ? styles.imageMessageText : null,
              ]}
            >
              {message.text}
            </Text>
          )}
        </View>
        {showTimestamp && (
          <Text
            style={[
              styles.timestamp,
              isOwnMessage ? styles.timestampOwn : styles.timestampOther,
            ]}
          >
            {formatMessageTime(message._creationTime)}
          </Text>
        )}
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 2,
    maxWidth: "80%",
  },
  ownMessageContainer: {
    alignSelf: "flex-end",
  },
  otherMessageContainer: {
    alignSelf: "flex-start",
  },
  bubble: {
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 2,
  },
  imageBubble: {
    padding: 0,
    overflow: "hidden",
    backgroundColor: "transparent",
  },
  ownMessage: {
    backgroundColor: "#3B82F6",
  },
  otherMessage: {
    backgroundColor: "#27272A",
  },
  messageText: {
    color: "#fff",
    fontSize: 16,
    lineHeight: 20,
  },
  imageMessageText: {
    marginTop: 8,
    marginHorizontal: 12,
    marginBottom: 8,
    backgroundColor: "#27272A",
    padding: 8,
    borderRadius: 12,
  },
  senderContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
    marginLeft: 4,
  },
  senderName: {
    fontSize: 12,
    color: "#71717A",
    marginLeft: 4,
  },
  senderAvatar: {
    backgroundColor: "#27272A",
  },
  initialAvatar: {
    justifyContent: "center",
    alignItems: "center",
  },
  initialText: {
    color: "#FFFFFF",
    fontWeight: "600",
  },
  messageImage: {
    borderRadius: 16,
  },
  timestamp: {
    fontSize: 11,
    marginTop: 4,
    marginHorizontal: 4,
    color: "#71717A",
  },
  timestampOwn: {
    textAlign: "right",
  },
  timestampOther: {
    textAlign: "left",
  },
  messageWrapper: {
    maxWidth: "100%",
  },
  ownMessageWrapper: {
    alignItems: "flex-end",
  },
  otherMessageWrapper: {
    alignItems: "flex-start",
  },
});
