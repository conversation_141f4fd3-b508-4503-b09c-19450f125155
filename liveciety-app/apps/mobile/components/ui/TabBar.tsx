import React from "react";
import { View, TouchableOpacity, Text, StyleSheet, Platform } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import type { BottomTabBarProps } from "@react-navigation/bottom-tabs";

const TAB_ICONS: Record<string, keyof typeof Ionicons.glyphMap> = {
  index: "home",
  categories: "grid",
  sell: "storefront",
  activity: "notifications",
  account: "person",
};

interface CustomTabBarProps extends BottomTabBarProps {
  onSellPress: () => void;
}

export default function TabBar({ state, descriptors, navigation, onSellPress }: CustomTabBarProps) {
  const primaryColor = "#1a90cb";
  const inactiveColor = "#8E8E93";

  // Define the tab order we want
  const tabOrder = ["index", "categories", "sell", "activity", "account"];

  return (
    <View style={styles.tabBarContainer}>
      <View style={styles.tabBar}>
        {tabOrder.map((routeName) => {
          // Handle the Sell button separately
          if (routeName === "sell") {
            return (
              <TouchableOpacity
                key="sell"
                accessibilityRole="button"
                onPress={onSellPress}
                style={styles.tabButton}
                activeOpacity={0.8}
              >
                <Ionicons
                  name="storefront"
                  size={26}
                  color={inactiveColor}
                />
                <Text
                  style={{
                    color: inactiveColor,
                    fontSize: 11,
                    marginTop: 2,
                  }}
                >
                  Sell
                </Text>
              </TouchableOpacity>
            );
          }

          // Handle regular tabs
          const route = state.routes.find(r => r.name === routeName);
          if (!route) return null;

          const descriptor = descriptors[route.key];
          if (!descriptor) return null;

          const { options } = descriptor;
          const label =
            options.tabBarLabel !== undefined
              ? options.tabBarLabel
              : options.title !== undefined
              ? options.title
              : route.name;

          const isFocused = state.index === state.routes.findIndex(r => r.name === route.name);

          const onPress = () => {
            const event = navigation.emit({
              type: "tabPress",
              target: route.key,
              canPreventDefault: true,
            });
            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name, route.params);
            }
          };

          return (
            <TouchableOpacity
              key={route.name}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              onPress={onPress}
              style={styles.tabButton}
              activeOpacity={0.8}
            >
              <Ionicons
                name={TAB_ICONS[route.name] || "ellipse-outline"}
                size={26}
                color={isFocused ? primaryColor : inactiveColor}
              />
              <Text
                style={{
                  color: isFocused ? primaryColor : inactiveColor,
                  fontSize: 11,
                  marginTop: 2,
                }}
              >
                {typeof label === "function"
                  ? label({
                      focused: isFocused,
                      color: isFocused ? primaryColor : inactiveColor,
                      position: "below-icon",
                      children: route.name,
                    })
                  : label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  tabBarContainer: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: Platform.OS === "ios" ? 24 : 12,
    alignItems: "center",
    zIndex: 100,
  },
  tabBar: {
    flexDirection: "row",
    backgroundColor: "#121212",
    borderRadius: 32,
    paddingHorizontal: 28,
    paddingVertical: 18,
    alignItems: "center",
    justifyContent: "space-between",
    width: "95%",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.12,
    shadowRadius: 24,
    elevation: 16,
  },
  tabButton: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 4,
  },
}); 