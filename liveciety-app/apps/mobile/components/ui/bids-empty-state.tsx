import React from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";

export default function BidsEmptyState() {
  const router = useRouter();

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => router.push("/categories")}
    >
      <View style={styles.textContainer}>
        <Text style={styles.title}>
          You have no active{"\n"}bids. Browse auctions{"\n"}to place a bid.
        </Text>
      </View>
      <Image
        source={require("@icon.png")}
        style={styles.image}
        resizeMode="contain"
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#333",
    borderRadius: 16,
    flexDirection: "row",
    overflow: "hidden",
    marginHorizontal: 16,
    marginVertical: 8,
  },
  textContainer: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    color: "#fff",
    lineHeight: 28,
  },
  image: {
    width: 140,
    height: 140,
    marginTop: 0,
    marginRight: -10,
  },
});
