import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { CustomBottomSheet } from "./bottom-sheet";

interface StreamOptionsSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function StreamOptionsSheet({
  isOpen,
  onClose,
}: StreamOptionsSheetProps) {
  const router = useRouter();

  const handleStartStream = () => {
    onClose();
    router.push("/screens/stream/new-listing");
  };

  const handleScheduleStream = () => {
    onClose();
    router.push("/screens/stream/schedule");
  };

  return (
    <CustomBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      snapPoints={["30%"]}
      enableDynamicHeight={false}
    >
      <View style={styles.container}>
        <Text style={styles.title}>Go Live</Text>

        <TouchableOpacity style={styles.option} onPress={handleStartStream}>
          <View style={styles.iconContainer}>
            <Ionicons name="radio" size={24} color="#fff" />
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.optionTitle}>Create a listing</Text>
            <Text style={styles.optionDescription}>
              Add a new listing to your store
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color="#8E8E93" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.option} onPress={handleScheduleStream}>
          <View style={styles.iconContainer}>
            <Ionicons name="calendar" size={24} color="#fff" />
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.optionTitle}>Schedule Stream</Text>
            <Text style={styles.optionDescription}>
              Plan your stream for later
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color="#8E8E93" />
        </TouchableOpacity>
      </View>
    </CustomBottomSheet>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 24,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 15,
    color: "#8E8E93",
  },
});
