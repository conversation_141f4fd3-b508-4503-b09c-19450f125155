import React from "react";
import { View, StyleSheet, TouchableOpacity, Share } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import UserSearch from "./user-search";

interface HeaderProps {
  onSearchActiveChange?: (active: boolean) => void;
  isSearchActive?: boolean;
  back?: boolean;
  onBack?: () => void;
}

export default function Header({
  onSearchActiveChange,
  isSearchActive,
  back,
  onBack,
}: HeaderProps) {
  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: "Check out Liveciety!",
        url: "https://liveciety.com",
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.warn("Shared with activity type:", result.activityType);
        } else {
          console.warn("Shared successfully");
        }
      } else if (result.action === Share.dismissedAction) {
        console.warn("Share dismissed");
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <View style={styles.header}>
      {back && (
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="chevron-back-outline" size={24} color="#fff" />
        </TouchableOpacity>
      )}
      <View style={styles.searchContainer}>
        <UserSearch
          placeholder="Search Liveciety"
          style={styles.userSearch}
          onFocus={() => onSearchActiveChange?.(true)}
          onBlur={() => onSearchActiveChange?.(false)}
        />
      </View>
      {!isSearchActive && (
        <View style={styles.headerIcons}>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push("/screens/app/notifications")}
          >
            <Ionicons name="notifications-outline" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={handleShare}>
            <Ionicons name="share-social-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    zIndex: 1,
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  searchContainer: {
    flex: 1,
    height: 40,
  },
  headerIcons: {
    flexDirection: "row",
    marginLeft: 8,
  },
  userSearch: {
    flex: 1,
  },
  iconButton: {
    marginLeft: 16,
    padding: 4,
  },
});
