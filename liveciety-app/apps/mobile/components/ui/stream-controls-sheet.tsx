import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { CustomBottomSheet } from "./bottom-sheet";

interface StreamControlsSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onStopStream: () => void;
  onShareStream?: () => void;
  onRotateCamera?: () => void;
  onToggleMic?: () => void;
  onManageProducts?: () => void;
  isHost: boolean;
  streamStatus: string;
  isMicEnabled?: boolean;
}

export default function StreamControlsSheet({
  isOpen,
  onClose,
  onStopStream,
  onShareStream,
  onRotateCamera,
  onToggleMic,
  onManageProducts,
  isHost,
  streamStatus,
  isMicEnabled = true,
}: StreamControlsSheetProps) {

  const handleStopStream = () => {
    Alert.alert(
      "Stop Stream",
      "Are you sure you want to stop this stream? Viewers will still be able to chat, but you won't be able to stream anymore.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Stop Stream",
          style: "destructive",
          onPress: () => {
            onStopStream();
            onClose();
          },
        },
      ]
    );
  };

  const handleReportStream = () => {
    Alert.alert(
      "Report Stream",
      "Report this stream for inappropriate content?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Report",
          style: "destructive",
          onPress: () => {
            // Implement report functionality
            Alert.alert("Reported", "Stream has been reported for review.");
            onClose();
          },
        },
      ]
    );
  };

  const handleShareStream = () => {
    if (onShareStream) {
      onShareStream();
    }
    onClose();
  };

  return (
    <CustomBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      snapPoints={isHost && streamStatus === "live" ? ["50%"] : isHost ? ["55%"] : ["25%"]}
      enableDynamicHeight={false}
    >
      <View style={styles.container}>
        <Text style={styles.title}>Stream Options</Text>

        {/* Broadcasting Controls - Host only during live stream */}
        {isHost && streamStatus === "live" && (
          <View style={styles.broadcastingSection}>
            <Text style={styles.sectionTitle}>Broadcasting</Text>
            <View style={styles.controlsGrid}>
              <TouchableOpacity 
                style={styles.controlButton} 
                onPress={onRotateCamera}
              >
                <View style={styles.controlIcon}>
                  <Ionicons name="camera-reverse" size={24} color="#fff" />
                </View>
                <Text style={styles.controlLabel}>Rotate Camera</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.controlButton} 
                onPress={onToggleMic}
              >
                <View style={[styles.controlIcon, !isMicEnabled && styles.disabledIcon]}>
                  <Ionicons 
                    name={isMicEnabled ? "mic" : "mic-off"} 
                    size={24} 
                    color={isMicEnabled ? "#fff" : "#FF3B30"} 
                  />
                </View>
                <Text style={styles.controlLabel}>
                  {isMicEnabled ? "Mic" : "Mic Off"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Host-only options */}
        {isHost && (
          <TouchableOpacity 
            style={styles.option} 
            onPress={() => {
              if (onManageProducts) {
                onManageProducts();
              }
              onClose();
            }}
          >
            <View style={styles.iconContainer}>
              <Ionicons name="storefront-outline" size={24} color="#fff" />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.optionTitle}>Manage Products</Text>
              <Text style={styles.optionDescription}>
                Pin products to show at the bottom of your stream
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={24} color="#8E8E93" />
          </TouchableOpacity>
        )}

        {isHost && streamStatus === "live" && (
          <TouchableOpacity style={styles.option} onPress={handleStopStream}>
            <View style={[styles.iconContainer, styles.dangerIcon]}>
              <Ionicons name="stop-circle" size={24} color="#FF3B30" />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.optionTitle}>End Show</Text>
              <Text style={styles.optionDescription}>
                End the live stream but keep chat active
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={24} color="#8E8E93" />
          </TouchableOpacity>
        )}

        {/* Share option for everyone */}
        <TouchableOpacity style={styles.option} onPress={handleShareStream}>
          <View style={styles.iconContainer}>
            <Ionicons name="share-outline" size={24} color="#fff" />
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.optionTitle}>Share Stream</Text>
            <Text style={styles.optionDescription}>
              Share this stream with others
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color="#8E8E93" />
        </TouchableOpacity>

        {/* Report option for non-hosts */}
        {!isHost && (
          <TouchableOpacity style={styles.option} onPress={handleReportStream}>
            <View style={[styles.iconContainer, styles.warningIcon]}>
              <Ionicons name="flag-outline" size={24} color="#FF9500" />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.optionTitle}>Report Stream</Text>
              <Text style={styles.optionDescription}>
                Report inappropriate content
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={24} color="#8E8E93" />
          </TouchableOpacity>
        )}
      </View>
    </CustomBottomSheet>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 24,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  dangerIcon: {
    backgroundColor: "rgba(255, 59, 48, 0.1)",
  },
  warningIcon: {
    backgroundColor: "rgba(255, 149, 0, 0.1)",
  },
  textContainer: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 15,
    color: "#8E8E93",
  },
  broadcastingSection: {
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 16,
  },
  controlsGrid: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
  },
  controlButton: {
    alignItems: "center",
    padding: 12,
    minWidth: 80,
  },
  controlIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#2C2C2E",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  disabledIcon: {
    backgroundColor: "rgba(255, 59, 48, 0.1)",
  },
  controlLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#fff",
    textAlign: "center",
  },
}); 