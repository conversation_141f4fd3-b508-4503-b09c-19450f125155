import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Animated,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { LinearGradient } from "expo-linear-gradient";

interface PinnedProductsDisplayProps {
  streamId: string;
  onProductPress?: (product: any) => void;
  style?: any;
}

export default function PinnedProductsDisplay({
  streamId,
  onProductPress,
  style,
}: PinnedProductsDisplayProps) {
  const stream = useQuery(
    api.streams.getStream,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip"
  );
  
  const products = useQuery(
    api.sellers.getProductsForStream,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip"
  );

  console.log("PinnedProductsDisplay - Stream:", stream);
  console.log("PinnedProductsDisplay - Products:", products);

  // Filter out any null products and ensure we have valid pinned products
  const pinnedProducts = (products || [])
    .filter(product => product && stream?.pinnedProducts?.includes(product._id as Id<"products">))
    .filter((product): product is NonNullable<typeof product> => product !== null);

  console.log("PinnedProductsDisplay - Filtered Products:", pinnedProducts);

  if (!pinnedProducts || pinnedProducts.length === 0) {
    console.log("PinnedProductsDisplay - No products to display");
    return null;
  }

  const handleProductPress = (product: any) => {
    if (onProductPress) {
      onProductPress(product);
    }
  };

  const renderProduct = (product: any, index: number) => (
    <TouchableOpacity
      key={product._id}
      style={[
        styles.productCard,
        index === 0 && styles.firstCard,
        index === pinnedProducts.length - 1 && styles.lastCard,
      ]}
      onPress={() => handleProductPress(product)}
      activeOpacity={0.85}
    >
      <View style={styles.productImageContainer}>
        {product.images && product.images.length > 0 ? (
          <Image
            source={{ uri: product.images[0] }}
            style={styles.productImage}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.placeholderImage}>
            <Ionicons name="image-outline" size={20} color="#666" />
          </View>
        )}
        
        {/* Live indicator for products being featured */}
        <View style={styles.liveIndicator}>
          <View style={styles.liveDot} />
          <Text style={styles.liveText}>LIVE</Text>
        </View>
      </View>

      <LinearGradient
        colors={["transparent", "rgba(0,0,0,0.8)"]}
        style={styles.productOverlay}
      >
        <View style={styles.productInfo}>
          <Text style={styles.productName} numberOfLines={2}>
            {product.name}
          </Text>
          
          <View style={styles.priceContainer}>
            <Text style={styles.productPrice}>
              ${((product.price || 0) / 100).toFixed(2)}
            </Text>
            {product.condition && (
              <Text style={styles.productCondition}>
                {product.condition}
              </Text>
            )}
          </View>

          {product.seller && (
            <View style={styles.sellerInfo}>
              <Text style={styles.sellerText} numberOfLines={1}>
                by {product.seller.username || product.seller.name}
              </Text>
            </View>
          )}
        </View>

        <TouchableOpacity style={styles.buyButton} onPress={() => handleProductPress(product)}>
          <Ionicons name="basket-outline" size={16} color="#000" />
          <Text style={styles.buyButtonText}>Buy</Text>
        </TouchableOpacity>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, style]}>
      <LinearGradient
        colors={["transparent", "rgba(0,0,0,0.9)", "rgba(0,0,0,0.95)"]}
        style={styles.backgroundGradient}
      />
      
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Ionicons name="storefront-outline" size={16} color="#fff" />
          <Text style={styles.headerText}>Featured Products</Text>
        </View>
        <TouchableOpacity style={styles.viewAllButton}>
          <Text style={styles.viewAllText}>View All</Text>
          <Ionicons name="chevron-forward" size={14} color="#1a96d2" />
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
        style={styles.scrollView}
      >
        {pinnedProducts.map((product, index) => renderProduct(product, index))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 20,
  },
  backgroundGradient: {
    position: "absolute",
    top: -50,
    left: 0,
    right: 0,
    bottom: 0,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 8,
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 6,
  },
  viewAllButton: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewAllText: {
    color: "#1a96d2",
    fontSize: 12,
    fontWeight: "500",
    marginRight: 2,
  },
  scrollView: {
    paddingLeft: 16,
  },
  scrollContainer: {
    paddingRight: 16,
  },
  productCard: {
    width: 140,
    height: 180,
    marginRight: 12,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: "#2C2C2E",
    position: "relative",
  },
  firstCard: {
    marginLeft: 0,
  },
  lastCard: {
    marginRight: 0,
  },
  productImageContainer: {
    width: "100%",
    height: "60%",
    position: "relative",
  },
  productImage: {
    width: "100%",
    height: "100%",
  },
  placeholderImage: {
    width: "100%",
    height: "100%",
    backgroundColor: "#2C2C2E",
    justifyContent: "center",
    alignItems: "center",
  },
  liveIndicator: {
    position: "absolute",
    top: 8,
    left: 8,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ff2d55",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  liveDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: "#fff",
    marginRight: 4,
  },
  liveText: {
    color: "#fff",
    fontSize: 8,
    fontWeight: "bold",
  },
  productOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: "50%",
    padding: 8,
    justifyContent: "space-between",
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 4,
    lineHeight: 16,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  productPrice: {
    color: "#1a96d2",
    fontSize: 14,
    fontWeight: "bold",
  },
  productCondition: {
    color: "#8E8E93",
    fontSize: 10,
  },
  sellerInfo: {
    marginBottom: 8,
  },
  sellerText: {
    color: "#8E8E93",
    fontSize: 10,
  },
  buyButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#1a96d2",
    paddingVertical: 6,
    paddingHorizontal: 8,
    borderRadius: 6,
    alignSelf: "stretch",
  },
  buyButtonText: {
    color: "#000",
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 4,
  },
}); 