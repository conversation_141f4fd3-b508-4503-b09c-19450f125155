import React from "react";
import { View, Text, StyleSheet, Animated } from "react-native";
import { useToast } from "./use-toast";

export function Toast() {
  const { toasts } = useToast();

  return (
    <View style={styles.container}>
      {toasts.map((toast, index) => (
        <View
          key={index}
          style={[
            styles.toast,
            toast.variant === "destructive" && styles.destructive,
          ]}
        >
          <Text style={styles.title}>{toast.title}</Text>
          <Text style={styles.description}>{toast.description}</Text>
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    bottom: 100,
    left: 0,
    right: 0,
    zIndex: 9999,
    padding: 16,
    gap: 8,
  },
  toast: {
    backgroundColor: "#2C2C2E",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  destructive: {
    backgroundColor: "#FF3B30",
  },
  title: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  description: {
    color: "#fff",
    fontSize: 14,
    opacity: 0.8,
  },
});
