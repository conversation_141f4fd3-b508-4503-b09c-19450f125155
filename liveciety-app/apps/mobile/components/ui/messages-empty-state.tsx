import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useState } from "react";
import { ComposeMessageSheet } from "./compose-message-sheet";

export default function MessagesEmptyState() {
  const [isComposeOpen, setIsComposeOpen] = useState(false);

  const handleOpenCompose = () => {
    setIsComposeOpen(true);
  };

  const handleCloseCompose = () => {
    setIsComposeOpen(false);
  };

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <Text style={styles.title}>Start a Chat</Text>
        <Text style={styles.subtitle}>
          Chat with your community, get to know{"\n"}sellers, plan your next
          co-host and more!
        </Text>
      </View>
      <TouchableOpacity
        style={styles.floatingButton}
        onPress={handleOpenCompose}
      >
        <Ionicons name="create" size={24} color="white" />
      </TouchableOpacity>

      <ComposeMessageSheet
        isOpen={isComposeOpen}
        onClose={handleCloseCompose}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
  },
  contentContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  title: {
    fontSize: 40,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 16,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 17,
    color: "#8E8E93",
    textAlign: "center",
    lineHeight: 22,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 15,
    right: 15,
    zIndex: 1,
  },
  floatingButton: {
    position: "absolute",
    right: 20,
    bottom: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#0A84FF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
