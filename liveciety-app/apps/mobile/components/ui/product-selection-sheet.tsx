import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { CustomBottomSheet } from "./bottom-sheet";

interface ProductSelectionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  streamId: string;
}

export default function ProductSelectionSheet({
  isOpen,
  onClose,
  streamId,
}: ProductSelectionSheetProps) {
  const [refreshKey, setRefreshKey] = useState(0);
  
  const productsData = useQuery(
    api.streams.getUserProductsForPinning,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip"
  );
  
  const pinProduct = useMutation(api.streams.pinProduct);
  const unpinProduct = useMutation(api.streams.unpinProduct);

  const handleTogglePin = async (productId: string, isPinned: boolean) => {
    try {
      if (!productsData?.canModifyPins) {
        Alert.alert("Permission Denied", "Only the host can pin or unpin products");
        return;
      }

      if (isPinned) {
        await unpinProduct({
          streamId: streamId as Id<"streams">,
          productId: productId as Id<"products">,
        });
      } else {
        await pinProduct({
          streamId: streamId as Id<"streams">,
          productId: productId as Id<"products">,
        });
      }
      setRefreshKey(prev => prev + 1);
    } catch (error) {
      Alert.alert("Error", error instanceof Error ? error.message : "Failed to update product");
    }
  };

  const renderProduct = ({ item }: { item: any }) => (
    <View style={styles.productItem}>
      <View style={styles.productImage}>
        {item.imageUrl ? (
          <Image
            source={{ uri: item.imageUrl }}
            style={styles.image}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.placeholderImage}>
            <Ionicons name="image-outline" size={24} color="#666" />
          </View>
        )}
      </View>
      
      <View style={styles.productInfo}>
        <Text style={styles.productName} numberOfLines={2}>
          {item.name}
        </Text>
        <Text style={styles.productPrice}>
          ${item.price?.toFixed(2) || "0.00"}
        </Text>
        {item.condition && (
          <Text style={styles.productCondition}>
            Condition: {item.condition}
          </Text>
        )}
      </View>
      
      {productsData?.canModifyPins && (
        <TouchableOpacity
          style={[
            styles.pinButton,
            item.isPinned ? styles.pinnedButton : styles.unpinnedButton,
          ]}
          onPress={() => handleTogglePin(item._id, item.isPinned)}
        >
          <Ionicons
            name={item.isPinned ? "bookmark" : "bookmark-outline"}
            size={20}
            color={item.isPinned ? "#fff" : "#1a96d2"}
          />
          <Text
            style={[
              styles.pinButtonText,
              item.isPinned ? styles.pinnedButtonText : styles.unpinnedButtonText,
            ]}
          >
            {item.isPinned ? "Unpin" : "Pin"}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const products = productsData?.products || [];
  const isLoading = productsData === undefined;
  const isEmpty = !isLoading && products.length === 0;

  return (
    <CustomBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      snapPoints={["70%"]}
      enableDynamicHeight={false}
    >
      <View style={styles.container}>
        <Text style={styles.title}>Products in Stream</Text>
        <Text style={styles.subtitle}>
          {productsData?.canModifyPins 
            ? "Select up to 3 products to display at the bottom of your stream" 
            : "Products available in this stream"}
        </Text>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading products...</Text>
          </View>
        ) : isEmpty ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="cube-outline" size={48} color="#666" />
            <Text style={styles.emptyText}>No products available</Text>
            <Text style={styles.emptySubtext}>
              {productsData?.canModifyPins 
                ? "Create some products first to pin them to your stream" 
                : "This stream has no products yet"}
            </Text>
          </View>
        ) : (
          <FlatList
            data={products}
            renderItem={renderProduct}
            keyExtractor={(item) => `${item._id}-${refreshKey}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </CustomBottomSheet>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#8E8E93",
    marginBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#8E8E93",
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
  },
  emptySubtext: {
    color: "#8E8E93",
    fontSize: 14,
    textAlign: "center",
    marginTop: 8,
  },
  listContainer: {
    paddingBottom: 16,
  },
  productItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: "hidden",
    marginRight: 12,
  },
  image: {
    width: "100%",
    height: "100%",
  },
  placeholderImage: {
    width: "100%",
    height: "100%",
    backgroundColor: "#2C2C2E",
    justifyContent: "center",
    alignItems: "center",
  },
  productInfo: {
    flex: 1,
    marginRight: 12,
  },
  productName: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  productPrice: {
    color: "#1a96d2",
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 2,
  },
  productCondition: {
    color: "#8E8E93",
    fontSize: 12,
  },
  pinButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  pinnedButton: {
    backgroundColor: "#1a96d2",
    borderColor: "#1a96d2",
  },
  unpinnedButton: {
    backgroundColor: "transparent",
    borderColor: "#1a96d2",
  },
  pinButtonText: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: "600",
  },
  pinnedButtonText: {
    color: "#fff",
  },
  unpinnedButtonText: {
    color: "#1a96d2",
  },
}); 