import React, { useState } from "react";
import { useEffect } from "react";
import { Platform, View, Text } from "react-native";
import {
  router,
  usePathname,
} from "expo-router";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { ConvexAuthProvider, useAuthActions } from "@convex-dev/auth/react";
import { ConvexReactClient } from "convex/react";
import * as SecureStore from "expo-secure-store";
import * as SplashScreen from "expo-splash-screen";

const convex = new ConvexReactClient(
  process.env.EXPO_PUBLIC_CONVEX_URL!,
  {
    unsavedChangesWarning: false,
  },
);

const secureStorage = {
  getItem: async (key: string) => {
    try {
      const value = await SecureStore.getItemAsync(key);
      console.log('🔵 SecureStore getItem:', key, value ? 'has value' : 'null');
      return value;
    } catch (e) {
      console.error("🔴 SecureStore getItem error:", e);
      return null;
    }
  },
  setItem: async (key: string, value: string) => {
    try {
      await SecureStore.setItemAsync(key, value);
      console.log('🔵 SecureStore setItem:', key, 'stored successfully');
    } catch (e) {
      console.error("🔴 SecureStore setItem error:", e);
    }
  },
  removeItem: async (key: string) => {
    try {
      await SecureStore.deleteItemAsync(key);
      console.log('🔵 SecureStore removeItem:', key, 'removed successfully');
    } catch (e) {
      console.error("🔴 SecureStore removeItem error:", e);
    }
  },
};

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync().catch(() => {
  /* reloading the app might trigger some race conditions, ignore them */
});

function AuthGuardInner() {
  const pathname = usePathname();
  const [isNavigationReady, setIsNavigationReady] = useState(true);
  const [hasCheckedPendingOAuth, setHasCheckedPendingOAuth] = useState(false);
  const user = useQuery(api.users.viewer);
  const hasActiveCode = useQuery(api.users.hasActiveVerificationCode);
  const { signIn } = useAuthActions();

  useEffect(() => {
    let isMounted = true;

    const handleNavigation = async () => {
      try {
        // Check for pending OAuth data first
        if (!hasCheckedPendingOAuth && user === null) {
          console.log('AuthGuard: Checking for pending OAuth data...');
          try {
            const pendingUserId = await SecureStore.getItemAsync('pendingOAuthUserId');
            const pendingEmail = await SecureStore.getItemAsync('pendingOAuthEmail');

            if (pendingUserId && pendingEmail) {
              console.log('AuthGuard: Found pending OAuth data, completing authentication...');



              // The OAuth flow has completed and the user exists in the database
              // The issue is that the client-side auth state is not updated
              // Since the OAuth flow is complete and the user exists, we need to trigger
              // the Convex Auth system to recognize the authentication
              console.log('AuthGuard: OAuth user data found, attempting to complete authentication...');

              try {
                // Try to use the signIn function to trigger authentication state update
                // Since the OAuth flow is already complete, we'll use a different approach
                console.log('AuthGuard: Attempting to trigger auth state update...');

                // Clear the pending data first
                await SecureStore.deleteItemAsync('pendingOAuthUserId');
                await SecureStore.deleteItemAsync('pendingOAuthEmail');
                await SecureStore.deleteItemAsync('pendingOAuthName');
                await SecureStore.deleteItemAsync('pendingOAuthImage');
                await SecureStore.deleteItemAsync('pendingOAuthUsername');

                console.log('AuthGuard: Pending OAuth data cleared');

                // The OAuth flow is complete, but the client doesn't know about it
                // Let's force a complete app restart to refresh the auth state
                console.log('AuthGuard: OAuth flow complete, forcing app restart to refresh auth state...');

                setHasCheckedPendingOAuth(true);

                // Force a complete app reload to refresh the authentication state
                if (__DEV__) {
                  console.log('AuthGuard: Development mode - redirecting to home to check auth state');
                  router.replace('/');
                } else {
                  // In production, force a complete app reload
                  const Updates = require('expo-updates');
                  await Updates.reloadAsync();
                }

                return;
              } catch (error) {
                console.error('AuthGuard: Error handling OAuth completion:', error);
                // Continue to clear pending data and redirect to login
              }
            }
          } catch (oauthError) {
            console.error('AuthGuard: Error completing OAuth authentication:', oauthError);
          }
          setHasCheckedPendingOAuth(true);
        }

        if (user === undefined || hasActiveCode === undefined) {
          console.log('AuthGuard: Still loading user data...', { user, hasActiveCode });
          return;
        }

        if (!isMounted) return;

        const inAuthGroup = pathname.startsWith("/screens/auth");
        const inOAuthGroup = pathname.startsWith("/oauth");

        if (user === null && !inAuthGroup && !inOAuthGroup) {
          console.log('AuthGuard: No user, redirecting to login');
          router.replace("/screens/auth/login");
          return;
        }

        if (user && inAuthGroup) {
          console.log('AuthGuard: User in auth group, checking status', {
            username: user.username,
            categories: user.preferences?.categories,
            subcategories: user.preferences?.subcategories,
            lastLoginType: user.lastLoginType,
            currentPath: pathname
          });

          if (!user.username) {
            console.log('AuthGuard: No username, redirecting to choose-username');
            if (!pathname.includes("/screens/auth/choose-username")) {
              router.replace("/screens/auth/choose-username");
            }
          } else if (!user.preferences?.categories) {
            console.log('AuthGuard: No categories, redirecting to choose-categories');
            if (!pathname.includes("/screens/auth/choose-categories")) {
              router.replace("/screens/auth/choose-categories");
            }
          } else if (!user.preferences?.subcategories) {
            console.log('AuthGuard: No subcategories, redirecting to choose-subcategories');
            if (!pathname.includes("/screens/auth/choose-subcategories")) {
              router.replace("/screens/auth/choose-subcategories");
            }
          } else {
            console.log('AuthGuard: User setup complete, redirecting to home');
            router.replace("/");
          }
        }

        SplashScreen.hideAsync().catch(() => {
          /* reloading the app might trigger some race conditions, ignore them */
        });
      } catch (error) {
        console.error("AuthGuard error:", error);
        SplashScreen.hideAsync().catch(() => {});
      }
    };

    handleNavigation();

    return () => {
      isMounted = false;
    };
  }, [user, hasActiveCode, pathname, hasCheckedPendingOAuth, signIn]);

  // Return null as we're just handling navigation
  return null;
}

export function AuthGuard({ children }: { children: React.ReactNode }) {
  try {
    return (
      <ConvexAuthProvider
        client={convex}
        storage={
          Platform.OS === "android" || Platform.OS === "ios"
            ? secureStorage
            : undefined
        }
      >
        <AuthGuardInner />
        {children}
      </ConvexAuthProvider>
    );
  } catch (error) {
    console.error("AuthGuard initialization error:", error);
    // Return a fallback UI instead of crashing
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <Text>Unable to initialize app. Please try again.</Text>
      </View>
    );
  }
}
