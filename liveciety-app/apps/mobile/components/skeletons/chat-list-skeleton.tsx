import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { useEffect, useRef } from "react";

export default function ChatListSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  // Create array for chat items
  const chatItems = Array(8).fill(null);

  return (
    <View style={styles.container}>
      {chatItems.map((_, index) => (
        <View key={index} style={styles.chatItem}>
          {/* Avatar skeleton */}
          <Animated.View style={[styles.avatar, { opacity: fadeAnim }]} />

          <View style={styles.chatInfo}>
            <View style={styles.topLine}>
              {/* Title skeleton */}
              <Animated.View
                style={[styles.titleSkeleton, { opacity: fadeAnim }]}
              />
              {/* Time skeleton */}
              <Animated.View
                style={[styles.timeSkeleton, { opacity: fadeAnim }]}
              />
            </View>

            <View style={styles.bottomLine}>
              {/* Message skeleton */}
              <Animated.View
                style={[styles.messageSkeleton, { opacity: fadeAnim }]}
              />
              {/* Unread badge skeleton */}
              {index % 3 === 0 && (
                <Animated.View
                  style={[styles.unreadBadge, { opacity: fadeAnim }]}
                />
              )}
            </View>
          </View>
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  chatItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: "#27272A",
  },
  avatar: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: "#27272A",
  },
  chatInfo: {
    flex: 1,
    marginLeft: 14,
    justifyContent: "center",
  },
  topLine: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 6,
  },
  bottomLine: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  titleSkeleton: {
    height: 17,
    width: "60%",
    backgroundColor: "#27272A",
    borderRadius: 4,
  },
  timeSkeleton: {
    height: 14,
    width: 40,
    backgroundColor: "#27272A",
    borderRadius: 4,
  },
  messageSkeleton: {
    height: 15,
    width: "70%",
    backgroundColor: "#27272A",
    borderRadius: 4,
  },
  unreadBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#27272A",
  },
});
