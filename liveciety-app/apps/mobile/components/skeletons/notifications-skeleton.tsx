import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { useEffect, useRef } from "react";

export default function NotificationsSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  // Create sections for Today and Earlier
  const sections = ["Today", "Earlier"];

  return (
    <View style={styles.container}>
      {sections.map((section) => (
        <View key={section}>
          {/* Section Header */}
          <View style={styles.sectionHeader}>
            <Animated.View
              style={[styles.sectionHeaderSkeleton, { opacity: fadeAnim }]}
            />
          </View>

          {/* Notification Items */}
          {[1, 2, 3].map((item) => (
            <View key={`${section}-${item}`} style={styles.notificationItem}>
              {/* Avatar skeleton */}
              <Animated.View style={[styles.avatar, { opacity: fadeAnim }]} />

              <View style={styles.notificationContent}>
                <View style={styles.textContainer}>
                  {/* Username skeleton */}
                  <Animated.View
                    style={[styles.usernameSkeleton, { opacity: fadeAnim }]}
                  />

                  {/* Message skeleton */}
                  <Animated.View
                    style={[styles.messageSkeleton, { opacity: fadeAnim }]}
                  />

                  {/* Timestamp skeleton */}
                  <Animated.View
                    style={[styles.timestampSkeleton, { opacity: fadeAnim }]}
                  />
                </View>

                {/* Follow button skeleton */}
                <Animated.View
                  style={[styles.followButtonSkeleton, { opacity: fadeAnim }]}
                />
              </View>
            </View>
          ))}
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    paddingTop: 16,
  },
  sectionHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#1C1C1E",
  },
  sectionHeaderSkeleton: {
    height: 16,
    width: 80,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  notificationItem: {
    flexDirection: "row",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textContainer: {
    flex: 1,
  },
  usernameSkeleton: {
    height: 15,
    width: "30%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
    marginBottom: 8,
  },
  messageSkeleton: {
    height: 14,
    width: "50%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
    marginBottom: 6,
  },
  timestampSkeleton: {
    height: 12,
    width: "20%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  followButtonSkeleton: {
    width: 80,
    height: 36,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    marginLeft: 8,
  },
});
