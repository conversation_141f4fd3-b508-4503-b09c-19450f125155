import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { useEffect, useRef } from "react";

export default function SubcategoriesSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  // Create array for subcategory items (including "All" category)
  const items = Array(6).fill(null);

  return (
    <View style={styles.container}>
      {items.map((_, index) => (
        <View key={index} style={styles.subcategoryItem}>
          {/* Icon skeleton */}
          <Animated.View
            style={[styles.subcategoryIcon, { opacity: fadeAnim }]}
          />

          <View style={styles.subcategoryContent}>
            {/* Title skeleton */}
            <Animated.View
              style={[styles.titleSkeleton, { opacity: fadeAnim }]}
            />

            {/* Viewers count skeleton */}
            <Animated.View
              style={[styles.viewersSkeleton, { opacity: fadeAnim }]}
            />
          </View>
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingHorizontal: 10,
  },
  subcategoryItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#1c1c1e",
  },
  subcategoryIcon: {
    width: 40,
    height: 40,
    backgroundColor: "#2C2C2E",
    borderRadius: 8,
    marginRight: 12,
  },
  subcategoryContent: {
    flex: 1,
    gap: 4,
  },
  titleSkeleton: {
    height: 15,
    width: "60%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  viewersSkeleton: {
    height: 12,
    width: "30%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
});
