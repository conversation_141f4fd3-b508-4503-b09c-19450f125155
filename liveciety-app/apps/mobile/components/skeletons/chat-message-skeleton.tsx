import React from "react";
import { View, StyleSheet, Animated, useWindowDimensions } from "react-native";
import { useEffect, useRef } from "react";

export default function ChatMessageSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;
  const { width: windowWidth } = useWindowDimensions();
  const maxImageWidth = windowWidth * 0.6; // 60% of screen width
  const imageHeight = maxImageWidth * 0.75; // 4:3 aspect ratio

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  // Create array for messages
  const messages = Array(10).fill(null);

  return (
    <View style={styles.container}>
      {messages.map((_, index) => {
        const isOwnMessage = index % 2 === 0;
        const hasImage = index % 5 === 0;
        const showSender = !isOwnMessage && (index === 0 || index % 3 === 0);

        return (
          <View
            key={index}
            style={[
              styles.messageContainer,
              isOwnMessage
                ? styles.ownMessageContainer
                : styles.otherMessageContainer,
            ]}
          >
            {showSender && (
              <View style={styles.senderContainer}>
                <Animated.View
                  style={[styles.senderAvatar, { opacity: fadeAnim }]}
                />
                <Animated.View
                  style={[styles.senderName, { opacity: fadeAnim }]}
                />
              </View>
            )}

            <View
              style={[
                styles.messageWrapper,
                isOwnMessage
                  ? styles.ownMessageWrapper
                  : styles.otherMessageWrapper,
              ]}
            >
              <Animated.View
                style={[
                  styles.bubble,
                  isOwnMessage ? styles.ownMessage : styles.otherMessage,
                  hasImage ? styles.imageBubble : null,
                  { opacity: fadeAnim },
                ]}
              >
                {hasImage && (
                  <Animated.View
                    style={[
                      styles.messageImage,
                      {
                        width: maxImageWidth,
                        height: imageHeight,
                        opacity: fadeAnim,
                      },
                    ]}
                  />
                )}
                <Animated.View
                  style={[
                    styles.messageText,
                    hasImage ? styles.imageMessageText : null,
                    { opacity: fadeAnim },
                  ]}
                />
              </Animated.View>
            </View>
          </View>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  messageContainer: {
    marginVertical: 2,
    maxWidth: "80%",
  },
  ownMessageContainer: {
    alignSelf: "flex-end",
  },
  otherMessageContainer: {
    alignSelf: "flex-start",
  },
  senderContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
    marginLeft: 4,
  },
  senderAvatar: {
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: "#27272A",
  },
  senderName: {
    width: 80,
    height: 12,
    backgroundColor: "#27272A",
    borderRadius: 6,
    marginLeft: 4,
  },
  messageWrapper: {
    maxWidth: "100%",
  },
  ownMessageWrapper: {
    alignItems: "flex-end",
  },
  otherMessageWrapper: {
    alignItems: "flex-start",
  },
  bubble: {
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 2,
  },
  imageBubble: {
    padding: 0,
    overflow: "hidden",
    backgroundColor: "transparent",
  },
  ownMessage: {
    backgroundColor: "#3B82F6",
  },
  otherMessage: {
    backgroundColor: "#27272A",
  },
  messageText: {
    height: 20,
    width: 150,
    backgroundColor: "#27272A",
    borderRadius: 4,
  },
  imageMessageText: {
    marginTop: 8,
    marginHorizontal: 12,
    marginBottom: 8,
    backgroundColor: "#27272A",
    padding: 8,
    borderRadius: 12,
  },
  messageImage: {
    borderRadius: 16,
    backgroundColor: "#27272A",
  },
});
