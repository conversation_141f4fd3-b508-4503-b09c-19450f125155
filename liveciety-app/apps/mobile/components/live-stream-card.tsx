import React, { useState } from "react";
import { View, Text, Image, StyleSheet, TouchableOpacity, Platform, Dimensions } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import UserAvatar from "./ui/user-avatar";
import { getImageUrl } from "../lib/utils";
import { Link } from 'expo-router';
import { getCategoryTitleById, getSubcategoryTitleById } from "../lib/utils";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface LiveStreamCardProps {
  username: string;
  title: string;
  category: string;
  subcategory: string;
  viewerCount: number;
  thumbnailUrl: string;
  profileImageUrl: string | null | undefined;
  streamId: Id<"streams">;
  isBookmarked: boolean;
  userColor?: string;
  onPress?: () => void;
  // Optional props for social features
  postTime?: string;
  liked?: boolean;
  likeCount?: number;
  commentCount?: number;
  emoji?: any;
}

export default function LiveStreamCard({
  username,
  title,
  category,
  subcategory,
  viewerCount,
  thumbnailUrl,
  profileImageUrl,
  streamId,
  isBookmarked: initialIsBookmarked,
  userColor = "#2C2C2E",
  onPress,
  postTime = "Just now",
  liked: initialLiked = false,
  likeCount = 0,
  commentCount = 0,
  emoji = null,
}: LiveStreamCardProps) {
  const toggleBookmark = useMutation(api.bookmarks.toggleBookmark);
  const [isBookmarked, setIsBookmarked] = useState(initialIsBookmarked);

  // Process the URLs to ensure they're valid
  const processedThumbnailUrl = getImageUrl(thumbnailUrl || "");

  const handleBookmarkPress = async (e: any) => {
    e.stopPropagation();
    try {
      setIsBookmarked(!isBookmarked);
      const result = await toggleBookmark({ streamId });
      if (result.bookmarked !== !isBookmarked) {
        setIsBookmarked(result.bookmarked);
      }
    } catch (error) {
      setIsBookmarked(isBookmarked);
      console.error("Error toggling bookmark:", error);
    }
  };

  return (
    <Link href={`/screens/stream/${streamId}`} asChild>
      <TouchableOpacity style={styles.container} onPress={onPress}>
        {/* User Info Above Card */}
        <View style={styles.userInfoContainer}>
          <UserAvatar
            image={getImageUrl(profileImageUrl || "")}
            username={username || ""}
            color={userColor}
            size={24}
            style={styles.profileAvatar}
          />
          <Text style={styles.usernameText}>{username}</Text>
        </View>

        {/* Stream Thumbnail */}
        <View style={styles.thumbnailContainer}>
          <Image
            source={{ uri: processedThumbnailUrl }}
            style={styles.thumbnail}
          />
          
          {/* Top Left - Live Badge */}
          <View style={styles.liveBadge}>
            <Text style={styles.liveBadgeText}>Live • {viewerCount}</Text>
          </View>
        </View>

        {/* Content Below */}
        <View style={styles.contentContainer}>
          <Text numberOfLines={1} style={styles.titleText}>
            {title}
          </Text>
          <Text numberOfLines={1} style={styles.categoryText}>
            {getCategoryTitleById(category)} • {getSubcategoryTitleById(category, subcategory)}
          </Text>
        </View>
      </TouchableOpacity>
    </Link>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    backgroundColor: "#1C1C1E",
    overflow: "hidden",
    marginBottom: 0,
  },
  userInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
  },
  profileAvatar: {
    marginRight: 8,
  },
  usernameText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  thumbnailContainer: {
    width: "100%",
    aspectRatio: 0.75,
    position: "relative",
    backgroundColor: "#000",
    borderRadius: 12,
    overflow: "hidden",
  },
  thumbnail: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  liveBadge: {
    position: "absolute",
    top: 8,
    left: 8,
    backgroundColor: "#FF4757",
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 3,
    zIndex: 3,
  },
  liveBadgeText: {
    color: "#fff",
    fontSize: 11,
    fontWeight: "700",
  },
  soundButton: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0,0,0,0.6)",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 3,
  },
  contentContainer: {
    paddingHorizontal: 8,
    paddingTop: 8,
    paddingBottom: 12,
    backgroundColor: "#1C1C1E",
  },
  titleText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "700",
    marginBottom: 2,
  },
  categoryText: {
    color: "#8E8E93",
    fontSize: 11,
    fontWeight: "500",
  },
});
