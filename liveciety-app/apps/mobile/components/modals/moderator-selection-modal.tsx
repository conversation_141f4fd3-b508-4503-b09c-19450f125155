import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { getImageUrl } from "../../lib/utils";

interface ModeratorSelectionModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectModerators: (moderators: any[]) => void;
  selectedModerators: any[];
}

export function ModeratorSelectionModal({
  isVisible,
  onClose,
  onSelectModerators,
  selectedModerators,
}: ModeratorSelectionModalProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [localSelectedModerators, setLocalSelectedModerators] =
    useState<any[]>(selectedModerators);

  // Reset local state when modal opens
  useEffect(() => {
    setLocalSelectedModerators(selectedModerators);
  }, [selectedModerators]);

  const searchResults = useQuery(api.users.searchUsers, {
    searchQuery,
    paginationOpts: {
      numItems: 10,
      cursor: null,
    },
  });

  const toggleModerator = (user: any) => {
    const isSelected = localSelectedModerators.some(
      (mod) => mod._id === user._id,
    );
    if (isSelected) {
      setLocalSelectedModerators((prev) =>
        prev.filter((mod) => mod._id !== user._id),
      );
    } else {
      setLocalSelectedModerators((prev) => [...prev, user]);
    }
  };

  const handleSave = () => {
    onSelectModerators(localSelectedModerators);
    onClose();
  };

  const renderUserItem = ({ item }: { item: any }) => {
    const isSelected = localSelectedModerators.some(
      (mod) => mod._id === item._id,
    );

    return (
      <TouchableOpacity
        style={styles.userItem}
        onPress={() => toggleModerator(item)}
      >
        <View style={styles.userInfo}>
          {item.image ? (
            <Image source={{ uri: getImageUrl(item.image) }} style={styles.avatar} />
          ) : (
            <View style={[styles.avatar, styles.placeholderAvatar]}>
              <Text style={styles.avatarText}>
                {item.username.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          <Text style={styles.username}>{item.username}</Text>
        </View>
        <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
          {isSelected && <Ionicons name="checkmark" size={18} color="#fff" />}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={isVisible} animationType="slide" transparent={true}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={onClose}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Add Moderators</Text>
            <TouchableOpacity onPress={handleSave}>
              <Text style={styles.saveButton}>Save</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#8E8E93" />
            <TextInput
              style={styles.searchInput}
              placeholder="Username"
              placeholderTextColor="#8E8E93"
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
            />
          </View>

          {localSelectedModerators.length > 0 && (
            <View style={styles.selectedContainer}>
              <FlatList
                horizontal
                data={localSelectedModerators}
                keyExtractor={(item) => item._id}
                renderItem={({ item }) => (
                  <View style={styles.selectedUser}>
                    <Text style={styles.selectedUsername}>{item.username}</Text>
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => toggleModerator(item)}
                    >
                      <Ionicons name="close" size={16} color="#fff" />
                    </TouchableOpacity>
                  </View>
                )}
                showsHorizontalScrollIndicator={false}
              />
            </View>
          )}

          <FlatList
            data={searchResults?.users || []}
            renderItem={renderUserItem}
            keyExtractor={(item) => item._id}
            style={styles.userList}
          />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 50,
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
  },
  cancelButton: {
    fontSize: 17,
    color: "#fff",
  },
  saveButton: {
    fontSize: 17,
    color: "#007AFF",
    fontWeight: "600",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#2C2C2E",
    margin: 16,
    paddingHorizontal: 12,
    borderRadius: 10,
  },
  searchInput: {
    flex: 1,
    color: "#fff",
    fontSize: 17,
    padding: 12,
  },
  userList: {
    flex: 1,
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  placeholderAvatar: {
    backgroundColor: "#2C2C2E",
    alignItems: "center",
    justifyContent: "center",
  },
  avatarText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  username: {
    fontSize: 17,
    color: "#fff",
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#2C2C2E",
    alignItems: "center",
    justifyContent: "center",
  },
  checkboxSelected: {
    backgroundColor: "#007AFF",
    borderColor: "#007AFF",
  },
  selectedContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  selectedUser: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#2C2C2E",
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  selectedUsername: {
    color: "#fff",
    fontSize: 15,
    marginRight: 6,
  },
  removeButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
  },
});
