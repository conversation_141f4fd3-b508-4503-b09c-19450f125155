import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { flat_rate_boxes } from "../../lib/shipping-profiles";

interface FlatRateBoxSelectorProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (box: string) => void;
  selectedBox: string | null;
}

export default function FlatRateBoxSelector({
  isVisible,
  onClose,
  onSelect,
  selectedBox,
}: FlatRateBoxSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredBoxes = flat_rate_boxes.filter((box) =>
    box.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>
              Select a Flat-Rate Box (optional)
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <View style={styles.searchBox}>
              <Ionicons name="search" size={20} color="#8E8E93" />
              <TextInput
                style={styles.searchInput}
                placeholder="Search"
                placeholderTextColor="#8E8E93"
                value={searchTerm}
                onChangeText={setSearchTerm}
                autoCapitalize="none"
                autoCorrect={false}
              />
              {searchTerm.length > 0 && (
                <TouchableOpacity onPress={() => setSearchTerm("")}>
                  <Ionicons name="close-circle" size={20} color="#8E8E93" />
                </TouchableOpacity>
              )}
            </View>
          </View>

          <View style={styles.boxList}>
            {filteredBoxes.map((box, index) => (
              <TouchableOpacity
                key={box}
                style={styles.boxItem}
                onPress={() => {
                  onSelect(box);
                  onClose();
                }}
              >
                <Text style={styles.boxText}>{box}</Text>
                <View
                  style={[
                    styles.radioButton,
                    selectedBox === box && styles.radioButtonSelected,
                  ]}
                />
              </TouchableOpacity>
            ))}
            {filteredBoxes.length === 0 && (
              <View style={styles.noResults}>
                <Text style={styles.noResultsText}>
                  No matching boxes found
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  content: {
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    minHeight: 650,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  searchBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#2C2C2E",
    borderRadius: 10,
    padding: 8,
    paddingHorizontal: 12,
  },
  searchInput: {
    flex: 1,
    color: "#fff",
    fontSize: 17,
    marginLeft: 8,
    padding: 0,
  },
  boxList: {
    paddingTop: 8,
  },
  boxItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  boxText: {
    fontSize: 17,
    color: "#fff",
    flex: 1,
    marginRight: 16,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#8E8E93",
  },
  radioButtonSelected: {
    borderColor: "#fff",
    backgroundColor: "#fff",
  },
  noResults: {
    padding: 16,
    alignItems: "center",
  },
  noResultsText: {
    color: "#8E8E93",
    fontSize: 17,
  },
});
