import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { useRouter, Stack, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { categories, subcategories } from "../../lib/constants";

export default function CategoriesModal() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const sourceScreen = params.sourceScreen as string;
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(
    null,
  );

  const handleBack = () => {
    if (selectedCategory) {
      setSelectedCategory(null);
    } else {
      router.back();
    }
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  const handleSubcategorySelect = (subcategory: { id: string; title: string }) => {
    setSelectedSubcategory(subcategory.id);
  };

  // Separate handler for the Select button
  const handleFinalSelection = () => {
    if (selectedCategory && selectedSubcategory) {
      const categoryData = categories.find((cat) => cat.id === selectedCategory);
      const subcategoryData = currentSubcategories?.find(
        (s) => s.id === selectedSubcategory,
      );
      if (categoryData && subcategoryData) {
        if (
          sourceScreen === "/stream/schedule" ||
          sourceScreen === "/stream/new-listing"
        ) {
          const pathname =
            sourceScreen === "/stream/schedule"
              ? "/screens/stream/schedule"
              : "/screens/stream/new-listing";
          router.replace({
            pathname,
            params: {
              selectedCategory: categoryData.title,
              selectedSubcategory: subcategoryData.title,
            },
          });
        } else {
          router.back();
        }
      }
    }
  };

  const selectedCategoryData = categories.find(
    (cat) => cat.id === selectedCategory,
  );
  const currentSubcategories = selectedCategory
    ? subcategories[selectedCategory]
    : [];

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons
              name={selectedCategory ? "chevron-back" : "close"}
              size={24}
              color="#fff"
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {selectedCategory
              ? selectedCategoryData?.title
              : "Select Category..."}
          </Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.list}>
          {!selectedCategory
            ? // Show main categories
              categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={styles.item}
                  onPress={() => handleCategorySelect(category.id)}
                >
                  <Text style={styles.itemText}>{category.title}</Text>
                  <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
                </TouchableOpacity>
              ))
            : // Show subcategories
              currentSubcategories?.map((subcategory) => (
                <TouchableOpacity
                  key={subcategory.id}
                  style={styles.item}
                  onPress={() => setSelectedSubcategory(subcategory.id)}
                >
                  <Text style={styles.itemText}>{subcategory.title}</Text>
                  <View
                    style={[
                      styles.radioButton,
                      selectedSubcategory === subcategory.id &&
                        styles.radioButtonSelected,
                    ]}
                  />
                </TouchableOpacity>
              ))}
        </ScrollView>

        {selectedCategory && selectedSubcategory && (
          <TouchableOpacity
            style={styles.nextButton}
            onPress={handleFinalSelection} // Changed to use the new handler
          >
            <Text style={styles.nextButtonText}>Select</Text>
          </TouchableOpacity>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
    flex: 1,
    textAlign: "center",
  },
  backButton: {
    padding: 8,
  },
  list: {
    flex: 1,
  },
  item: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  itemText: {
    fontSize: 17,
    color: "#fff",
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#8E8E93",
  },
  radioButtonSelected: {
    borderColor: "#1a96d2",
    backgroundColor: "#1a96d2",
  },
  nextButton: {
    backgroundColor: "#1a96d2",
    margin: 16,
    padding: 16,
    borderRadius: 25,
    alignItems: "center",
  },
  nextButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "600",
  },
});
