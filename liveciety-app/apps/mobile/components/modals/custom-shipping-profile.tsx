import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Switch,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { ScaleSelectorSheet } from "./scale-selector-sheet";
import FlatRateBoxSelector from "./flat-rate-box-selector";
import {
  weight_scales,
  dimension_scales,
} from "../../lib/shipping-profiles";

interface CustomShippingProfileProps {
  onBack: () => void;
  onSave: (profile: any) => void;
}

export default function CustomShippingProfile({
  onBack,
  onSave,
}: CustomShippingProfileProps) {
  const [name, setName] = useState("");
  const [weight, setWeight] = useState("");
  const [weightScale, setWeightScale] = useState("ounce");
  const [maxItems, setMaxItems] = useState(false);
  const [maxItemsCount, setMaxItemsCount] = useState("");
  const [fixedWeight, setFixedWeight] = useState(false);
  const [incrementalWeight, setIncrementalWeight] = useState("");
  const [incrementalWeightScale, setIncrementalWeightScale] = useState("ounce");
  const [length, setLength] = useState("");
  const [width, setWidth] = useState("");
  const [height, setHeight] = useState("");
  const [dimensionScale, setDimensionScale] = useState("inch");
  const [isWeightScaleVisible, setIsWeightScaleVisible] = useState(false);
  const [isIncrementalWeightScaleVisible, setIsIncrementalWeightScaleVisible] =
    useState(false);
  const [isDimensionScaleVisible, setIsDimensionScaleVisible] = useState(false);
  const [selectedFlatRateBox, setSelectedFlatRateBox] = useState<string | null>(
    null,
  );
  const [isFlatRateBoxSelectorVisible, setIsFlatRateBoxSelectorVisible] =
    useState(false);

  const handleSave = () => {
    onSave({
      name,
      weight,
      weightScale,
      maxItems: maxItems ? maxItemsCount : null,
      fixedWeight: fixedWeight
        ? {
            weight: incrementalWeight,
            scale: incrementalWeightScale,
          }
        : null,
      dimensions: {
        length,
        width,
        height,
        scale: dimensionScale,
      },
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Shipping Profile</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.sectionTitle}>Details</Text>

        <TextInput
          style={styles.input}
          placeholder="Name*"
          placeholderTextColor="rgba(235, 235, 245, 0.6)"
          value={name}
          onChangeText={setName}
        />

        <View style={styles.weightContainer}>
          <TextInput
            style={[styles.input, styles.weightInput]}
            placeholder="Weight"
            placeholderTextColor="rgba(235, 235, 245, 0.6)"
            value={weight}
            onChangeText={setWeight}
            keyboardType="decimal-pad"
          />
          <TouchableOpacity
            style={styles.scaleButton}
            onPress={() => setIsWeightScaleVisible(true)}
          >
            <Text style={styles.scaleButtonText}>{weightScale}</Text>
            <Ionicons
              name="chevron-down"
              size={20}
              color="rgba(235, 235, 245, 0.6)"
            />
          </TouchableOpacity>
        </View>
        <Text style={styles.helperText}>
          Please enter the weight of this item and packaging
        </Text>

        <Text style={styles.sectionTitle}>Bundling Options</Text>
        <Text style={styles.sectionDescription}>
          Specify what happens when a buyer makes multiple purchases in one
          show.
        </Text>

        <View style={styles.toggleContainer}>
          <View style={styles.toggleTextContainer}>
            <Text style={styles.toggleLabel}>
              Set the maximum number of items to put into one package
            </Text>
            <Text style={styles.toggleDescription}>
              Recommended for items that are unusually sized, fragile, or you
              prefer to ship individually.
            </Text>
          </View>
          <Switch
            value={maxItems}
            onValueChange={setMaxItems}
            trackColor={{ false: "#3A3A3C", true: "#34C759" }}
            ios_backgroundColor="#3A3A3C"
          />
        </View>

        {maxItems && (
          <>
            <TextInput
              style={styles.input}
              placeholder="Max # of items in one box"
              placeholderTextColor="rgba(235, 235, 245, 0.6)"
              value={maxItemsCount}
              onChangeText={setMaxItemsCount}
              keyboardType="number-pad"
            />
            <Text style={styles.helperText}>
              For cost-effective bundling, we recommend setting the quantity
              based on box sizes smaller than 1 ft³.
            </Text>

            <Text style={styles.subSectionTitle}>Box Dimensions</Text>
            <TouchableOpacity
              style={styles.flatRateBoxButton}
              onPress={() => setIsFlatRateBoxSelectorVisible(true)}
            >
              <Text
                style={[
                  styles.flatRateBoxText,
                  !selectedFlatRateBox && styles.flatRateBoxPlaceholder,
                ]}
              >
                {selectedFlatRateBox || "Select a Flat-Rate Box (optional)"}
              </Text>
              <Ionicons
                name="chevron-down"
                size={20}
                color="rgba(235, 235, 245, 0.6)"
              />
            </TouchableOpacity>
            <Text style={styles.helperText}>
              Navigate to your Shipping Settings to opt-in to shipping via
              Flat-Rate.
            </Text>

            <View style={styles.dimensionsContainer}>
              <View style={styles.dimensionInputContainer}>
                <Text style={styles.dimensionLabel}>Length</Text>
                <TextInput
                  style={[styles.input, styles.dimensionInput]}
                  placeholder="15"
                  placeholderTextColor="rgba(235, 235, 245, 0.6)"
                  value={length}
                  onChangeText={setLength}
                  keyboardType="decimal-pad"
                />
              </View>
              <View style={styles.dimensionInputContainer}>
                <Text style={styles.dimensionLabel}>Width</Text>
                <TextInput
                  style={[styles.input, styles.dimensionInput]}
                  placeholder="9.5"
                  placeholderTextColor="rgba(235, 235, 245, 0.6)"
                  value={width}
                  onChangeText={setWidth}
                  keyboardType="decimal-pad"
                />
              </View>
              <View style={styles.dimensionInputContainer}>
                <Text style={styles.dimensionLabel}>Height</Text>
                <TextInput
                  style={[styles.input, styles.dimensionInput]}
                  placeholder="0.75"
                  placeholderTextColor="rgba(235, 235, 245, 0.6)"
                  value={height}
                  onChangeText={setHeight}
                  keyboardType="decimal-pad"
                />
              </View>
              <View style={styles.dimensionInputContainer}>
                <Text style={styles.dimensionLabel}>Scale</Text>
                <TouchableOpacity
                  style={[styles.scaleButton, styles.dimensionScale]}
                  onPress={() => setIsDimensionScaleVisible(true)}
                >
                  <Text style={styles.scaleButtonText}>{dimensionScale}</Text>
                  <Ionicons
                    name="chevron-down"
                    size={20}
                    color="rgba(235, 235, 245, 0.6)"
                  />
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}

        <View style={styles.toggleContainer}>
          <View style={styles.toggleTextContainer}>
            <Text style={styles.toggleLabel}>
              Set a fixed weight for additional items in the same package
            </Text>
            <Text style={styles.toggleDescription}>
              Select an incremental weight for any additional items eligible for
              bundling.
            </Text>
          </View>
          <Switch
            value={fixedWeight}
            onValueChange={setFixedWeight}
            trackColor={{ false: "#3A3A3C", true: "#34C759" }}
            ios_backgroundColor="#3A3A3C"
          />
        </View>

        {fixedWeight && (
          <View style={styles.weightContainer}>
            <TextInput
              style={[styles.input, styles.weightInput]}
              placeholder="Incremental Weight"
              placeholderTextColor="rgba(235, 235, 245, 0.6)"
              value={incrementalWeight}
              onChangeText={setIncrementalWeight}
              keyboardType="decimal-pad"
            />
            <TouchableOpacity
              style={styles.scaleButton}
              onPress={() => setIsIncrementalWeightScaleVisible(true)}
            >
              <Text style={styles.scaleButtonText}>
                {incrementalWeightScale}
              </Text>
              <Ionicons
                name="chevron-down"
                size={20}
                color="rgba(235, 235, 245, 0.6)"
              />
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.saveButton, !name && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={!name}
        >
          <Text style={styles.saveButtonText}>Save Profile</Text>
        </TouchableOpacity>
      </View>

      <ScaleSelectorSheet
        isVisible={isWeightScaleVisible}
        onClose={() => setIsWeightScaleVisible(false)}
        onSelect={setWeightScale}
        scales={weight_scales}
        selectedScale={weightScale}
        title="Weight Scale"
      />

      <ScaleSelectorSheet
        isVisible={isIncrementalWeightScaleVisible}
        onClose={() => setIsIncrementalWeightScaleVisible(false)}
        onSelect={setIncrementalWeightScale}
        scales={weight_scales}
        selectedScale={incrementalWeightScale}
        title="Incremental Weight Scale"
      />

      <ScaleSelectorSheet
        isVisible={isDimensionScaleVisible}
        onClose={() => setIsDimensionScaleVisible(false)}
        onSelect={setDimensionScale}
        scales={dimension_scales}
        selectedScale={dimensionScale}
        title="Dimension Scale"
      />

      <FlatRateBoxSelector
        isVisible={isFlatRateBoxSelectorVisible}
        onClose={() => setIsFlatRateBoxSelectorVisible(false)}
        onSelect={setSelectedFlatRateBox}
        selectedBox={selectedFlatRateBox}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: 60,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 16,
  },
  sectionDescription: {
    fontSize: 15,
    color: "rgba(235, 235, 245, 0.6)",
    marginBottom: 24,
  },
  input: {
    backgroundColor: "#222",
    borderRadius: 12,
    padding: 16,
    color: "#fff",
    fontSize: 17,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  weightContainer: {
    flexDirection: "row",
    gap: 8,
    marginBottom: 8,
  },
  weightInput: {
    flex: 1,
  },
  scaleButton: {
    backgroundColor: "#222",
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    minWidth: 120,
    borderWidth: 1,
    borderColor: "#2C2C2E",
    height: 55,
  },
  scaleButtonText: {
    color: "#fff",
    fontSize: 17,
    flex: 1,
  },
  helperText: {
    fontSize: 13,
    color: "rgba(235, 235, 245, 0.6)",
    marginBottom: 32,
  },
  toggleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 24,
  },
  toggleTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  toggleLabel: {
    fontSize: 17,
    color: "#fff",
    marginBottom: 4,
    lineHeight: 22,
  },
  toggleDescription: {
    fontSize: 13,
    color: "rgba(235, 235, 245, 0.6)",
    lineHeight: 18,
  },
  subSectionTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 16,
  },
  inputPlaceholder: {
    color: "rgba(235, 235, 245, 0.6)",
    fontSize: 17,
  },
  dimensionsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 24,
  },
  dimensionInputContainer: {
    width: "48%",
    minWidth: 80,
  },
  dimensionLabel: {
    color: "rgba(235, 235, 245, 0.6)",
    fontSize: 13,
    marginBottom: 4,
  },
  dimensionInput: {
    backgroundColor: "#1C1C1E",
    textAlign: "left",
    marginBottom: 0,
    height: 55,
  },
  dimensionScale: {
    backgroundColor: "#1C1C1E",
    flex: 0,
    minWidth: "100%",
    height: 55,
  },
  footer: {
    padding: 16,
    paddingBottom: 32,
  },
  saveButton: {
    backgroundColor: "#1a96d2",
    borderRadius: 100,
    padding: 16,
    alignItems: "center",
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "600",
  },
  flatRateBoxButton: {
    backgroundColor: "#222",
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#2C2C2E",
    marginBottom: 8,
  },
  flatRateBoxText: {
    color: "#fff",
    fontSize: 17,
  },
  flatRateBoxPlaceholder: {
    color: "rgba(235, 235, 245, 0.6)",
  },
});
