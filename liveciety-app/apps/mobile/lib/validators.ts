import { z } from "zod";

export const emailSchema = z.string().email("Invalid email address");
export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters long");

export const signInSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  flow: z.literal("signIn"),
  loginType: z.literal("password"),
});

export const signUpSchema = z
  .object({
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: passwordSchema,
    flow: z.literal("signUp"),
    loginType: z.literal("password"),
    name: z.string().min(1, "Name is required"),
    role: z.literal("user"),
  }).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  }
);