import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";
import { CURRENCY_VALIDATOR, PRODUCT_CONDITION, PRODUCT_VISIBILITY } from "./lib/validators";

export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be logged in to upload files.");
    }
    return await ctx.storage.generateUploadUrl();
  },
});

export const createProduct = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    price: v.number(),
    imageId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be logged in to create a product.");
    }

    const productId = await ctx.db.insert("products", {
      sellerId: userId,
      name: args.name,
      description: args.description,
      price: args.price,
      currency: "usd",
      condition: "new",
      visibility: "published",
      images: args.imageId ? [args.imageId] : [],
      status: "active",
    });

    return productId;
  },
});

export const listAvailableProducts = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be logged in to list products.");
    }

    const products = await ctx.db
      .query("products")
      .withIndex("by_sellerId_and_status", (q) => 
        q.eq("sellerId", userId).eq("status", "active")
      )
      .order("desc")
      .collect();

    return Promise.all(
      products.map(async (product) => {
        const seller = await ctx.db.get(product.sellerId);
        return {
          ...product,
          sellerName: seller?.name ?? seller?.email ?? "Unknown Seller",
        };
      })
    );
  },
});

export const getProduct = query({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    if (!product) {
      return null;
    }

    const seller = await ctx.db.get(product.sellerId);
    return {
      ...product,
      sellerName: seller?.name ?? seller?.email ?? "Unknown Seller",
    };
  },
});

export const deleteProduct = mutation({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be logged in to delete a product.");
    }

    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found.");
    }

    if (product.sellerId !== userId) {
      throw new Error("You can only delete your own products.");
    }

    await ctx.db.delete(args.productId);
  },
});
