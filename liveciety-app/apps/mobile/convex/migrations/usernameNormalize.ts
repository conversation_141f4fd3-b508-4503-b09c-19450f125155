import { mutation, MutationCtx } from "../_generated/server";

const normalizeUsername = (username: string): string => {
  return username
    .toLowerCase()
    .replace(/\./g, "_")
    .replace(/\s+/g, "-");
};

const usernameNormalizeMigration = mutation({
  args: {},
  handler: async (ctx: MutationCtx) => {
    const users = await ctx.db.query("users").collect();
    let updated = 0;
    for (const user of users) {
      const normalized = normalizeUsername(user.username || "");
      if (user.username !== normalized) {
        await ctx.db.patch(user._id, { username: normalized });
        updated++;
      }
    }
    return { updated };
  }
});

export default usernameNormalizeMigration; 