import { defineApp } from "convex/server";
import aggregate from "@convex-dev/aggregate/convex.config";
// import polar from "@convex-dev/polar/convex.config";
import twilio from "@convex-dev/twilio/convex.config";

const app: any = defineApp();

app.use(aggregate, { name: "aggregateUsers" });
app.use(aggregate, { name: "aggregateTasks" });
app.use(aggregate, { name: "aggregateFollows" });
// app.use(polar);
app.use(twilio);

export default app;
