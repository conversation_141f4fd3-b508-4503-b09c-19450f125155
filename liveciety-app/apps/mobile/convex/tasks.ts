import { query, mutation } from "./_generated/server";
import { aggregateTasks } from "./custom";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import {
  addIdToRecordField,
  getCurrentUser,
  removeFavoritesForR<PERSON>ord,
  removeRelatedIdFromObject,
} from "./helpers/utils";
import {
  ACTIVITY_TYPES,
  NOTIFICATION_TYPES,
  TASK_PRIORITY,
  TaskStatus,
} from "./lib/types";
import { TASK_STATUS } from "./lib/types";
import { QueryCtx } from "./_generated/server";
import { hasAllPermissions, hasPermission } from "./helpers/permissions";

/**
 * @name create
 * @description Create a task
 * @param title - The title of the task
 * @param description - The description of the task
 * @param dueDate - The due date of the task in epoch time
 * @param related - Id's of contacts related to the task
 * @param assignee - Id of the user assigned to the task
 * @param status - "backlog", "todo", "in_progress", "review", "done"
 * @param priority - "no_priority", "urgent", "high", "medium", "low"
 */
export const create = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    dueDate: v.optional(v.number()),
    related: v.optional(v.union(v.array(v.id("contacts")), v.null())),
    assignee: v.optional(v.union(v.id("users"), v.null())),
    status: v.optional(TASK_STATUS),
    priority: v.optional(TASK_PRIORITY),
  },
  async handler(ctx, args) {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("Not authenticated createTask");
    }

    const tasksInStatus = await ctx.db
      .query("tasks")
      .withIndex("by_status_position", (q) =>
        q.eq("status", args.status || "todo"),
      )
      .order("desc")
      .first();

    const position = tasksInStatus ? (tasksInStatus.position || 0) + 1 : 0;

    let assignee = user._id;
    if (args.assignee !== undefined) {
      assignee = args.assignee as Id<"users">;
    }

    const taskId = await ctx.db.insert("tasks", {
      createdBy: user._id,
      title: args.title,
      description: args.description,
      dueDate: args.dueDate,
      related: args.related as Id<"contacts">[] | null,
      assignee: assignee,
      priority: args.priority || "no_priority",
      status: args.status || "todo",
      position: position,
    });

    if (args.related) {
      for (const relatedId of args.related) {
        await addIdToRecordField(ctx, relatedId, "tasks", taskId);
      }
    }

    if (assignee !== user._id) {
      await ctx.db.insert("adminNotifications", {
        userId: assignee,
        type: NOTIFICATION_TYPES.TASK_ASSIGNED,
        message: `You have been assigned a new task by ${user.name}`,
        read: false,
        handled: false,
        archived: false,
        metadata: {
          link: `/tasks/${taskId}`,
        },
      });
    }

    await ctx.db.insert("activities", {
      createdBy: user._id,
      type: ACTIVITY_TYPES.TASK_CREATED,
      description: `Task added by ${user.name}`,
      metadata: {
        link: `/tasks/${taskId}`,
        objectId: taskId,
      },
    });

    const newTask = await ctx.db.get(taskId);
    if (newTask) {
      try {
        await aggregateTasks.insertIfDoesNotExist(ctx, newTask);
      } catch (error) {
        console.error("Error inserting task into aggregate:", error);
      }
    } else {
      console.error("Error inserting task into aggregate: newTask is null");
    }

    return taskId;
  },
});

/**
 * @name getAll
 * @description Get all tasks for a user, filtered by user role
 * @param favorites - Whether to only return favorite tasks
 */
export const getAll = query({
  args: {
    favorites: v.optional(v.boolean()),
  },
  handler: async (ctx: QueryCtx, args: { favorites: boolean }) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("Not authenticated");
    }

    let tasks = await ctx.db.query("tasks").collect();

    const assigneeIds = [
      ...new Set(
        tasks
          .map((task) => task.assignee)
          .filter((id): id is Id<"users"> => id !== null && id !== undefined),
      ),
    ];

    const assignees = await Promise.all(
      assigneeIds.map(async (id) => {
        const assignee = await ctx.db.get(id);
        if (!assignee) return null;

        return {
          ...assignee,
          status,
        };
      }),
    );

    const assigneesMap = new Map(
      assignees.filter(Boolean).map((assignee) => [assignee!._id, assignee]),
    );

    const relatedIds = tasks
      .filter((task) => task.related && Array.isArray(task.related))
      .flatMap((task) => task.related || []);

    const uniqueRelatedIds = [...new Set(relatedIds)];

    const relatedRecords = await Promise.all(
      uniqueRelatedIds.map(async (id) => {
        const contact = await ctx.db.get(id as Id<"contacts">);
        if (contact) return { type: "contact", ...contact };
        return null;
      }),
    );

    const relatedRecordsMap = new Map(
      relatedRecords.filter(Boolean).map((record) => [record!._id, record]),
    );

    if (args.favorites) {
      const favorites = await ctx.db
        .query("favorites")
        .withIndex("by_user", (q) => q.eq("userId", user._id))
        .collect();

      tasks = tasks.filter((task) =>
        favorites.some((favorite) => favorite.objectId === task._id),
      );
    }

    const populatedTasks = tasks.map((task) => ({
      ...task,
      related:
        task.related && Array.isArray(task.related)
          ? task.related
              .map((id) => relatedRecordsMap.get(id) || null)
              .filter(Boolean)
          : [],
      assignee: task.assignee
        ? assigneesMap.get(task.assignee as Id<"users">) || null
        : null,
    }));

    return populatedTasks;
  },
});

/**
 * @name update
 * @description Update a task
 * @param id - The ID of the task
 * @param title - The title of the task
 * @param description - The description of the task
 * @param dueDate - The due date of the task in epoch time
 * @param status - "backlog", "todo", "in_progress", "review", "done"
 * @param assignee - Id of the user assigned to the task
 * @param related - Id's of contacts related to the task
 * @param priority - "no_priority", "urgent", "high", "medium", "low"
 * @param statusHistory - The history of the task's status
 * @param lastUpdated - The last updated timestamp of the task in epoch time
 * @param position - The position of the task
 */
export const update = mutation({
  args: {
    id: v.id("tasks"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    dueDate: v.optional(v.number()),
    status: v.optional(TASK_STATUS),
    assignee: v.optional(v.union(v.id("users"), v.null())),
    related: v.optional(v.union(v.array(v.id("contacts")), v.null())),
    priority: v.optional(TASK_PRIORITY),
    statusHistory: v.optional(
      v.array(
        v.object({
          status: TASK_STATUS,
          timestamp: v.number(),
          userId: v.id("users"),
        }),
      ),
    ),
    lastUpdated: v.optional(v.number()),
    position: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { id, ...update } = args;

    const oldTask = await ctx.db.get(id);
    if (!oldTask) {
      throw new Error("Task not found");
    }

    if (update.related && update.related !== oldTask.related) {
      await removeRelatedIdFromObject(ctx, id, "tasks", "related");

      if (Array.isArray(update.related)) {
        for (const relatedId of update.related) {
          await addIdToRecordField(ctx, relatedId, "tasks", id);
        }
      }
    }

    await ctx.db.patch(id, update);
    const newTask = await ctx.db.get(id);

    try {
      if (newTask) {
        await aggregateTasks.deleteIfExists(ctx, oldTask);
        await aggregateTasks.insertIfDoesNotExist(ctx, newTask);
      } else {
        console.error("Error updating aggregate tasks: newTask is null");
      }
    } catch (error) {
      console.error("Error updating aggregate tasks:", error);
      throw error;
    }

    return {
      success: true,
      task: newTask,
      message: "Task updated successfully",
    };
  },
});

/**
 * @name delete
 * @description Delete a task
 * @param id - The ID of the task
 */
export const deleteTask = mutation({
  args: { id: v.id("tasks") },
  handler: async (ctx, args) => {
    const { id } = args;

    if (!id) {
      throw new Error("Task ID is required");
    }

    const task = await ctx.db.get(id);
    if (!task) {
      throw new Error(`Task with ID ${id} does not exist`);
    }

    try {
      if (task.assignee) {
        const taskForAggregate = {
          ...task,
          assignee: task.assignee,
          status: task.status || "",
        };
        await aggregateTasks.deleteIfExists(ctx, taskForAggregate);
      }
    } catch (error) {
      console.error("Error deleting task from aggregate:", error);
    }

    await removeRelatedIdFromObject(ctx, id, "tasks", "related");
    await removeFavoritesForRecord(ctx, id);
    await ctx.db.delete(id);

    return { success: true, message: "Task deleted successfully" };
  },
});

/**
 * @name count
 * @description Count the number of tasks
 * @returns number
 */
export const count = query({
  args: {},
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("Not authenticated");
    }

    const tasks = await ctx.db
      .query("tasks")
      .filter((q) => q.eq(q.field("status"), "todo"))
      .collect();

    return tasks.length;
  },
});

/**
 * @name updateBatchTasks
 * @description Update a batch of tasks
 * @param taskIds - The IDs of the tasks
 * @param status - The status of the tasks
 * @param priority - The priority of the tasks
 * @param isDelete - Whether to delete the tasks
 */
export const updateBatchTasks = mutation({
  args: {
    taskIds: v.array(v.id("tasks")),
    status: v.optional(TASK_STATUS),
    assignee: v.optional(v.id("users")),
    priority: v.optional(TASK_PRIORITY),
    isDelete: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) {
      throw new Error("Not authenticated updateBatchTasks");
    }

    for (const taskId of args.taskIds) {
      const task = await ctx.db.get(taskId);

      if (!task) continue;

      if (args.isDelete) {
        await ctx.db.delete(taskId);
        continue;
      }

      const updates: Record<string, string> = {};

      if (args.status) {
        updates.status = args.status;
      }

      if (args.assignee) {
        updates.assignee = args.assignee;
      }

      if (args.priority) {
        updates.priority = args.priority;
      }

      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(taskId, updates);
      }

      await aggregateTasks.deleteIfExists(ctx, task);
      await aggregateTasks.insertIfDoesNotExist(ctx, task);
    }

    return { success: true };
  },
});

/**
 * @name get
 * @description Get a task by ID
 * @param id - The ID of the task
 * @returns The task with populated assignee information including status
 */
export const get = query({
  args: { id: v.id("tasks") },
  handler: async (ctx, args) => {
    const task = await ctx.db.get(args.id);
    if (!task || !task.assignee || typeof task.assignee !== "string")
      return task;

    const assignee = await ctx.db.get(task.assignee as Id<"users">);
    if (!assignee) return task;

    return {
      ...task,
      assignee: {
        ...assignee,
        status,
      },
    };
  },
});

/**
 * @name reorderTasks
 * @description Update positions of multiple tasks
 */
export const reorderTasks = mutation({
  args: {
    updates: v.array(
      v.object({
        taskId: v.id("tasks"),
        newPosition: v.number(),
        newStatus: TASK_STATUS,
      }),
    ),
  },
  async handler(ctx, args) {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const now = Date.now();

    const updatesByStatus = args.updates.reduce(
      (acc, update) => {
        if (!acc[update.newStatus]) {
          acc[update.newStatus] = [];
        }
        acc[update.newStatus]?.push(update);
        return acc;
      },
      {} as Record<string, typeof args.updates>,
    );

    for (const [status, updates] of Object.entries(updatesByStatus)) {
      updates.sort((a, b) => a.newPosition - b.newPosition);

      for (const update of updates) {
        const task = await ctx.db.get(update.taskId);
        if (!task) continue;

        const statusChanged = task.status !== status;
        const statusHistory = statusChanged
          ? [
              ...(task.statusHistory || []),
              {
                status: status as TaskStatus,
                timestamp: now,
                userId: user._id,
              },
            ]
          : undefined;

        await ctx.db.patch(update.taskId, {
          position: update.newPosition,
          status: status as TaskStatus,
          lastUpdated: now,
          statusHistory,
        });

        if (task && statusChanged) {
          try {
            await aggregateTasks.deleteIfExists(ctx, task);
            const updatedTask = await ctx.db.get(update.taskId);
            if (updatedTask) {
              await aggregateTasks.insertIfDoesNotExist(ctx, updatedTask);
            }
          } catch (error) {
            console.error("Error updating aggregate tasks:", error);
          }
        }
      }
    }

    return { success: true };
  },
});

/**
 * @name updateOrder
 * @description Update the order of tasks by adjusting their positions and status
 * @param taskId - The ID of the task being moved
 * @param targetTaskId - The ID of the task to move relative to
 * @param newStatus - The new status of the task (if changed)
 */
export const updateOrder = mutation({
  args: {
    taskId: v.id("tasks"),
    targetTaskId: v.id("tasks"),
    newStatus: TASK_STATUS,
  },
  async handler(ctx, args) {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("Not authenticated");
    }

    const task = await ctx.db.get(args.taskId);
    const targetTask = await ctx.db.get(args.targetTaskId);

    if (!task || !targetTask) {
      throw new Error("Task not found");
    }

    const now = Date.now();
    const statusChanged = task.status !== args.newStatus;

    const tasksInStatus = await ctx.db
      .query("tasks")
      .withIndex("by_status_position", (q) => q.eq("status", args.newStatus))
      .order("asc")
      .collect();

    const targetIndex = tasksInStatus.findIndex(
      (t) => t._id === targetTask._id,
    );
    if (targetIndex === -1) {
      throw new Error("Target task not found in status group");
    }

    const tasks = [...tasksInStatus];
    if (!statusChanged) {
      const taskIndex = tasks.findIndex((t) => t._id === task._id);
      if (taskIndex !== -1) {
        tasks.splice(taskIndex, 1);
      }
    }
    tasks.splice(targetIndex, 0, task);

    const updates = tasks.map((t, index) =>
      ctx.db.patch(t._id, { position: index * 1000 }),
    );

    const statusHistory = statusChanged
      ? [
          ...(task.statusHistory || []),
          {
            status: args.newStatus,
            timestamp: now,
            userId: user._id,
          },
        ]
      : undefined;

    await ctx.db.patch(task._id, {
      status: args.newStatus,
      position: targetIndex * 1000,
      lastUpdated: now,
      ...(statusHistory ? { statusHistory } : {}),
    });

    await Promise.all(updates);

    if (statusChanged) {
      try {
        await aggregateTasks.deleteIfExists(ctx, task);
        const updatedTask = await ctx.db.get(task._id);
        if (updatedTask) {
          await aggregateTasks.insertIfDoesNotExist(ctx, updatedTask);
        }
      } catch (error) {
        console.error("Error updating aggregate tasks:", error);
      }
    }

    return { success: true };
  },
});
