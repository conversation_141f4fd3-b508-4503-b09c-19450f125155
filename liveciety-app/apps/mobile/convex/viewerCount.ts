import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";

/**
 * Real-time viewer count tracking system for mobile
 * 
 * This system tracks viewer counts using multiple approaches:
 * 1. LiveKit participant count (real-time via polling)
 * 2. Database viewer sessions (fallback for accuracy)
 * 3. Webhook updates from LiveKit events
 */

/**
 * Increment viewer count when someone joins a stream
 */
export const incrementViewerCount = mutation({
  args: { 
    streamId: v.id("streams"),
    sessionId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    const sessionId = args.sessionId || `session-${Date.now()}-${Math.random().toString(36).substring(7)}`;
    
    const stream = await ctx.db.get(args.streamId);
    if (!stream || stream.status !== "live") {
      return { success: false, reason: "Stream not live" };
    }

    // Check if this user already has an active session for this stream
    if (userId) {
      const existingSession = await ctx.db
        .query("viewerSessions")
        .withIndex("by_streamId_userId", q => 
          q.eq("streamId", args.streamId).eq("userId", userId)
        )
        .filter(q => q.eq(q.field("isActive"), true))
        .first();
      
      if (existingSession) {
        // Update existing session timestamp
        await ctx.db.patch(existingSession._id, {
          lastSeen: Date.now(),
          sessionId
        });
        return { success: true, reason: "Updated existing session" };
      }
    }

    // Create new viewer session
    await ctx.db.insert("viewerSessions", {
      streamId: args.streamId,
      userId: userId || undefined,
      sessionId,
      joinedAt: Date.now(),
      lastSeen: Date.now(),
      isActive: true,
      userAgent: "React Native Client",
    });

    return { success: true, reason: "Created new session" };
  },
});

/**
 * Decrement viewer count when someone leaves a stream
 */
export const decrementViewerCount = mutation({
  args: { 
    streamId: v.id("streams"),
    sessionId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    
    if (args.sessionId) {
      // Find by session ID
      const session = await ctx.db
        .query("viewerSessions")
        .withIndex("by_sessionId", q => q.eq("sessionId", args.sessionId!))
        .filter(q => q.and(
          q.eq(q.field("streamId"), args.streamId),
          q.eq(q.field("isActive"), true)
        ))
        .first();
      
      if (session) {
        await ctx.db.patch(session._id, {
          isActive: false,
          leftAt: Date.now(),
          lastSeen: Date.now(),
        });
        return { success: true, reason: "Deactivated session by sessionId" };
      }
    }
    
    if (userId) {
      // Find by user ID as fallback
      const session = await ctx.db
        .query("viewerSessions")
        .withIndex("by_streamId_userId", q => 
          q.eq("streamId", args.streamId).eq("userId", userId)
        )
        .filter(q => q.eq(q.field("isActive"), true))
        .first();
      
      if (session) {
        await ctx.db.patch(session._id, {
          isActive: false,
          leftAt: Date.now(),
          lastSeen: Date.now(),
        });
        return { success: true, reason: "Deactivated session by userId" };
      }
    }

    return { success: false, reason: "No active session found" };
  },
});

/**
 * Update viewer activity (heartbeat to keep session alive)
 */
export const updateViewerActivity = mutation({
  args: {
    streamId: v.id("streams"),
    sessionId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    
    let session = null;
    
    if (args.sessionId) {
      session = await ctx.db
        .query("viewerSessions")
        .withIndex("by_sessionId", q => q.eq("sessionId", args.sessionId!))
        .filter(q => q.and(
          q.eq(q.field("streamId"), args.streamId),
          q.eq(q.field("isActive"), true)
        ))
        .first();
    }
    
    if (!session && userId) {
      session = await ctx.db
        .query("viewerSessions")
        .withIndex("by_streamId_userId", q => 
          q.eq("streamId", args.streamId).eq("userId", userId)
        )
        .filter(q => q.eq(q.field("isActive"), true))
        .first();
    }
    
    if (session) {
      await ctx.db.patch(session._id, {
        lastSeen: Date.now(),
      });
      return { success: true };
    }
    
    return { success: false };
  },
});

/**
 * Get current viewer count for a stream
 */
export const getViewerCount = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    const stream = await ctx.db.get(args.streamId);
    if (!stream) return 0;

    // Only count viewers for live streams
    if (stream.status !== "live") return 0;

    // Count active sessions, excluding sessions older than 2 minutes (stale connections)
    const twoMinutesAgo = Date.now() - (2 * 60 * 1000);
    
    const activeSessions = await ctx.db
      .query("viewerSessions")
      .withIndex("by_streamId", q => q.eq("streamId", args.streamId))
      .filter(q => q.and(
        q.eq(q.field("isActive"), true),
        q.gte(q.field("lastSeen"), twoMinutesAgo)
      ))
      .collect();

    // Don't count the host as a viewer
    const viewerSessions = activeSessions.filter(session => 
      session.userId !== stream.hostId
    );

    return viewerSessions.length;
  },
});

/**
 * Cleanup stale viewer sessions (called periodically)
 */
export const cleanupStaleViewerSessions = mutation({
  args: { 
    streamId: v.optional(v.id("streams")),
    olderThanMinutes: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const olderThan = args.olderThanMinutes || 5; // Default 5 minutes
    const cutoff = Date.now() - (olderThan * 60 * 1000);
    
    let staleSessions;
    
    if (args.streamId) {
      // Cleanup for specific stream
      staleSessions = await ctx.db
        .query("viewerSessions")
        .withIndex("by_streamId", q => q.eq("streamId", args.streamId!))
        .filter(q => q.and(
          q.eq(q.field("isActive"), true),
          q.lt(q.field("lastSeen"), cutoff)
        ))
        .collect();
    } else {
      // Cleanup all stale sessions
      staleSessions = await ctx.db
        .query("viewerSessions")
        .filter(q => q.and(
          q.eq(q.field("isActive"), true),
          q.lt(q.field("lastSeen"), cutoff)
        ))
        .collect();
    }

    for (const session of staleSessions) {
      await ctx.db.patch(session._id, {
        isActive: false,
        leftAt: Date.now(),
      });
    }

    return { cleanedUp: staleSessions.length };
  },
});

/**
 * Get viewer statistics for a stream
 */
export const getViewerStats = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    const stream = await ctx.db.get(args.streamId);
    if (!stream) return null;

    const allSessions = await ctx.db
      .query("viewerSessions")
      .withIndex("by_streamId", q => q.eq("streamId", args.streamId))
      .collect();

    const currentViewers = allSessions.filter(s => 
      s.isActive && 
      s.userId !== stream.hostId &&
      s.lastSeen > Date.now() - (2 * 60 * 1000) // Active in last 2 minutes
    ).length;

    const totalUniqueViewers = new Set(
      allSessions
        .filter(s => s.userId !== stream.hostId)
        .map(s => s.userId || s.sessionId)
    ).size;

    const peakViewers = Math.max(currentViewers, stream.peakViewers || 0);

    return {
      current: currentViewers,
      peak: peakViewers,
      totalUnique: totalUniqueViewers,
      isLive: stream.status === "live",
    };
  },
}); 