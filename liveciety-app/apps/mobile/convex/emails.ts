import { v } from "convex/values";
import { internalAction } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { api, internal } from "./_generated/api";
import { sendEmail, sendTransactionalEmail } from "./lib/emailService";

/**
 * @name sendSellerInterestEmail
 * @description Send an email notification about a new seller interest submission using Resend
 */
export const sendSellerInterestEmail = internalAction({
  args: {
    sellerId: v.id("interestedSellers"),
    email: v.string(),
    hasSellingExperience: v.boolean(),
    platform: v.optional(v.string()),
    platformLink: v.optional(v.string()),
    monthlyRevenue: v.optional(v.string()),
    primaryGoal: v.optional(v.string()),
    socialMedia: v.optional(v.array(
      v.object({
        platform: v.string(),
        username: v.optional(v.string()),
      })
    )),
    additionalInfo: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Format social media information
    const socialMediaText = args.socialMedia 
      ? args.socialMedia
          .filter(item => item.platform !== "na")
          .map(item => `${item.platform}: ${item.username || "Not provided"}`)
          .join("\n") 
      : "None";

    const socialMediaHtml = args.socialMedia && args.socialMedia.length > 0 && !args.socialMedia.some(item => item.platform === "na")
      ? `<ul>
          ${args.socialMedia
            .filter(item => item.platform !== "na")
            .map(item => `<li><strong>${item.platform}:</strong> ${item.username || "Not provided"}</li>`)
            .join("")}
        </ul>`
      : "<p>None</p>";

    // Prepare email subject and body
    const subject = `New Seller Interest Form Submission`;
    
    // Plain text email
    const body = `
New seller interest form submission:

Seller ID: ${args.sellerId}
Email: ${args.email}
Has selling experience: ${args.hasSellingExperience ? "Yes" : "No"}
${args.hasSellingExperience && args.platform ? `Platform: ${args.platform}` : ""}
${args.hasSellingExperience && args.monthlyRevenue ? `Monthly revenue: ${args.monthlyRevenue}` : ""}
${args.primaryGoal ? `Primary goal: ${args.primaryGoal}` : ""}

Social Media:
${socialMediaText}

${args.additionalInfo ? `Additional information:\n${args.additionalInfo}` : "No additional information provided."}

Submitted at: ${new Date().toISOString()}
`;

    // HTML formatted email
    const html = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    h1 { color: #2563eb; margin-bottom: 20px; }
    h2 { color: #4b5563; margin-top: 25px; margin-bottom: 10px; }
    .info-section { margin-bottom: 25px; }
    .info-item { margin-bottom: 10px; }
    .label { font-weight: bold; display: inline-block; margin-right: 5px; }
    .note { background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin-top: 20px; }
    .footer { margin-top: 30px; font-size: 12px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 15px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>New Seller Interest Form Submission</h1>
    
    <div class="info-section">
      <div class="info-item"><span class="label">Seller ID:</span> ${args.sellerId}</div>
      <div class="info-item"><span class="label">Email:</span> ${args.email}</div>
      <div class="info-item"><span class="label">Has selling experience:</span> ${args.hasSellingExperience ? "Yes" : "No"}</div>
      ${args.hasSellingExperience && args.platform ? `<div class="info-item"><span class="label">Platform:</span> ${args.platform}</div>` : ""}
      ${args.hasSellingExperience && args.monthlyRevenue ? `<div class="info-item"><span class="label">Monthly revenue:</span> ${args.monthlyRevenue}</div>` : ""}
      ${args.primaryGoal ? `<div class="info-item"><span class="label">Primary goal:</span> ${args.primaryGoal}</div>` : ""}
    </div>
    
    <h2>Social Media</h2>
    ${socialMediaHtml}
    
    <h2>Additional Information</h2>
    <div class="info-section">
      ${args.additionalInfo ? `<p>${args.additionalInfo.replace(/\n/g, '<br>')}</p>` : "<p>No additional information provided.</p>"}
    </div>
    
    <div class="note">
      <p>Submitted at: ${new Date().toLocaleString()}</p>
    </div>
    
    <div class="footer">
      <p>This is an automated notification from Liveciety. Please do not reply directly to this email.</p>
    </div>
  </div>
</body>
</html>
`;

    try {
      // Send the email
      await sendEmail(
        "<EMAIL>", 
        subject, 
        body,
        args.email, // Reply-to address
        html // HTML version
      );
      return { success: true };
    } catch (error) {
      console.error("Failed to send seller interest email:", error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error" 
      };
    }
  },
});

/**
 * @name sendSellerInterestLoopsEmail
 * @description Send an email notification about a new seller interest submission using Loops.so
 */
export const sendSellerInterestLoopsEmail = internalAction({
  args: {
    sellerId: v.id("interestedSellers"),
    email: v.string(),
    hasSellingExperience: v.boolean(),
    platform: v.optional(v.string()),
    platformLink: v.optional(v.string()),
    monthlyRevenue: v.optional(v.string()),
    primaryGoal: v.optional(v.string()),
    socialMedia: v.optional(v.array(
      v.object({
        platform: v.string(),
        username: v.optional(v.string()),
      })
    )),
    additionalInfo: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.runQuery(api.users.viewer);

    const formattedSocialMedia = args.socialMedia 
      ? args.socialMedia
          .filter(item => item.platform !== "na")
          .map(item => ({ 
            platform: item.platform,
            username: item.username || "Not provided"
          }))
      : [];

    // Prepare data for Loops template
    const userData = {
      sellerId: args.sellerId,
      userEmail: args.email,
      hasSellingExperience: args.hasSellingExperience ? "Yes" : "No",
      platform: args.hasSellingExperience && args.platform ? args.platform : "N/A",
      platformLink: args.hasSellingExperience && args.platformLink ? args.platformLink : "N/A",
      monthlyRevenue: args.hasSellingExperience && args.monthlyRevenue ? args.monthlyRevenue : "N/A",
      primaryGoal: args.primaryGoal || "Not specified",
      socialMediaPlatforms: formattedSocialMedia.length > 0 ? 
        formattedSocialMedia.map(item => `${item.platform}: ${item.username}`).join(", ") : 
        "None",
      additionalInfo: args.additionalInfo || "No additional information provided",
      submittedAt: new Date().toLocaleString(),
      userId: user?._id
    };

    try {
      // Use your Loops transactional email ID here
      // You'll need to create this template in Loops.so
      const LOOPS_TEMPLATE_ID = "cmam9xgcc3cvr666omih0u1a3";
      
      // Send the transactional email
      await sendTransactionalEmail(
        "<EMAIL>", 
        LOOPS_TEMPLATE_ID,
        userData
      );
      
      return { success: true };
    } catch (error) {
      console.error("Failed to send seller interest email via Loops:", error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error" 
      };
    }
  },
});

/**
 * @name sendPasswordResetLoopsEmail
 * @description Send a password reset email using Loops.so transactional email
 */
export const sendPasswordResetLoopsEmail = internalAction({
  args: {
    email: v.string(),
    code: v.string(),
    name: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Prepare data for Loops template
    const userData = {
      email: args.email,
      code: args.code,
      name: args.name || "",
    };
    try {
      // Use your Loops transactional email ID for password reset
      const LOOPS_RESET_TEMPLATE_ID = "cmam9xgcc3cvr666omih0u1a3";
      await sendTransactionalEmail(
        args.email,
        LOOPS_RESET_TEMPLATE_ID,
        userData
      );
      return { success: true };
    } catch (error) {
      console.error("Failed to send password reset email via Loops:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
}); 