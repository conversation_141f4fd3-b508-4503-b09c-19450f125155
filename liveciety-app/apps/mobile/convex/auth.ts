import <PERSON> from "@auth/core/providers/google";
import { Password } from "@convex-dev/auth/providers/Password";
import { convexAuth } from "@convex-dev/auth/server";
import { ExtendedProfile, LoginType } from "./lib/types";
import { generateColor, generateUniqueCode } from "./helpers/utils";
import { hashPassword, verifyPassword } from "./helpers/scrypt";
import { validateUsername } from "../lib/profanity";
import { LoopsPasswordReset } from "./email/helpers/reset";
import { aggregateUsers } from "./custom";
import { MutationCtx, internalAction, internalMutation, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
const PasswordProvider = Password({
  crypto: {
    hashSecret: hashPassword,
    verifySecret: verifyPassword,
  },
  validatePasswordRequirements: (password: string) => {
    if (password.length < 8) {
      throw new Error("Password must be at least 8 characters long");
    }
  },
  profile: (params: any) => {
    const email = params.email as string;
    return {
      email,
      image: "",
      loginType: params.loginType as string,
      username: params.username || undefined,
      firstName: params.firstName || undefined,
      lastName: params.lastName || undefined,
      phone: params.phone || undefined,
    };
  },
  reset: LoopsPasswordReset,
});

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
        },
      },
      profile: (profile: any) => {
        console.log("Google OAuth profile received:", profile);
        return {
          id: profile.sub,
          email: profile.email,
          name: profile.name,
          image: profile.picture,
          firstName: profile.given_name,
          lastName: profile.family_name,
          emailVerified: profile.email_verified,
        };
      },
    }),
    PasswordProvider,
  ],
  callbacks: {
    async createOrUpdateUser(ctx: any, args: any) {
      const profile = args.profile;
      const type = args.type;

      if (!profile.email) {
        throw new Error("Email is required");
      }

      const existingUserByEmail = await ctx.db
        .query("users")
        .filter((q: any) => q.eq(q.field("email"), profile.email))
        .first();

      if (existingUserByEmail) {
        await ctx.db.patch(existingUserByEmail._id, {
          lastLoginType: ["password", "oauth", "magiclink", "credentials"].includes(type) ? type : "password",
          image: profile.image || existingUserByEmail.image,
          name: profile.name || existingUserByEmail.name,
          firstName:
            profile.name?.split(" ")[0] || existingUserByEmail.firstName,
          lastName: profile.name?.split(" ")[1] || existingUserByEmail.lastName,
        });
        return existingUserByEmail._id;
      }

      const color = generateColor();
      
      // Generate username from email or provided username
      let desiredUsername;
      if (profile.username) {
        // If username is provided in the profile (from signUp form)
        desiredUsername = profile.username.toLowerCase();
      } else {
        // Default to email prefix
        desiredUsername = profile.email?.split("@")[0]?.toLowerCase() || "";
      }
      
      // Validate the username format
      const validationResult = validateUsername(desiredUsername);
      if (!validationResult.isValid) {
        throw new Error(`Invalid username: ${validationResult.error}`);
      }
      
      // Check if the username is already taken
      const existingUserByUsername = await ctx.db
        .query("users")
        .filter((q: any) => q.eq(q.field("username"), desiredUsername))
        .first();
      
      // If username is taken, generate a unique username
      let finalUsername = desiredUsername;
      if (existingUserByUsername) {
        // Append a random number to make it unique
        finalUsername = `${desiredUsername}${Math.floor(Math.random() * 10000)}`;
        
        // Make sure even the new username is unique (check again)
        const stillExists = await ctx.db
          .query("users")
          .filter((q: any) => q.eq(q.field("username"), finalUsername))
          .first();
        
        if (stillExists) {
          // Try with a different random number
          finalUsername = `${desiredUsername}${Math.floor(Math.random() * 1000000)}`;
        }
      }

      const userId = await ctx.db.insert("users", {
        email: profile.email,
        image: profile.image || "",
        name: profile.name || `${profile.firstName} ${profile.lastName}` || finalUsername,
        firstName: profile.firstName || profile.name?.split(" ")[0] || finalUsername,
        lastName: profile.lastName || profile.name?.split(" ")[1] || "",
        username: finalUsername,
        color: color,
        lastLoginType: ["password", "oauth", "magiclink", "credentials"].includes(type) ? type : "password",
        role: "user",
        phone: profile.phone || "",
        finishedSignUp: false,
      });

      await aggregateUsers.insertIfDoesNotExist(ctx, {
        _id: userId,
        _creationTime: Date.now(),
        email: profile.email,
        image: profile.image || "",
        name: profile.name || `${profile.firstName} ${profile.lastName}` || finalUsername,
        firstName: profile.firstName || profile.name?.split(" ")[0] || finalUsername,
        lastName: profile.lastName || profile.name?.split(" ")[1] || "",
        username: finalUsername,
        color: color,
        lastLoginType: ["password", "oauth", "magiclink", "credentials"].includes(type) ? type : "password",
        role: "user",
        phone: profile.phone || "",
        finishedSignUp: false,
      });

      return userId;
    },
  },
});

/**
 * Internal action to handle mobile OAuth completion
 */
export const createOrUpdateUserFromProfile = internalAction({
  args: {
    profile: v.object({
      email: v.string(),
      name: v.optional(v.string()),
      image: v.optional(v.string()),
      loginType: v.string(),
    }),
    type: v.string(),
  },
  handler: async (ctx, args) => {
    // This will use the existing createOrUpdateUser callback
    const callbacks = (convexAuth as any).callbacks || {};
    if (callbacks.createOrUpdateUser) {
      return await callbacks.createOrUpdateUser(ctx, {
        profile: args.profile,
        type: args.type,
      });
    }
    throw new Error("createOrUpdateUser callback not found");
  },
});

/**
 * Internal mutation to create a mobile user
 */
export const createMobileUser = internalMutation({
  args: {
    email: v.string(),
    name: v.optional(v.string()),
    image: v.optional(v.string()),
    loginType: v.string(),
  },
  handler: async (ctx: MutationCtx, args: any): Promise<Id<"users">> => {
    console.log("Creating/updating mobile user:", args.email);

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .filter((q: any) => q.eq(q.field("email"), args.email))
      .first();

    if (existingUser) {
      console.log("Updating existing user:", existingUser._id);
      // Update existing user
      await ctx.db.patch(existingUser._id, {
        lastLoginType: args.loginType,
        image: args.image || existingUser.image,
        name: args.name || existingUser.name,
      });
      return existingUser._id;
    }

    // Create new user
    const color = generateColor();
    const desiredUsername = args.email.split("@")[0]?.toLowerCase() || "";
    
    // Validate the username format
    const validationResult = validateUsername(desiredUsername);
    if (!validationResult.isValid) {
      throw new Error(`Invalid username: ${validationResult.error}`);
    }
    
    // Check if the username is already taken
    const existingUserByUsername = await ctx.db
      .query("users")
      .filter((q: any) => q.eq(q.field("username"), desiredUsername))
      .first();
    
    // If username is taken, generate a unique username
    let finalUsername = desiredUsername;
    if (existingUserByUsername) {
      finalUsername = `${desiredUsername}${Math.floor(Math.random() * 10000)}`;
    }

    const userId = await ctx.db.insert("users", {
      email: args.email,
      image: args.image || "",
      name: args.name || finalUsername,
      firstName: args.name?.split(" ")[0] || finalUsername,
      lastName: args.name?.split(" ")[1] || "",
      username: finalUsername,
      color: color,
      lastLoginType: args.loginType,
      role: "user",
      phone: "",
      finishedSignUp: false,
    });

    console.log("Created new mobile user:", userId);
    return userId;
  },
});

/**
 * Internal mutation to create a mobile auth session
 */
export const createMobileAuthSession = internalMutation({
  args: {
    tempCode: v.string(),
    userId: v.id("users"),
    email: v.string(),
    expiresAt: v.number(),
  },
  handler: async (ctx: MutationCtx, args: any) => {
    console.log("Creating mobile auth session for user:", args.userId);

    return await ctx.db.insert("mobileAuthSessions", {
      tempCode: args.tempCode,
      userId: args.userId,
      email: args.email,
      expiresAt: args.expiresAt,
      used: false,
    });
  },
});

/**
 * Exchange temporary mobile auth code for a JWT token that can be used by the client
 */
export const exchangeMobileAuthCode = mutation({
  args: {
    tempCode: v.string(),
  },
  handler: async (ctx: MutationCtx, args: any) => {
    // Find and validate the temporary auth session
    const authSession = await ctx.db
      .query("mobileAuthSessions")
      .filter((q: any) => q.eq(q.field("tempCode"), args.tempCode))
      .first();

    if (!authSession) {
      throw new Error("Invalid or expired auth code");
    }

    if (authSession.used) {
      throw new Error("Auth code has already been used");
    }

    if (authSession.expiresAt < Date.now()) {
      throw new Error("Auth code has expired");
    }

    // Mark the session as used
    await ctx.db.patch(authSession._id, { used: true });

    // Get the user - cast string to proper Id type
    const userId = authSession.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }

    console.log("Found user for auth session:", user._id, user.email);

    // Instead of manually creating sessions, create the auth account entry
    // Check if an auth account already exists for this user
    const existingAuthAccount = await ctx.db
      .query("authAccounts")
      .filter((q: any) => q.eq(q.field("userId"), userId))
      .filter((q: any) => q.eq(q.field("provider"), "google"))
      .first();

    if (!existingAuthAccount) {
      console.log("Creating auth account entry for Google OAuth");
      // Create auth account entry for Google OAuth
      await ctx.db.insert("authAccounts", {
        userId: userId,
        provider: "google",
        providerAccountId: user.email!, // Use email as provider ID for mobile OAuth
      });
    } else {
      console.log("Auth account already exists for user");
    }

    return {
      success: true,
      user: {
        _id: user._id,
        email: user.email!,
        name: user.name!,
        image: user.image!,
        username: user.username!,
      },
    };
  },
});

