// File: liveciety-app/apps/mobile/convex/oauth.ts

import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { auth } from "./auth";
import { getAuthUserId } from "@convex-dev/auth/server";

/**
 * Validates an OAuth session token and returns user information
 */
export const signInWithOAuthSession = mutation({
  args: {
    sessionToken: v.string(),
  },
  returns: v.object({
    sessionValid: v.boolean(),
    userId: v.optional(v.string()),
  }),
  handler: async (ctx, args) => {
    try {
      // Get the user ID from the session token
      const userId = await getAuthUserId(ctx);
      
      if (userId) {
        return {
          sessionValid: true,
          userId: userId,
        };
      }
      
      return {
        sessionValid: false,
      };
    } catch (error) {
      console.error("Failed to validate OAuth session:", error);
      return {
        sessionValid: false,
      };
    }
  },
});