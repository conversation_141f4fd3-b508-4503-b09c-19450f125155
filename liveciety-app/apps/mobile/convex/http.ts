import { httpRouter } from "convex/server";
import { auth, store, signIn } from "./auth";
import { httpAction } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { twilio } from "./integration/twilio";
import <PERSON><PERSON> from "stripe";
import { ERRORS } from "./lib/errors";
import { api, internal } from "./_generated/api";

const http = httpRouter();

auth.addHttpRoutes(http);

/**
 * @name mobileOAuthCallback  
 * @description Handles mobile-specific OAuth callback by creating a temporary session and redirecting to mobile app
 * @method GET
 * @route /api/auth/mobile/callback/google
 */
http.route({
  path: "/api/auth/mobile/callback/google",
  method: "GET", 
  handler: httpAction(async (ctx, request) => {
    const url = new URL(request.url);
    const code = url.searchParams.get("code");
    const state = url.searchParams.get("state");
    const error = url.searchParams.get("error");
    
    console.log("Mobile OAuth callback received:", { 
      hasCode: !!code, 
      hasState: !!state,
      error,
      isMobileRequest: state?.startsWith("mobile_app_") 
    });
    
    if (error) {
      console.error("OAuth error:", error);
      const errorUrl = `liveciety://oauth/error?error=${encodeURIComponent(error)}`;
      return createRedirectResponse(errorUrl, "Authentication cancelled");
    }
    
    if (!code) {
      console.error("No code in OAuth callback");
      const errorUrl = `liveciety://oauth/error?error=no_code`;
      return createRedirectResponse(errorUrl, "Authentication failed: No code provided");
    }

        try {
      console.log("Processing OAuth code for mobile app...");
      
      // Exchange the code for user information using Google's OAuth API
      const clientId = process.env.GOOGLE_CLIENT_ID;
      const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
      const redirectUri = url.origin + url.pathname;
      
      // Exchange code for access token
      const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          client_id: clientId!,
          client_secret: clientSecret!,
          code: code,
          grant_type: "authorization_code",
          redirect_uri: redirectUri,
        }),
      });
      
      if (!tokenResponse.ok) {
        throw new Error(`Token exchange failed: ${tokenResponse.status}`);
      }
      
      const tokenData = await tokenResponse.json();
      
      // Get user info from Google
      const userResponse = await fetch("https://www.googleapis.com/oauth2/v2/userinfo", {
        headers: {
          Authorization: `Bearer ${tokenData.access_token}`,
        },
      });
      
      if (!userResponse.ok) {
        throw new Error(`User info fetch failed: ${userResponse.status}`);
      }
      
      const userInfo = await userResponse.json();
      console.log("Google user info retrieved:", { email: userInfo.email, name: userInfo.name });
      
      // Create or update user using our existing auth system
      const profile = {
        email: userInfo.email,
        name: userInfo.name,
        image: userInfo.picture,
        loginType: "oauth",
      };
      
      // Create or update user and let the mobile app handle authentication
      console.log("Creating/updating user in database...");
      
      // Use our internal action to create/update the user
      const userId = await ctx.runMutation(internal.auth.createMobileUser, {
        email: userInfo.email,
        name: userInfo.name,
        image: userInfo.picture,
        loginType: "oauth",
      });
      
      if (!userId) {
        throw new Error("Failed to create or update user");
      }
      
      console.log("User created/updated successfully:", userId);
      
      // Create a temporary authentication code that the mobile app can use
      const authCode = `mobile_auth_${Date.now()}_${Math.random().toString(36).substring(7)}`;
      
      // Store the auth code temporarily
      await ctx.runMutation(internal.auth.createMobileAuthSession, {
        tempCode: authCode,
        userId: userId,
        email: userInfo.email,
        expiresAt: Date.now() + (5 * 60 * 1000), // 5 minutes
      });
      
      console.log("Temporary auth code created:", authCode);
      
      // Redirect to mobile app with auth code
      const successUrl = `liveciety://oauth/success?authCode=${encodeURIComponent(authCode)}&email=${encodeURIComponent(userInfo.email)}`;
      console.log("Redirecting to success URL:", successUrl);
      return createRedirectResponse(successUrl, "Authentication successful");
      
    } catch (error) {
      console.error("OAuth flow error:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      const errorUrl = `liveciety://oauth/error?error=${encodeURIComponent(errorMessage)}`;
      return createRedirectResponse(errorUrl, `Authentication failed: ${errorMessage}`);
    }
  }),
});

function createRedirectResponse(redirectUrl: string, message: string) {
  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Redirecting to app...</title>
        <script>
          window.location.href = "${redirectUrl}";
          setTimeout(function() {
            document.getElementById('manual-link').style.display = 'block';
          }, 2000);
        </script>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #18181B;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
          }
          .loader {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          #manual-link {
            display: none;
            margin-top: 20px;
          }
          a {
            color: #3b82f6;
            text-decoration: none;
          }
          a:hover {
            text-decoration: underline;
          }
        </style>
      </head>
      <body>
        <div class="loader"></div>
        <h2>${message}</h2>
        <p>You should be automatically redirected to the app.</p>
        <div id="manual-link">
          <p>If you're not redirected automatically, click the button below:</p>
          <a href="${redirectUrl}" style="display: inline-block; background-color: #3b82f6; color: white; padding: 12px 24px; border-radius: 6px; font-weight: bold;">
            Open Liveciety App
          </a>
        </div>
      </body>
    </html>
  `;
  
  return new Response(htmlContent, {
    status: 200,
    headers: {
      "Content-Type": "text/html",
    },
  });
}

/**
 * @name registerTwilioRoutes
 * @description Registers Twilio routes.
 */
twilio.registerRoutes(http);

/**
 * @name twilioDeliveryStatus
 * @description Handles Twilio message delivery status callbacks.
 * @method POST
 * @route /twilio/delivery-status
 */
http.route({
  path: "/twilio/sms/delivery-status",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const formData = await request.formData();

    return new Response("Status received", { status: 200 });
  }),
});

/**
 * @name getImage
 * @description Handles getting an image from the storage bucket.
 * @method GET
 * @route /getImage
 */
http.route({
  path: "/getImage",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const { searchParams } = new URL(request.url);
    const rawStorageId = searchParams.get("storageId");

    if (!rawStorageId) {
      return new Response("Missing storageId parameter", {
        status: 400,
      });
    }

    const storageId = rawStorageId.includes("?storageId=")
      ? rawStorageId.split("?storageId=")[1]
      : rawStorageId;

    const blob = await ctx.storage.get(storageId as Id<"_storage">);

    if (blob === null) {
      return new Response("Image not found", {
        status: 404,
      });
    }

    return new Response(blob);
  }),
});

async function getStripeEvent(request: Request) {
  if (!process.env.STRIPE_WEBHOOK_SECRET) {
    console.error("Stripe webhook secret is not configured");
    throw new Error(`Stripe - ${ERRORS.ENVS_NOT_INITIALIZED}`);
  }
  try {
    const signature = request.headers.get("Stripe-Signature");
    if (!signature) {
      console.error("No Stripe signature found in request headers");
      throw new Error(ERRORS.STRIPE_MISSING_SIGNATURE);
    }
    
    const clonedRequest = request.clone();
    const payload = await clonedRequest.text();
    
    try {
      const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
        apiVersion: "2025-05-28.basil",
      });
      
      const event = await stripe.webhooks.constructEventAsync(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );
      
      return event;
    } catch (err) {
      console.error("Failed to construct Stripe event:", err);
      throw new Error(
        err instanceof Error ? err.message : ERRORS.STRIPE_SOMETHING_WENT_WRONG,
      );
    }
  } catch (err: unknown) {
    console.error("Error in getStripeEvent:", err);
    throw err instanceof Error
      ? err
      : new Error(ERRORS.STRIPE_SOMETHING_WENT_WRONG);
  }
}

/**
 * @name stripeWebhook
 * @description Handles Stripe webhooks.
 * @method POST
 * @route /stripe
 */
http.route({
  path: "/stripe",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    let event;
    try {
      const isDev = process.env.NODE_ENV === "development";
      
      if (isDev) {
        const payload = await request.clone().text();
        try {
          event = JSON.parse(payload);
        } catch (err) {
          console.error("Failed to parse webhook payload:", err);
          return new Response("Invalid webhook payload", { status: 400 });
        }
      } else {
        event = await getStripeEvent(request);
      }
    } catch (err) {
      console.error("Webhook Error:", err);
      return new Response("Webhook Error", { status: 400 });
    }

    try {
      
      switch (event.type) {
        case 'setup_intent.succeeded':
          const setupIntent = event.data.object;
          break;
        case 'payment_method.attached':
          const paymentMethod = event.data.object;
          break;
        case 'payment_method.detached':
          break;
        default:
          console.error(`Unhandled event type ${event.type}`);
      }
      
      return new Response("OK", { status: 200 });
    } catch (error) {
      console.error("Error processing webhook:", error);
      return new Response("Error processing webhook", { status: 500 });
    }
  }),
});

/**
 * @name livekit
 * @description Handles LiveKit webhooks.
 * @method POST
 * @route /livekit
 */
http.route({
  path: "/livekit",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const authorization = request.headers.get("Authorization");
    if (!authorization) {
      return new Response("Error occurred -- no authorization headers", { status: 400 });
    }
    const body = await request.text();
    const result = await ctx.runAction(api.actions.livekit.handleLivekitWebhook, {
      body,
      authorization,
    });
    if (result.error) {
      return new Response(result.error, { status: 400 });
    }
    return new Response("Success!", { status: 200 });
  }),
});

/**
 * @name api-livekit
 * @description Handles LiveKit webhooks at /api/livekit path.
 * @method POST
 * @route /api/livekit
 */
http.route({
  path: "/api/livekit",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const authorization = request.headers.get("Authorization");
    if (!authorization) {
      return new Response("Error occurred -- no authorization headers", { status: 400 });
    }
    const body = await request.text();
    const result = await ctx.runAction(api.actions.livekit.handleLivekitWebhook, {
      body,
      authorization,
    });
    if (result.error) {
      return new Response(result.error, { status: 400 });
    }
    return new Response("Success!", { status: 200 });
  }),
});

export default http;
