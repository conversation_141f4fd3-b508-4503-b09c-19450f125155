import type { Id } from "./_generated/dataModel";
import { TableAggregate } from "@convex-dev/aggregate";
import { components } from "./_generated/api";
import { DataModel } from "./_generated/dataModel";
import { Triggers } from "convex-helpers/server/triggers";
import { MutationCtx } from "./_generated/server";
import { query } from "./_generated/server";

const triggers = new Triggers<DataModel, MutationCtx>();

/**
 * @name aggregateUsers
 * @description Aggregate users by workspace, assignee, and status
 * This uses the TableAggregate from @convex-dev/aggregate to efficiently group and query users
 */
export const aggregateUsers = new TableAggregate<{
  Key: [Id<"users">];
  DataModel: DataModel;
  TableName: "users";
}>(components.aggregateUsers, {
  sortKey: (doc) => {
    if (!doc._id) {
      console.warn(`User missing _id, skipping aggregation`, doc);
      throw new Error(`User missing _id, skipping aggregation`);
    }
    return [doc._id];
  },
});

/**
 * @name aggregateFollows
 * @description Aggregate follows to track follower and following counts for users
 * This allows efficient querying of follower/following counts for user profiles
 */
export const aggregateFollows = new TableAggregate<{
  Key: [Id<"users">, "follower" | "following"];
  DataModel: DataModel;
  TableName: "follows";
}>(components.aggregateFollows, {
  sortKey: (doc) => {
    // Create a single key for each follow relationship based on the relationship type
    if (doc.followingId && doc.followerId) {
      // This is type assertion to fix TypeScript errors
      // In a real implementation, we would need to update the components
      return [doc.followingId as Id<"users">, "follower"];
    }
    return [doc.followerId as Id<"users">, "following"];
  }
});

/**
 * @name aggregateTasks
 * @description Aggregate tasks by workspace, assignee, and status
 * This uses the TableAggregate from @convex-dev/aggregate to efficiently group and query tasks
 */
export const aggregateTasks = new TableAggregate<{
  Key: [Id<"users">, string];
  DataModel: DataModel;
  TableName: "tasks";
}>(components.aggregateTasks, {
  sortKey: (doc) => {
    if (!doc.assignee) {
      throw new Error(
        `Task ${doc._id} is missing required fields for aggregation`,
      );
    }

    return [doc.assignee as Id<"users">, doc.status || ""];
  },
});

// Register triggers for the aggregate users
triggers.register("users", aggregateUsers.trigger());
triggers.register("tasks", aggregateTasks.trigger());
triggers.register("follows", aggregateFollows.trigger());
