import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";
import { CURRENCY_VALIDATOR, PRODUCT_CONDITION, PRODUCT_VISIBILITY } from "./lib/validators";

/**
 * @name becomeSeller
 * @description Convert a regular user to a seller
 */
export const becomeSeller = mutation({
  args: {
    username: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const existingSeller = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("username"), args.username))
      .first();

    if (existingSeller) {
      throw new Error("Store name already taken");
    }

    await ctx.db.patch(user._id, {
      role: "seller",
      username: args.username,

    });
  },
});

/**
 * @name addProduct
 * @description Add a product that can be sold during streams
 */
export const addProduct = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    price: v.number(),
    currency: CURRENCY_VALIDATOR,
    inventory: v.number(),
    category: v.string(),
    condition: PRODUCT_CONDITION,
    images: v.array(v.id("_storage")),
    streamId: v.optional(v.array(v.id("streams"))),
    subcategory: v.optional(v.string()),
    flashSale: v.optional(v.boolean()),
    acceptOffers: v.optional(v.boolean()),
    reserveForLive: v.optional(v.boolean()),
    shippingProfile: v.optional(v.string()),
    hasHazardousMaterials: v.optional(v.boolean()),
    variants: v.optional(
      v.array(
        v.object({
          color: v.optional(v.string()),
          size: v.optional(v.string()),
          quantity: v.optional(v.number()),
        }),
      ),
    ),
    quantity: v.optional(v.number()),
    isAuction: v.optional(v.boolean()),
    startingBid: v.optional(v.number()),
    suddenDeath: v.optional(v.boolean()),
    visibility: PRODUCT_VISIBILITY,
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    if (user.role !== "seller")
      throw new Error("Only sellers can add products");

    return await ctx.db.insert("products", {
      sellerId: user._id,
      name: args.name,
      description: args.description,
      price: args.price,
      currency: args.currency,
      inventory: args.inventory,
      category: args.category,
      condition: args.condition,
      images: args.images,
      status: "active",
      tags: [],
      streamId: args.streamId,
      subcategory: args.subcategory,
      flashSale: args.flashSale,
      acceptOffers: args.acceptOffers,
      reserveForLive: args.reserveForLive,
      shippingProfile: args.shippingProfile,
      hasHazardousMaterials: args.hasHazardousMaterials,
      variants: args.variants,
      quantity: args.quantity,
      isAuction: args.isAuction,
      startingBid: args.startingBid,
      suddenDeath: args.suddenDeath,
      visibility: args.visibility,
    });
  },
});

/**
 * @name getSellerProfile
 * @description Get a seller's profile
 */
export const getSellerProfile = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const seller = await ctx.db.get(args.userId);
    if (!seller || seller.role !== "seller") {
      throw new Error("Seller not found");
    }

    const activeStreams = await ctx.db
      .query("streams")
      .filter((q) =>
        q.and(
          q.eq(q.field("hostId"), args.userId),
          q.eq(q.field("status"), "scheduled"),
          q.eq(q.field("status"), "live"),
        ),
      )
      .collect();

    const products = await ctx.db
      .query("products")
      .filter((q) =>
        q.and(
          q.eq(q.field("sellerId"), args.userId),
          q.eq(q.field("status"), "active"),
        ),
      )
      .collect();

    return {
      ...seller,
      activeStreams,
      products,
    };
  },
});

/**
 * @name getActiveStreams
 * @description Get all currently active streams
 */
export const getActiveStreams = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const query = ctx.db
      .query("streams")
      .withIndex("by_hostId", (q) => q.eq("hostId", user._id))
      .filter((q) => 
        q.or(
          q.eq(q.field("status"), "scheduled"),
          q.eq(q.field("status"), "live")
        )
      );

    return await query.collect();
  },
});

/**
 * @name getProductsForStream
 * @description Get all products for a given stream
 */
export const getProductsForStream = query({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args) => {
    const products = await ctx.db
      .query("products")
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();
      
    return products.filter(
      (product) => Array.isArray(product.streamId) && product.streamId.includes(args.streamId)
    );
  },
});

/**
 * @name listProducts
 * @description List all products for the current seller
 */
export const listProducts = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    if (user.role !== "seller") throw new Error("Only sellers can list products");

    return await ctx.db
      .query("products")
      .withIndex("by_sellerId_and_status", (q) => q.eq("sellerId", user._id))
      .order("desc")
      .collect();
  },
});

/**
 * @name updateProduct
 * @description Update an existing product
 */
export const updateProduct = mutation({
  args: {
    productId: v.id("products"),
    name: v.string(),
    description: v.optional(v.string()),
    price: v.number(),
    category: v.optional(v.string()),
    subcategory: v.optional(v.string()),
    inventory: v.number(),
    acceptOffers: v.boolean(),
    reserveForLive: v.boolean(),
    status: v.union(v.literal("draft"), v.literal("active"), v.literal("archived")),
    images: v.array(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    if (user.role !== "seller") throw new Error("Only sellers can update products");

    const product = await ctx.db.get(args.productId);
    if (!product) throw new Error("Product not found");
    if (product.sellerId !== user._id) throw new Error("You can only update your own products");

    // Handle image updates
    const oldImages = product.images || [];
    const newImages = args.images || [];

    // Delete removed images from storage
    const removedImages = oldImages.filter(id => !newImages.includes(id));
    for (const imageId of removedImages) {
      try {
        await ctx.storage.delete(imageId);
      } catch (error) {
        console.error("Failed to delete image:", error);
      }
    }

    // Update the product
    await ctx.db.patch(args.productId, {
      name: args.name,
      description: args.description,
      price: args.price,
      category: args.category,
      subcategory: args.subcategory,
      inventory: args.inventory,
      acceptOffers: args.acceptOffers,
      reserveForLive: args.reserveForLive,
      status: args.status,
      images: args.images,
    });

    return { success: true };
  },
});

/**
 * @name removeProductImage
 * @description Remove an image from storage
 */
export const removeProductImage = mutation({
  args: {
    imageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    if (user.role !== "seller") throw new Error("Only sellers can remove product images");

    await ctx.storage.delete(args.imageId);
  },
});

/**
 * @name generateUploadUrl
 * @description Generate a URL for uploading product images
 */
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    if (user.role !== "seller") throw new Error("Only sellers can upload product images");

    return await ctx.storage.generateUploadUrl();
  },
});

/**
 * @name deleteProduct
 * @description Delete a product
 */
export const deleteProduct = mutation({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    if (user.role !== "seller") throw new Error("Only sellers can delete products");

    const product = await ctx.db.get(args.productId);
    if (!product) throw new Error("Product not found");
    if (product.sellerId !== user._id) throw new Error("You can only delete your own products");

    await ctx.db.delete(args.productId);
  },
});

/**
 * @name addProductsToStream
 * @description Add existing products to a stream
 */
export const addProductsToStream = mutation({
  args: {
    streamId: v.id("streams"),
    productIds: v.array(v.id("products")),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    if (user.role !== "seller") throw new Error("Only sellers can add products to streams");

    const stream = await ctx.db.get(args.streamId);
    if (!stream) throw new Error("Stream not found");
    if (stream.hostId !== user._id) throw new Error("You can only add products to your own streams");

    // Update each product's streamId array
    for (const productId of args.productIds) {
      const product = await ctx.db.get(productId);
      if (!product) continue;
      if (product.sellerId !== user._id) continue;

      const existingStreamIds = product.streamId || [];
      if (!existingStreamIds.includes(args.streamId)) {
        await ctx.db.patch(productId, {
          streamId: [...existingStreamIds, args.streamId],
        });
      }
    }

    return { success: true };
  },
});
