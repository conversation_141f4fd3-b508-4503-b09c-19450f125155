import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";

/**
 * Submit feedback from a user
 * 
 * @param type - "bug" or "feature_request"
 * @param message - The feedback message content
 * @param url - Optional URL where the feedback was submitted from
 * @param metadata - Optional metadata about the feedback
 */
export const submitFeedback = mutation({
  args: {
    type: v.union(v.literal("bug"), v.literal("feature_request")),
    message: v.string(),
    url: v.optional(v.string()),
    metadata: v.optional(v.object({
      browser: v.optional(v.string()),
      device: v.optional(v.string()),
      os: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    
    if (!user) {
      throw new ConvexError("You must be logged in to submit feedback");
    }
    
    if (args.message.trim().length === 0) {
      throw new ConvexError("Feedback message cannot be empty");
    }

    const feedbackId = await ctx.db.insert("feedback", {
      userId: user._id,
      type: args.type,
      message: args.message,
      url: args.url,
      metadata: args.metadata,
      status: "new",
      createdAt: Date.now(),
    });

    return feedbackId;
  },
});

/**
 * Get all feedback (admin only)
 */
export const getFeedback = query({
  args: {
    favorites: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    
    if (!user) {
      throw new ConvexError("You must be logged in to view feedback");
    }

    const feedback = await ctx.db
      .query("feedback")
      .order("desc")
      .collect();

    const populatedFeedback = await Promise.all(
      feedback.map(async (entry) => {
        const user = await ctx.db.get(entry.userId);
        return {
          ...entry,
          user,
        };
      })
    );

    if (args.favorites) {
      const favorites = await ctx.db
        .query("favorites")
        .withIndex("by_user", (q) => q.eq("userId", user._id))
        .collect();

      const populatedFavorites = await Promise.all(
        favorites.map(async (favorite) => {
          const recordId = favorite.objectId;
          const record = await ctx.db.get(recordId);
          if (!record) return null;
          if ("userId" in record) {
            const recordUser = await ctx.db.get(record.userId as Id<"users">);
            return {
              ...record,
              user: recordUser,
            };
          }
          return record;
        })
      );

      return populatedFavorites.filter(Boolean);
    }

    return populatedFeedback;
  },
});

/**
 * Update feedback status (admin only)
 */
export const updateFeedbackStatus = mutation({
  args: {
    feedbackId: v.id("feedback"),
    status: v.union(
      v.literal("new"),
      v.literal("in_progress"),
      v.literal("done"),
      v.literal("rejected")
    ),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    
    if (!user) {
      throw new ConvexError("You must be logged in to update feedback");
    }

    const feedback = await ctx.db.get(args.feedbackId);
    if (!feedback) {
      throw new ConvexError("Feedback not found");
    }

    await ctx.db.patch(args.feedbackId, {
      status: args.status,
      notes: args.notes,
      updatedAt: Date.now(),
      updatedBy: user._id,
    });

    return args.feedbackId;
  },
});

/**
 * Get feedback for current user
 */
export const getUserFeedback = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    
    if (!user) {
      throw new ConvexError("You must be logged in to view your feedback");
    }
    
    const feedback = await ctx.db
      .query("feedback")
      .filter(q => q.eq(q.field("userId"), user._id))
      .order("desc")
      .collect();

    return feedback;
  },
}); 


/**
 * @name updateBatchFeedbacks
 * @description Update a batch of feedbacks
 * @param feedbackIds - The IDs of the feedbacks
 * @param status - The status of the feedbacks
 * @param type - The type of the feedbacks
 * @param isDelete - Whether to delete the feedbacks
 */
export const updateBatchFeedbacks = mutation({
  args: {
    feedbackIds: v.array(v.id("feedback")),
    status: v.optional(v.string()),
    type: v.optional(v.string()),
    isDelete: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) {
      throw new Error("Not authenticated updateBatchFeedbacks");
    }

    for (const feedbackId of args.feedbackIds) {
      const feedback = await ctx.db.get(feedbackId);

      if (!feedback) continue;

      if (args.isDelete) {
        await ctx.db.delete(feedbackId);
        continue;
      }

      const updates: Record<string, any> = {
        updatedAt: Date.now(),
        updatedBy: currentUser._id,
      };

      if (args.status) {
        updates.status = args.status;
      }

      if (args.type) {
        updates.type = args.type;
      }

      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(feedbackId, updates);
      }
    }

    return { success: true };
  },
});

/**
 * delete feedback
 */
export const deleteFeedback = mutation({
  args: {
    feedbackId: v.id("feedback"),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) {
      throw new Error("Not authenticated deleteFeedback");
    }

    const feedback = await ctx.db.get(args.feedbackId);

    if (!feedback) {
      throw new Error("Feedback not found");
    }

    await ctx.db.delete(args.feedbackId);

    return { success: true };
  },
});
