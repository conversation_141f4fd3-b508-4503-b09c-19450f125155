import { mutation, query } from "./_generated/server";
import { ConvexError, v } from "convex/values";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";

/**
 * Toggle a favorite
 * @param objectId - The ID of the object to toggle
 * @param objectType - The type of object to toggle
 */
export const toggle = mutation({
  args: {
    objectId: v.union(v.id("contacts"), v.id("tasks"), v.id("users"), v.id("feedback")),
  },
  async handler(ctx, args) {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new ConvexError("User not found");
    }

    const favorite = await ctx.db
      .query("favorites")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("objectId"), args.objectId))
      .first();

    if (!favorite) {
      await ctx.db.insert("favorites", {
        objectId: args.objectId as Id<"users"> | Id<"tasks"> | Id<"feedback">,
        userId: user._id,
      });
    } else {
      await ctx.db.delete(favorite._id);
    }
  },
});

/**
 * Get all favorites for a user
 */
export const get = query({
  args: {},
  async handler(ctx, args) {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new ConvexError("User not found");
    }

    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();

    const populatedFavorites = await Promise.all(
      favorites.map(async (favorite) => {
        const recordId = favorite.objectId;
        
        // Determine the table name
        let tableName = "users"; // Default

        // Try to detect the table from the recordId
        try {
          // Try to access __tableName as any
          const idAny = recordId as any;
          if (idAny && idAny.__tableName) {
            tableName = idAny.__tableName;
          } else {
            // Try to infer from ID string format
            const idStr = String(recordId);
            
            // Check known patterns
            if (idStr.includes("users:")) {
              tableName = "users";
            } else if (idStr.includes("tasks:")) {
              tableName = "tasks";
            } else if (idStr.includes("feedback:")) {
              tableName = "feedback";
            }
          }
        } catch (e) {
          console.error("Error determining table name:", e);
        }

        const record = await ctx.db.get(recordId);

        if (!record) return null;

        // Choose appropriate icon based on table
        let icon = "IconSquareRoundedCheck";
        if (tableName === "users") {
          icon = "IconUser";
        } else if (tableName === "feedback") {
          icon = "IconMessage";
        }

        return {
          ...favorite,
          _id: favorite._id,
          name:
            "name" in record && record.name
              ? record.name
              : "title" in record && record.title
                ? record.title
                : "Unknown",
          icon,
          isActive: false,
          folderId: favorite.folderId,
          position: favorite.position,
          tableName
        };
      }),
    );

    const result = populatedFavorites.filter(Boolean);
    return result;
  },
});

/**
 * Create a favorite folder
 */
export const createFolder = mutation({
  args: {
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("User not found");

    // Get highest position
    const folders = await ctx.db
      .query("favoriteFolders")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();

    const maxPosition = Math.max(...folders.map((f) => f.position ?? 0), -1);

    return await ctx.db.insert("favoriteFolders", {
      name: args.name,
      userId: user._id,
      isOpen: true,
      position: maxPosition + 1000,
    });
  },
});

/**
 * Update a favorite folder
 */
export const updateFolder = mutation({
  args: {
    folderId: v.id("favoriteFolders"),
    name: v.optional(v.string()),
    isOpen: v.optional(v.boolean()),
    position: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { folderId, ...updates } = args;
    await ctx.db.patch(folderId, updates);
  },
});

/**
 * Delete a favorite folder
 */
export const deleteFolder = mutation({
  args: { folderId: v.id("favoriteFolders") },
  async handler(ctx, args) {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new ConvexError("User not found");
    }

    const folder = await ctx.db.get(args.folderId);
    if (!folder) {
      throw new ConvexError("Folder not found");
    }

    if (folder.userId !== user._id) {
      throw new ConvexError("Not authorized");
    }

    // Find all favorites in this folder
    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("folderId"), args.folderId))
      .collect();

    for (const favorite of favorites) {
      await ctx.db.delete(favorite._id);
    }

    await ctx.db.delete(args.folderId);
  },
});

/**
 * Get all favorite folders for a user
 */
export const getFolders = query({
  args: {},
  async handler(ctx) {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new ConvexError("User not found");
    }

    const folders = await ctx.db
      .query("favoriteFolders")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();

    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();

    const itemTitles = new Map();

    for (const favorite of favorites) {
      const item = await ctx.db.get(favorite.objectId);
      if (item) {
        itemTitles.set(
          favorite.objectId,
          "title" in item && item.title
            ? item.title
            : "name" in item && item.name
              ? item.name
              : "",
        );
      }
    }

    return folders.map((folder) => ({
      ...folder,
      favorites: favorites
        .filter((favorite) => favorite.folderId === folder._id)
        .map((favorite) => ({
          ...favorite,
          title: itemTitles.get(favorite.objectId),
        })),
    }));
  },
});

/**
 * Update a favorite item
 */
export const updateItem = mutation({
  args: {
    itemId: v.id("favorites"),
    folderId: v.optional(v.id("favoriteFolders")),
    position: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { itemId, folderId, position } = args;
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new ConvexError("User not found");
    }

    const favorite = await ctx.db
      .query("favorites")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("_id"), itemId))
      .first();

    if (!favorite) {
      throw new ConvexError("Favorite not found");
    }

    await ctx.db.patch(favorite._id, { folderId, position: args.position });
  },
});