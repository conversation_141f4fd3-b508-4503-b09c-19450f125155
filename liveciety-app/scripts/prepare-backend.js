import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const workspaceRoot = path.resolve(__dirname, '..');
const mobileAppRoot = path.resolve(workspaceRoot, 'apps/mobile');

// Helper function to copy directory recursively
function copyDir(src, dest) {
  if (!fs.existsSync(src)) {
    console.warn(`Source directory ${src} does not exist`);
    return false;
  }

  // Create destination directory if it doesn't exist
  fs.mkdirSync(dest, { recursive: true });

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      // Skip node_modules and other build artifacts
      if (['node_modules', '.git', 'dist', 'build', '.next'].includes(entry.name)) {
        continue;
      }
      copyDir(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }

  return true;
}

// Define packages to copy
const packages = [
  {
    name: 'lib',
    src: path.resolve(workspaceRoot, 'packages/lib'),
    dest: path.resolve(mobileAppRoot, 'packages/lib')
  },
  {
    name: 'assets',
    src: path.resolve(workspaceRoot, 'packages/assets'),
    dest: path.resolve(mobileAppRoot, 'packages/assets')
  },
  {
    name: 'backend',
    src: path.resolve(workspaceRoot, 'packages/backend'),
    dest: path.resolve(mobileAppRoot, 'packages/backend')
  }
];

console.log('🚀 Preparing packages for EAS build...');

// Copy all packages
for (const pkg of packages) {
  console.log(`📦 Copying ${pkg.name}...`);
  
  if (copyDir(pkg.src, pkg.dest)) {
    console.log(`✅ Successfully copied packages/${pkg.name}`);
  } else {
    console.log(`⚠️  packages/${pkg.name} not found, creating placeholder...`);
    
    // Create placeholder package.json for missing packages
    fs.mkdirSync(pkg.dest, { recursive: true });
    const placeholderPackageJson = {
      "name": `@workspace/${pkg.name}`,
      "version": "1.0.0",
      "description": `Placeholder ${pkg.name} package for EAS build`,
      "main": "index.js",
      "private": true
    };
    
    fs.writeFileSync(
      path.join(pkg.dest, 'package.json'),
      JSON.stringify(placeholderPackageJson, null, 2)
    );
    
    // Create placeholder index.js
    fs.writeFileSync(
      path.join(pkg.dest, 'index.js'),
      `// Placeholder ${pkg.name} package for EAS build\nexport default {};\n`
    );
  }
}

// Handle Convex-specific logic (keeping your original logic)
const backendPath = path.resolve(workspaceRoot, 'packages/backend');
const mobileConvexPath = path.resolve(mobileAppRoot, 'convex');
const generatedPath = path.join(backendPath, 'convex/_generated');
const mobileGeneratedPath = path.join(mobileConvexPath, '_generated');

// Copy generated API files to mobile app for EAS builds
if (fs.existsSync(generatedPath)) {
  console.log('📋 Copying Convex generated files...');
  
  // Ensure mobile convex directory exists
  fs.mkdirSync(mobileGeneratedPath, { recursive: true });

  // Create package.json for mobile convex
  const packageJsonPath = path.join(mobileConvexPath, 'package.json');
  const packageJsonContent = {
    "name": "@workspace/backend-mobile",
    "version": "1.0.0",
    "description": "Generated Convex API files for mobile app",
    "main": "./_generated/api.js",
    "types": "./_generated/api.d.ts",
    "private": true
  };

  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJsonContent, null, 2));
  console.log('✅ Created mobile convex package.json');

  // Copy generated files
  const filesToCopy = ['api.js', 'api.d.ts', 'dataModel.d.ts', 'server.js', 'server.d.ts'];

  filesToCopy.forEach(file => {
    const sourcePath = path.join(generatedPath, file);
    const destPath = path.join(mobileGeneratedPath, file);

    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✅ Copied ${file} to mobile app`);
    }
  });

  console.log('✅ Generated API files copied to mobile app');
} else {
  console.log('⚠️  Generated files not found - run convex dev first');
}

// Ensure the mobile app can find the copied packages by updating Metro paths
const metroConfigPath = path.join(mobileAppRoot, 'metro.config.js');
if (fs.existsSync(metroConfigPath)) {
  console.log('✅ Metro config found - packages should be accessible');
} else {
  console.warn('⚠️  Metro config not found at expected location');
}

console.log('🎉 Package preparation complete!');
console.log('📁 Copied packages are available at:');
packages.forEach(pkg => {
  console.log(`   - ${pkg.dest}`);
});