import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";

export const sendMessage = mutation({
  args: {
    streamId: v.id("streams"),
    text: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be logged in to send a message.");
    }
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found.");
    }

    const stream = await ctx.db.get(args.streamId);
    if (!stream || stream.status !== "live") {
        throw new Error("Cannot send message to a non-live or non-existent stream.");
    }

    // Moderation Check
    if (stream.blockedUserIds?.includes(userId)) {
        throw new Error("You are blocked from sending messages in this stream.");
    }

    await ctx.db.insert("chatMessages", {
      streamId: args.streamId,
      userId: userId,
      userName: user.name ?? user.email ?? "Anonymous",
      text: args.text,
    });
    return null;
  },
});

export const listMessages = query({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("chatMessages")
      .withIndex("by_streamId", (q) => q.eq("streamId", args.streamId))
      .order("asc") 
      .take(100); 
  },
});
