// This file is for internal functions that might be needed by other modules,
// but are not part of the public API, or to avoid circular dependencies.
// For example, an internal way to get a user ID without exposing it directly if not needed.

import { internalQuery } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server"; // Corrected import
import { Id } from "./_generated/dataModel";

// A more strongly typed internal getter for user ID.
export const getUserId = internalQuery({
  handler: async (ctx): Promise<Id<"users"> | null> => {
    return await getAuthUserId(ctx);
  },
});
