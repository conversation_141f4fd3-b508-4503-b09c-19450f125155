/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          light: "#E0F2FE", // Light blue for backgrounds or highlights
          DEFAULT: "#0EA5E9", // Sky blue - main primary color
          hover: "#0284C7", // Darker sky blue for hover
          dark: "#075985", // Even darker for text or borders
        },
        secondary: "#475569", // Slate gray for text
        accent: "#F59E0B", // Amber for accents
        danger: "#EF4444", // Red for errors or destructive actions
      },
      spacing: {
        section: "3rem",
      },
      borderRadius: {
        container: "0.5rem",
      },
      fontFamily: {
        sans: ['"Inter Variable"', "sans-serif"],
      },
    },
  },
  plugins: [],
};
