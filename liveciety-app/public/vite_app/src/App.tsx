import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { useState } from "react";
import { CreateProductForm } from "./CreateProductForm";
import { ProductList } from "./ProductList";
import { CreateStream } from "./CreateStream";
import { StreamList, ViewState as StreamListViewState } from "./StreamList"; // Import ViewState
import { StreamHostView } from "./StreamHostView";
import { StreamView } from "./StreamView";
import { Id } from "../convex/_generated/dataModel";

type View =
  | "home"
  | "products"
  | "createProduct"
  | "streams"
  | "createStream"
  | "viewStream"
  | "hostStream";

// This ViewState is used internally by App.tsx
export interface AppViewState { // Renamed to avoid conflict and exported if needed elsewhere
  type: View;
  streamId?: Id<"streams">;
}

export default function App() {
  const [currentView, setCurrentView] = useState<AppViewState>({ type: "home" });

  // Adapt AppViewState to StreamListViewState for the StreamList component
  const handleSetCurrentViewForStreamList = (view: StreamListViewState) => {
    if (view.type === "viewStream" && view.streamId) {
      setCurrentView({ type: "viewStream", streamId: view.streamId });
    } else {
      // Handle other potential view types from StreamList if necessary
      // For now, assume it only navigates to viewStream or a generic type
      setCurrentView({ type: view.type as View });
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-100">
      <header className="sticky top-0 z-20 bg-white/90 backdrop-blur-md h-16 flex justify-between items-center border-b shadow-sm px-4 sm:px-6 lg:px-8">
        <h2
          className="text-2xl font-bold text-primary cursor-pointer"
          onClick={() => setCurrentView({ type: "home" })}
        >
          Whatnot Clone
        </h2>
        <nav className="flex items-center space-x-2 sm:space-x-4">
          <button
            onClick={() => setCurrentView({ type: "products" })}
            className="text-gray-600 hover:text-primary transition-colors font-medium px-2 py-1 rounded"
          >
            Products
          </button>
          <button
            onClick={() => setCurrentView({ type: "streams" })}
            className="text-gray-600 hover:text-primary transition-colors font-medium px-2 py-1 rounded"
          >
            Live
          </button>
          <Authenticated>
            <button
              onClick={() => setCurrentView({ type: "createProduct" })}
              className="text-gray-600 hover:text-primary transition-colors font-medium px-2 py-1 rounded"
            >
              Sell Item
            </button>
            <button
              onClick={() => setCurrentView({ type: "createStream" })}
              className="text-green-600 hover:text-green-700 transition-colors font-medium px-2 py-1 rounded border border-green-600 hover:border-green-700"
            >
              Go Live
            </button>
          </Authenticated>
          <SignOutButton />
        </nav>
      </header>
      <main className="flex-1 p-4 sm:p-6 lg:p-8">
        <div className="max-w-full mx-auto">
          <Content currentView={currentView} setCurrentView={setCurrentView} setCurrentViewForStreamList={handleSetCurrentViewForStreamList}/>
        </div>
      </main>
      <Toaster richColors />
    </div>
  );
}

function Content({
  currentView,
  setCurrentView,
  setCurrentViewForStreamList,
}: {
  currentView: AppViewState;
  setCurrentView: (view: AppViewState) => void;
  setCurrentViewForStreamList: (view: StreamListViewState) => void; // Add this prop
}) {
  const loggedInUser = useQuery(api.auth.loggedInUser);

  if (loggedInUser === undefined && currentView.type !== "products" && currentView.type !== "streams" && currentView.type !== "viewStream") {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
      </div>
    );
  }

  switch (currentView.type) {
    case "products":
      return <ProductList />;
    case "createProduct":
      return (
        <Authenticated>
          <CreateProductForm onProductCreated={() => setCurrentView({ type: "products" })} />
        </Authenticated>
      );
    case "streams":
      return <StreamList setCurrentView={setCurrentViewForStreamList} />; // Use the adapted handler
    case "createStream":
      return (
        <Authenticated>
          <CreateStream onStreamCreated={(streamId) => setCurrentView({ type: "hostStream", streamId })} />
        </Authenticated>
      );
    case "hostStream":
      if (!currentView.streamId) return <p>Error: Stream ID missing.</p>;
      return (
        <Authenticated>
          <StreamHostView streamId={currentView.streamId} onStreamEnded={() => setCurrentView({type: "streams"})} />
        </Authenticated>
      );
    case "viewStream":
      if (!currentView.streamId) return <p>Error: Stream ID missing.</p>;
      return <StreamView streamId={currentView.streamId} />;
    case "home":
    default:
      return (
        <div className="flex flex-col items-center justify-center text-center py-12">
          <div className="bg-white p-8 rounded-xl shadow-xl max-w-2xl">
            <h1 className="text-5xl font-extrabold text-primary mb-6">
              Welcome to the Marketplace!
            </h1>
            <Authenticated>
              <p className="text-2xl text-gray-700 mb-8">
                Hello, {loggedInUser?.name ?? loggedInUser?.email ?? "friend"}! What would you like to do?
              </p>
            </Authenticated>
            <Unauthenticated>
              <p className="text-2xl text-gray-700 mb-8">
                Sign in to start buying, selling, and streaming.
              </p>
              <div className="max-w-sm mx-auto">
                <SignInForm />
              </div>
            </Unauthenticated>
            <div className="mt-10 grid grid-cols-1 sm:grid-cols-2 gap-4">
                <button
                  onClick={() => setCurrentView({ type: "products" })}
                  className="w-full px-8 py-3 bg-primary text-white font-semibold rounded-lg shadow-md hover:bg-primary-hover transition-transform transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50"
                >
                  Browse Products
                </button>
                 <button
                  onClick={() => setCurrentView({ type: "streams" })}
                  className="w-full px-8 py-3 bg-rose-500 text-white font-semibold rounded-lg shadow-md hover:bg-rose-600 transition-transform transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:ring-opacity-50"
                >
                  Watch Live Streams
                </button>
                <Authenticated>
                  <button
                    onClick={() => setCurrentView({ type: "createProduct" })}
                    className="w-full px-8 py-3 bg-sky-500 text-white font-semibold rounded-lg shadow-md hover:bg-sky-600 transition-transform transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-opacity-50"
                  >
                    List an Item
                  </button>
                  <button
                    onClick={() => setCurrentView({ type: "createStream" })}
                    className="w-full px-8 py-3 bg-green-500 text-white font-semibold rounded-lg shadow-md hover:bg-green-600 transition-transform transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                  >
                    Start a Stream
                  </button>
                </Authenticated>
              </div>
          </div>
        </div>
      );
  }
}
