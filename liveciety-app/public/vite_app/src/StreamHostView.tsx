import { useEffect, useState, useRef, FormEvent } from "react";
import { useAction, useMutation, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import {
  RoomEvent,
  Room,
  LocalTrackPublication,
  Track,
  RemoteParticipant,
  RemoteTrackPublication,
  ConnectionState,
} from "livekit-client";
import { toast } from "sonner";
import { ChatBox } from "./ChatBox"; 
import { Volume2, VolumeX, Maximize, Minimize, UserPlus, UserX, ShieldCheck, Users } from "lucide-react"; 

const LIVEKIT_WS_URL = import.meta.env.VITE_LIVEKIT_WS_URL;
const OBS_DETECTION_TIMEOUT_MS = 3500; 

export function StreamHostView({
  streamId,
  onStreamEnded,
}: {
  streamId: Id<"streams">;
  onStreamEnded: () => void;
}) {
  const streamDetails = useQuery(api.streams.getStream, { streamId });
  const generateHostToken = useAction(api.livekit.generateHostToken);
  const endStreamMutation = useMutation(api.streams.endStream);
  const goLiveMutation = useMutation(api.streams.goLive);
  const generateIngestInfoAction = useAction(api.livekit.generateIngestInfo);
  const getParticipantCountAction = useAction(api.livekit.getRoomParticipantsCount); 
  const loggedInUser = useQuery(api.auth.loggedInUser);

  const moderationData = useQuery(api.moderation.getStreamModerationData, streamId ? { streamId } : "skip");
  const addModeratorMutation = useMutation(api.moderation.addModerator);
  const removeModeratorMutation = useMutation(api.moderation.removeModerator);
  const unblockUserMutation = useMutation(api.moderation.unblockUserFromChat);

  const [newModeratorId, setNewModeratorId] = useState<string>("");

  const [room, setRoom] = useState<Room | undefined>();
  const [token, setToken] = useState<string | undefined>();
  const [error, setError] = useState<string | undefined>();
  const [isConnecting, setIsConnecting] = useState(false);
  const [isLive, setIsLive] = useState(false); 
  const [currentSource, setCurrentSource] = useState<"browser" | "obs" | "none">("none");
  const [viewerCount, setViewerCount] = useState<number | null>(null);

  const videoContainerRef = useRef<HTMLDivElement>(null); 
  const videoRef = useRef<HTMLVideoElement>(null);
  const browserFallbackTimerRef = useRef<NodeJS.Timeout | null>(null);

  const [ingestUrl, setIngestUrl] = useState<string | null>(null);
  const [streamKey, setStreamKey] = useState<string | null>(null);
  const [showObsInfo, setShowObsInfo] = useState(false);
  const [assumeObsMode, setAssumeObsMode] = useState(false);
  const [showModerationPanel, setShowModerationPanel] = useState(false);

  const [isMuted, setIsMuted] = useState(true); 
  const [volume, setVolume] = useState(0.5);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const isHost = loggedInUser?._id === streamDetails?.hostId;

  useEffect(() => {
    if (streamDetails?.status === "live" && streamDetails.roomName) {
      getParticipantCountAction({ roomName: streamDetails.roomName })
        .then(setViewerCount)
        .catch(err => console.error("Failed to fetch viewer count:", err));
      
      const intervalId = setInterval(() => {
        if (room?.state === "connected" && streamDetails?.status === "live" && streamDetails.roomName) { 
            getParticipantCountAction({ roomName: streamDetails.roomName })
            .then(setViewerCount)
            .catch(err => console.error("Failed to refresh viewer count:", err));
        }
      }, 30000); 
      return () => clearInterval(intervalId);

    } else {
      setViewerCount(null); 
    }
  }, [streamDetails?.status, streamDetails?.roomName, getParticipantCountAction, room?.state]);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.muted = isMuted;
      videoRef.current.volume = volume;
    }
  }, [isMuted, volume]);

  const toggleMute = () => setIsMuted(!isMuted);

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    if (newVolume > 0 && isMuted) setIsMuted(false);
    else if (newVolume === 0 && !isMuted) setIsMuted(true);
  };

  const toggleFullscreen = () => {
    if (!videoContainerRef.current) return;
    if (!document.fullscreenElement) {
      videoContainerRef.current.requestFullscreen().catch(err => {
        toast.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
      });
    } else {
      document.exitFullscreen();
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => setIsFullscreen(!!document.fullscreenElement);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  useEffect(() => {
    if (!LIVEKIT_WS_URL) { setError("LiveKit WebSocket URL is not configured."); toast.error("LiveKit URL not configured."); return; }
    if (!streamId || !streamDetails || !isHost) return;
    console.log("StreamHostView: Fetching host token for stream:", streamId);
    generateHostToken({ streamId }).then(setToken).catch((err) => { console.error("Failed to get host token:", err); setError("Failed to get stream token."); toast.error("Error fetching stream token."); });
  }, [streamId, generateHostToken, streamDetails?._id, isHost]);

  useEffect(() => {
    console.log("StreamHostView: Main connection useEffect triggered. Status:", streamDetails?.status, "Token:", !!token, "RoomName:", streamDetails?.roomName, "User:", loggedInUser?._id, "AssumeOBS:", assumeObsMode);

    if (streamDetails?.status !== "live") {
      if (room?.state === ConnectionState.Connected || room?.state === ConnectionState.Connecting) {
        console.log("StreamHostView: Stream is not live or ended, disconnecting room if connected. Current status:", streamDetails?.status);
        room.disconnect(true);
        setCurrentSource("none");
        setIsLive(false);
        setRoom(undefined); 
      }
      return; 
    }

    if (error && !showObsInfo) return; 
    if (!token || !LIVEKIT_WS_URL || !streamDetails?.roomName || !loggedInUser?._id) {
        console.log("StreamHostView: Prerequisites for connection not met. Token:", !!token, "RoomName:", streamDetails?.roomName, "User:", loggedInUser?._id);
        return;
    }
    
    if (room && room.state !== ConnectionState.Disconnected && room.name === streamDetails.roomName) {
      console.log("StreamHostView: Room already exists and is not disconnected. Current state:", room.state, "assumeObsMode:", assumeObsMode);
      if (assumeObsMode && room.state === ConnectionState.Connected) {
          console.log("StreamHostView: assumeObsMode is true while connected. Disabling local tracks.");
          room.localParticipant.setCameraEnabled(false).catch(console.warn);
          room.localParticipant.setMicrophoneEnabled(false).catch(console.warn);
          if (currentSource === "browser") { setCurrentSource("none"); setIsLive(false); }
      }
      return; 
    }
    
    console.log("StreamHostView: Creating new Room instance for LIVE stream. Assume OBS:", assumeObsMode);
    const newRoom = new Room({ adaptiveStream: true, dynacast: true, videoCaptureDefaults: { resolution: { width: 1280, height: 720, frameRate: 30 }}});
    setRoom(newRoom);
    setIsConnecting(true); setCurrentSource("none"); setIsLive(false); // Reset states

    const connectAndSetup = async () => {
      console.log("StreamHostView: connectAndSetup called.");
      try {
        await newRoom.connect(LIVEKIT_WS_URL, token, { autoSubscribe: false });
        console.log("StreamHostView: Connected to LiveKit room:", streamDetails.roomName);
        setIsConnecting(false);

        if (browserFallbackTimerRef.current) clearTimeout(browserFallbackTimerRef.current);

        if (!assumeObsMode) {
          console.log("StreamHostView: Not in OBS mode by default, setting timer for browser fallback.");
          browserFallbackTimerRef.current = setTimeout(async () => {
            if (newRoom.state === ConnectionState.Connected && currentSource === "none" && !isLive) { 
              console.log("StreamHostView: Browser fallback timeout. Attempting to publish local camera/mic.");
              try {
                await newRoom.localParticipant.setCameraEnabled(true);
                await newRoom.localParticipant.setMicrophoneEnabled(true);
              } catch (e) { console.error("StreamHostView: Error enabling local media after timeout:", e); toast.error("Could not start browser camera/mic."); setCurrentSource("none"); setIsLive(false); }
            } else {
              console.log("StreamHostView: Browser fallback timeout, but conditions not met. State:", newRoom.state, "CurrentSource:", currentSource, "IsLive:", isLive);
            }
          }, OBS_DETECTION_TIMEOUT_MS);
        } else {
            console.log("StreamHostView: Assuming OBS mode. Local camera/mic will not be enabled automatically.");
            await newRoom.localParticipant.setCameraEnabled(false).catch(console.warn);
            await newRoom.localParticipant.setMicrophoneEnabled(false).catch(console.warn);
        }
        
        newRoom.remoteParticipants.forEach(rp => {
          if (rp.identity === loggedInUser._id) {
            rp.trackPublications.forEach(pub => {
              if (pub.kind === Track.Kind.Video && !pub.isSubscribed) {
                console.log("StreamHostView: Found host's remote video track (OBS) on connect. Subscribing. SID:", pub.trackSid);
                pub.setSubscribed(true);
              }
            });
          }
        });

      } catch (e: any) { console.error("StreamHostView: Failed to connect to LiveKit room:", e); setError(`Failed to connect: ${e.message}`); toast.error(`Connection failed: ${e.message}`); setIsConnecting(false); setIsLive(false); }
    };
    connectAndSetup();

    const handleRemoteTrackPublished = (pub: RemoteTrackPublication, rp: RemoteParticipant) => {
      console.log("StreamHostView: RoomEvent.TrackPublished (Remote). Participant:", rp.identity, "Track SID:", pub.trackSid, "Kind:", pub.kind);
      if (rp.identity === loggedInUser._id && pub.kind === Track.Kind.Video && !pub.isSubscribed) {
        console.log("StreamHostView: Host's remote video track published (OBS). Subscribing. SID:", pub.trackSid);
        pub.setSubscribed(true);
      }
    };
    const handleTrackSubscribed = (track: Track, pub: RemoteTrackPublication, rp: RemoteParticipant) => {
      console.log("StreamHostView: RoomEvent.TrackSubscribed. Participant:", rp.identity, "Track SID:", pub.trackSid, "Kind:", track.kind, "Is host:", rp.identity === loggedInUser?._id);
      if (rp.identity === loggedInUser?._id && track.kind === Track.Kind.Video) {
        console.log("StreamHostView: Host's remote video track subscribed (OBS). SID:", pub.trackSid);
        if (browserFallbackTimerRef.current) clearTimeout(browserFallbackTimerRef.current);
        if (videoRef.current) {
            console.log("StreamHostView: Attaching host's remote video track to video element. SID:", pub.trackSid);
            track.attach(videoRef.current);
        }
        newRoom.localParticipant.setCameraEnabled(false).then(() => {
            console.log("StreamHostView: Setting currentSource to 'obs' and isLive to true. SID:", pub.trackSid);
            setCurrentSource("obs");
            setIsLive(true);
            toast.info("OBS stream displaying.");
        }).catch(console.warn);
        newRoom.localParticipant.setMicrophoneEnabled(false).catch(console.warn);
      }
    };
    const handleTrackUnsubscribed = (track: Track, pub: RemoteTrackPublication, rp: RemoteParticipant) => {
      console.log("StreamHostView: RoomEvent.TrackUnsubscribed. Participant:", rp.identity, "Track SID:", pub.trackSid, "Kind:", track.kind);
      if (rp.identity === loggedInUser._id && track.kind === Track.Kind.Video) {
        track.detach(); setCurrentSource("none"); setIsLive(false); toast.info("OBS stream stopped.");
      }
    };
    const handleLocalTrackPublished = (pub: LocalTrackPublication) => {
      console.log("StreamHostView: RoomEvent.LocalTrackPublished. Source:", pub.source, "Track SID:", pub.trackSid, "assumeObsMode:", assumeObsMode, "currentSource:", currentSource);
      if (assumeObsMode || currentSource === "obs") {
        console.log("StreamHostView: In OBS mode or OBS source active, unpublishing local track if any. SID:", pub.trackSid);
        if(pub.track) newRoom.localParticipant.unpublishTrack(pub.track, true);
        return;
      }
      if (pub.source === Track.Source.Camera && pub.videoTrack && videoRef.current) {
        console.log("StreamHostView: Local camera track published. Attaching and setting browser mode, isLive true. SID:", pub.trackSid);
        pub.videoTrack.attach(videoRef.current);
        setCurrentSource("browser");
        setIsLive(true);
      } else if (pub.source === Track.Source.Microphone) {
        console.log("StreamHostView: Local microphone track published. SID:", pub.trackSid);
        if (currentSource !== "browser") {
            console.log("StreamHostView: Setting currentSource to 'browser' (mic only for now).");
            setCurrentSource("browser");
        }
        console.log("StreamHostView: Setting isLive to true (mic published).");
        setIsLive(true);
      }
    };
    const handleLocalTrackUnpublished = (pub: LocalTrackPublication) => {
      console.log("StreamHostView: RoomEvent.LocalTrackUnpublished. Source:", pub.source, "Track SID:", pub.trackSid);
      if (pub.source === Track.Source.Camera && videoRef.current && pub.track) pub.track.detach(videoRef.current);
      const camPub = newRoom.localParticipant.getTrackPublication(Track.Source.Camera);
      const micPub = newRoom.localParticipant.getTrackPublication(Track.Source.Microphone);
      if ((!camPub || !camPub.track) && (!micPub || !micPub.track) && currentSource === "browser") {
        console.log("StreamHostView: Both local camera and mic unpublished, setting source to none, isLive false.");
        setCurrentSource("none"); setIsLive(false);
      }
    };

    newRoom.on(RoomEvent.TrackPublished, handleRemoteTrackPublished);
    newRoom.on(RoomEvent.TrackSubscribed, handleTrackSubscribed);
    newRoom.on(RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed);
    newRoom.on(RoomEvent.LocalTrackPublished, handleLocalTrackPublished);
    newRoom.on(RoomEvent.LocalTrackUnpublished, handleLocalTrackUnpublished);
    newRoom.on(RoomEvent.Disconnected, r => { console.log("StreamHostView: RoomEvent.Disconnected. Reason:", r); if (browserFallbackTimerRef.current) clearTimeout(browserFallbackTimerRef.current); setIsConnecting(false); setIsLive(false); setCurrentSource("none"); toast.info(`Disconnected: ${r || "No reason"}`); });
    newRoom.on(RoomEvent.Reconnecting, () => { console.log("StreamHostView: RoomEvent.Reconnecting."); setIsConnecting(true); toast.info("Reconnecting...")});
    newRoom.on(RoomEvent.Reconnected, async () => { 
      setIsConnecting(false);
      toast.success("Reconnected!");
      console.log("StreamHostView: RoomEvent.Reconnected. Evaluating stream state. Current source:", currentSource, "IsLive:", isLive, "AssumeOBS:", assumeObsMode);

      const hostRemoteParticipant = Array.from(newRoom.remoteParticipants.values()).find(p => p.identity === loggedInUser!._id);
      if (hostRemoteParticipant) {
        const videoPublication = Array.from(hostRemoteParticipant.trackPublications.values()).find(pub => pub.kind === Track.Kind.Video && pub.isSubscribed && pub.track);
        if (videoPublication && videoPublication.track) {
          console.log("StreamHostView: Reconnected. Host OBS track is active. Attaching and setting live. SID:", videoPublication.trackSid);
          if (videoRef.current) videoPublication.track.attach(videoRef.current);
          setCurrentSource("obs");
          setIsLive(true);
          await newRoom.localParticipant.setCameraEnabled(false).catch(console.warn);
          await newRoom.localParticipant.setMicrophoneEnabled(false).catch(console.warn);
          return; 
        } else {
          console.log("StreamHostView: Reconnected. Host remote participant found, but no active subscribed video track (OBS).");
        }
      } else {
        console.log("StreamHostView: Reconnected. Host remote participant NOT found (expected for OBS).");
      }

      const localVideoPublication = newRoom.localParticipant.getTrackPublication(Track.Source.Camera);
      if (localVideoPublication?.track) { // Corrected: Check if track exists
        console.log("StreamHostView: Reconnected. Host local camera track is published. Attaching and setting live. SID:", localVideoPublication.trackSid);
        if (videoRef.current) localVideoPublication.track.attach(videoRef.current);
        setCurrentSource("browser");
        setIsLive(true);
        return; 
      } else {
         console.log("StreamHostView: Reconnected. Host local camera track NOT found or not published.");
      }
      
      if (!assumeObsMode && currentSource === "none" && !isLive) {
        console.log("StreamHostView: Reconnected & no stream detected. Attempting browser fallback.");
        if (browserFallbackTimerRef.current) clearTimeout(browserFallbackTimerRef.current);
        browserFallbackTimerRef.current = setTimeout(async () => {
          if (newRoom.state === ConnectionState.Connected && currentSource === "none" && !isLive) {
            console.log("StreamHostView: Reconnected browser fallback timeout. Publishing local media.");
            try {
              await newRoom.localParticipant.setCameraEnabled(true);
              await newRoom.localParticipant.setMicrophoneEnabled(true);
            } catch (e) {
              console.error("StreamHostView: Reconnected error enabling local media:", e);
              toast.error("Could not start browser camera/mic after reconnect.");
            }
          }
        }, OBS_DETECTION_TIMEOUT_MS);
      } else if (assumeObsMode && !isLive) {
         console.log("StreamHostView: Reconnected. Assuming OBS mode, waiting for OBS stream to publish/subscribe.");
      } else {
        console.log("StreamHostView: Reconnected. No specific action taken by Reconnected handler logic. isLive:", isLive, "currentSource:", currentSource);
      }
    });

    return () => {
      console.log("StreamHostView: Cleanup main useEffect. Disconnecting room.");
      if (browserFallbackTimerRef.current) clearTimeout(browserFallbackTimerRef.current);
      newRoom.disconnect(true); setRoom(undefined);
      setCurrentSource("none"); setIsLive(false); setIsConnecting(false);
    };
  }, [token, streamDetails?.roomName, loggedInUser?._id, streamDetails?.status, assumeObsMode]);

  const handleGoLive = async () => {
    if (!streamId || !isHost) return;
    try {
      await goLiveMutation({ streamId });
      toast.success("Stream is now LIVE!");
    } catch (err: any) { 
      console.error("Failed to go live:", err); 
      toast.error(`Failed to go live: ${err.data?.value || err.message}`); 
    }
  };

  const handleEndStream = async () => {
    if (room) await room.disconnect(true);
    try {
      await endStreamMutation({ streamId });
      toast.success("Stream ended.");
      onStreamEnded(); 
    } catch (err) { 
      console.error("Failed to end stream:", err); 
      toast.error("Failed to end stream on server."); 
    }
  };

  const handleShowObsInfo = async () => {
    if (showObsInfo) { setShowObsInfo(false); setError(undefined); setAssumeObsMode(false); toast.info("OBS mode deactivated. Browser fallback enabled."); return; } 
    if (!streamId) { toast.error("Stream ID missing."); return; }
    setError(undefined); setIngestUrl(null); setStreamKey(null);
    try {
      const info = await generateIngestInfoAction({ streamId });
      setIngestUrl(info.ingestUrl); setStreamKey(info.streamKey);
      setShowObsInfo(true); 
      setAssumeObsMode(true); 
      toast.success("OBS info generated! OBS mode activated.");
      if (room && room.state === ConnectionState.Connected && streamDetails?.status === "live") {
        console.log("StreamHostView: OBS info shown, ensuring local cam/mic are off.");
        await room.localParticipant.setCameraEnabled(false);
        await room.localParticipant.setMicrophoneEnabled(false);
        if (currentSource === "browser") { setCurrentSource("none"); setIsLive(false); }
      }
    } catch (err: any) { 
      console.error("Failed to generate OBS info:", err); 
      const msg = `Failed to get OBS info: ${err.message || "Unknown error"}`; 
      toast.error(msg); setError(msg); 
    }
  };

  const copyToClipboard = (text: string | null) => { if (text) navigator.clipboard.writeText(text).then(() => toast.success("Copied!"), () => toast.error("Copy failed.")); };
  
  const handleAddModerator = async (e: FormEvent) => {
    e.preventDefault();
    if (!streamId || !newModeratorId.trim() || !isHost) return;
    try {
      await addModeratorMutation({ streamId, userIdToAdd: newModeratorId.trim() as Id<"users"> });
      toast.success("Moderator added."); setNewModeratorId("");
    } catch (err: any) { toast.error(`Failed to add moderator: ${err.data?.value || err.message}`); }
  };

  const handleRemoveModerator = async (userIdToRemove: Id<"users">) => {
    if (!streamId || !isHost) return;
    try {
      await removeModeratorMutation({ streamId, userIdToRemove });
      toast.success("Moderator removed.");
    } catch (err: any) { toast.error(`Failed to remove moderator: ${err.data?.value || err.message}`); }
  };

  const handleUnblockUser = async (userIdToUnblock: Id<"users">) => {
    if (!streamId || !isHost) return; 
    try {
      await unblockUserMutation({ streamId, userIdToUnblock });
      toast.success("User unblocked.");
    } catch (err: any) { toast.error(`Failed to unblock user: ${err.data?.value || err.message}`); }
  };

  const overallError = error && !showObsInfo; 
  const obsInfoError = error && showObsInfo; 

  if (overallError) return <div className="text-red-500 p-4 text-center text-xl bg-red-100 border border-red-500 rounded-md">{error}</div>;
  if (!streamDetails && isHost) return <div className="p-4 text-center">Loading stream details...</div>; 
  if (!token && isHost && streamDetails?.status === "live") return <div className="p-4 text-center">Initializing stream session...</div>;


  let statusMessage = "Stream is PENDING. Click 'Go Live!' to start.";
  if (streamDetails?.status === "live") {
    if (isConnecting) statusMessage = "Connecting to LiveKit...";
    else if (room?.state === ConnectionState.Connected) {
      if (isLive) statusMessage = `You are LIVE ${currentSource === "obs" ? "(via OBS)" : currentSource === "browser" ? "(via browser)" : "(unknown source)"}`;
      else statusMessage = `Connected. ${assumeObsMode ? "Waiting for OBS stream..." : "Waiting for video source (Browser or OBS)..."}`;
    } else if (room?.state === ConnectionState.Disconnected && !error) statusMessage = "Disconnected.";
    else if (room?.state === ConnectionState.Connecting) statusMessage = "Connecting..."; 
    else statusMessage = `Stream is Live. Status: ${room?.state || "Unknown"}. Waiting for video...`; 
  } else if (streamDetails?.status === "ended") {
    statusMessage = "This stream has ended.";
  }


  return (
    <div className="flex flex-col items-center p-4 bg-gray-900 text-white min-h-screen">
      <div className="w-full max-w-7xl mx-auto flex flex-col lg:flex-row gap-4">
        <div className="flex-grow lg:w-2/3 flex flex-col items-center">
          <h1 className="text-3xl font-bold mb-1">{streamDetails?.title || "Stream"}</h1>
          <div className="flex items-center space-x-4 mb-1">
            <p className="text-sm text-gray-400">Category: {streamDetails?.category || "N/A"}</p>
            {streamDetails?.status === "live" && viewerCount !== null && (
              <div className="flex items-center text-sm text-gray-400">
                <Users size={16} className="mr-1"/> {viewerCount} viewer(s)
              </div>
            )}
          </div>
          <p className="mb-3 text-sm text-gray-400">Room: {streamDetails?.roomName || "N/A"}</p>
          
          <div ref={videoContainerRef} className="w-full aspect-video bg-black rounded-md shadow-2xl mb-1 relative overflow-hidden group">
            <video ref={videoRef} autoPlay playsInline className="w-full h-full object-contain"></video>
            {streamDetails?.status === "live" && isLive && (
              <div className="absolute bottom-0 left-0 right-0 p-2 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button onClick={toggleMute} className="text-white p-1.5 hover:bg-white/20 rounded-full">{isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}</button>
                  <input type="range" min="0" max="1" step="0.05" value={isMuted ? 0 : volume} onChange={handleVolumeChange} className="w-20 h-1.5 accent-primary cursor-pointer"/>
                </div>
                <button onClick={toggleFullscreen} className="text-white p-1.5 hover:bg-white/20 rounded-full">{isFullscreen ? <Minimize size={20} /> : <Maximize size={20} />}</button>
              </div>
            )}
          </div>
          <p className={`mb-4 ${isLive ? 'text-green-400 animate-pulse' : 'text-yellow-400'}`}>{statusMessage}</p>
          
          <div className="flex flex-wrap justify-center gap-2 mb-6">
            {isHost && streamDetails?.status === "pending" && (<button onClick={handleGoLive} className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white font-semibold rounded-lg shadow-md text-sm">Go Live!</button>)}
            {isHost && (streamDetails?.status === "live" || streamDetails?.status === "pending") && (<button onClick={handleEndStream} className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg shadow-md text-sm" >End Stream</button>)}
            {isHost && streamDetails?.status === "live" && (<button onClick={handleShowObsInfo} className="px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white font-semibold rounded-lg shadow-md text-sm">{showObsInfo ? "Hide" : "Show"} OBS Setup</button>)}
            {isHost && streamDetails?.status === "live" && (<button onClick={() => setShowModerationPanel(!showModerationPanel)} className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-lg shadow-md text-sm">{showModerationPanel ? "Hide" : "Show"} Moderation</button>)}
          </div>
          
          {showObsInfo && isHost && streamDetails?.status === "live" && (
            <div className="w-full max-w-2xl p-6 bg-gray-800 rounded-lg shadow-xl my-4 text-sm">
              <h3 className="text-xl sm:text-2xl font-semibold text-primary mb-4">OBS Streaming Setup</h3>
              {ingestUrl && streamKey ? (
                <>
                  <p className="text-gray-300 mb-4">Use these details in OBS:</p>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-400">Server (Ingest URL):</label>
                      <div className="flex items-center"><input type="text" readOnly value={ingestUrl} className="w-full p-2 rounded-l-md bg-gray-700 border border-gray-600 text-gray-200 focus:outline-none"/><button onClick={() => copyToClipboard(ingestUrl)} className="p-2 bg-primary hover:bg-primary-hover rounded-r-md text-xs">Copy</button></div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400">Stream Key:</label>
                       <div className="flex items-center"><input type="text" readOnly value={streamKey} className="w-full p-2 rounded-l-md bg-gray-700 border border-gray-600 text-gray-200 focus:outline-none"/><button onClick={() => copyToClipboard(streamKey)} className="p-2 bg-primary hover:bg-primary-hover rounded-r-md text-xs">Copy</button></div>
                    </div>
                  </div>
                </>
              ) : !obsInfoError ? <p className="text-gray-400">Generating OBS info...</p> : null}
               <button onClick={() => {setShowObsInfo(false); setError(undefined); setAssumeObsMode(false); toast.info("OBS mode deactivated. Browser fallback enabled.");}} className="mt-4 text-sm text-gray-400 hover:text-gray-200">Hide OBS Info</button>
            </div>
          )}

          {showModerationPanel && isHost && moderationData && (
            <div className="w-full max-w-2xl p-4 sm:p-6 bg-gray-800 rounded-lg shadow-xl my-4 text-sm">
              <h3 className="text-xl sm:text-2xl font-semibold text-primary mb-4">Moderation Controls</h3>
              <form onSubmit={handleAddModerator} className="mb-6"><label htmlFor="newModId" className="block text-sm font-medium text-gray-300 mb-1">Add Moderator (User ID):</label><div className="flex gap-2"><input type="text" id="newModId" value={newModeratorId} onChange={(e) => setNewModeratorId(e.target.value)} placeholder="Enter User ID" className="flex-grow p-2 rounded-md bg-gray-700 border border-gray-600 text-gray-200 focus:outline-none focus:ring-1 focus:ring-primary"/><button type="submit" className="px-3 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md flex items-center gap-1"><UserPlus size={16}/> Add</button></div></form>
              <div className="mb-6"><h4 className="text-lg font-medium text-gray-200 mb-2">Current Moderators:</h4>{moderationData.moderators && moderationData.moderators.length > 0 ? (<ul className="space-y-1">{moderationData.moderators.map(mod => mod && (<li key={mod._id} className="flex justify-between items-center p-1.5 bg-gray-700 rounded"><span className="truncate" title={mod.name || mod._id}>{mod.name || mod._id}</span><button onClick={() => handleRemoveModerator(mod._id)} className="p-1 hover:bg-red-700 rounded text-red-400 hover:text-red-200"><UserX size={16}/></button></li>))}</ul>) : <p className="text-gray-400">No moderators yet.</p>}</div>
              <div><h4 className="text-lg font-medium text-gray-200 mb-2">Blocked Users:</h4>{moderationData.blockedUsers && moderationData.blockedUsers.length > 0 ? (<ul className="space-y-1">{moderationData.blockedUsers.map(user => user && (<li key={user._id} className="flex justify-between items-center p-1.5 bg-gray-700 rounded"><span className="truncate" title={user.name || user._id}>{user.name || user._id}</span><button onClick={() => handleUnblockUser(user._id)} className="p-1 hover:bg-green-700 rounded text-green-400 hover:text-green-200"><ShieldCheck size={16}/></button></li>))}</ul>) : <p className="text-gray-400">No users blocked from chat.</p>}</div>
            </div>
          )}
        </div>
        {streamDetails?.status === "live" && (<div className="lg:w-1/3 lg:min-h-[400px] lg:max-h-[calc(100vh-8rem)] flex-shrink-0"><ChatBox streamId={streamId} /></div>)}
      </div>
    </div>
  );
}
