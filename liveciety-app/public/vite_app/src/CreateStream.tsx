import { FormEvent, useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import { toast } from "sonner";
import { streamCategories } from "../convex/streams"; // Corrected import path

export function CreateStream({
  onStreamCreated,
}: {
  onStreamCreated: (streamId: Id<"streams">) => void;
}) {
  const [title, setTitle] = useState("");
  const [selectedProductId, setSelectedProductId] = useState<Id<"products"> | "">("");
  const [selectedCategory, setSelectedCategory] = useState<string>(streamCategories[0]); 
  
  const createStreamMutation = useMutation(api.streams.createStream);
  const allUserProducts = useQuery(api.products.listAvailableProducts);
  const loggedInUser = useQuery(api.auth.loggedInUser); 

  const userProducts = loggedInUser && allUserProducts
    ? allUserProducts.filter(p => p.sellerId === loggedInUser._id)
    : [];

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault();
    if (!title) {
      toast.error("Please enter a title for your stream.");
      return;
    }
    if (!selectedCategory) {
      toast.error("Please select a category for your stream.");
      return;
    }

    try {
      const streamId = await createStreamMutation({
        title,
        category: selectedCategory,
        productId: selectedProductId || undefined,
      });
      toast.success("Stream scheduled! You can go live from the stream page.");
      onStreamCreated(streamId); 
    } catch (error: any) {
      console.error("Failed to create stream:", error);
      toast.error(`Failed to create stream: ${error.data?.value || error.message || "Please try again."}`);
    }
  };

  return (
    <div className="max-w-lg mx-auto p-6 bg-white rounded-xl shadow-2xl">
      <h2 className="text-3xl font-bold text-gray-800 mb-6 text-center">Schedule a New Stream</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
            Stream Title
          </label>
          <input
            type="text"
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-shadow"
            placeholder="e.g., Unboxing new collectibles!"
            required
          />
        </div>
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
            Category
          </label>
          <select
            id="category"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-shadow bg-white"
            required
          >
            {streamCategories.map((category: string) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="product" className="block text-sm font-medium text-gray-700 mb-1">
            Showcase a Product (Optional)
          </label>
          <select
            id="product"
            value={selectedProductId}
            onChange={(e) => setSelectedProductId(e.target.value as Id<"products"> | "")}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-shadow bg-white"
          >
            <option value="">None</option>
            {userProducts.map((product) => (
              <option key={product._id} value={product._id}>
                {product.name}
              </option>
            ))}
          </select>
           {loggedInUser && userProducts.length === 0 && (
             <p className="text-xs text-gray-500 mt-1">You have no products to showcase.</p>
           )}
        </div>
        <button
          type="submit"
          className="w-full px-6 py-3 bg-primary text-white font-semibold rounded-lg shadow-md hover:bg-primary-hover transition-all transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 disabled:opacity-60"
          disabled={!title || !selectedCategory}
        >
          Schedule Stream
        </button>
      </form>
    </div>
  );
}
