{"name": "flex-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "convex dev --once && node setup.mjs && npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build"}, "dependencies": {"@convex-dev/auth": "^0.0.80", "clsx": "^2.1.1", "convex": "1.21.1-alpha.1", "livekit-client": "^2.1.5", "livekit-server-sdk": "^2.1.2", "lucide-react": "^0.417.0", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^1.5.0", "tailwind-merge": "^2.4.0"}, "overrides": {"convex": "1.21.1-alpha.1"}, "devDependencies": {"@eslint/js": "^9.8.0", "@types/node": "^20.14.18", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "dotenv": "^16.4.5", "eslint": "^9.8.0", "eslint-plugin-react-hooks": "^5.1.0-alpha.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.8.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.40", "prettier": "^3.3.3", "tailwindcss": "^3.4.7", "typescript": "^5.5.4", "typescript-eslint": "^8.0.0-alpha.39", "vite": "^5.4.0"}}