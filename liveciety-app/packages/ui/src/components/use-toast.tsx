import { useState, useCallback } from "react";

interface ToastOptions {
  title: string;
  description: string;
  variant?: "default" | "destructive";
  duration?: number;
}

export function useToast() {
  const [toasts, setToasts] = useState<ToastOptions[]>([]);

  const toast = useCallback((options: ToastOptions) => {
    const id = Date.now();
    setToasts((prev) => [...prev, options]);

    setTimeout(() => {
      setToasts((prev) => prev.filter((t) => t !== options));
    }, options.duration || 3000);
  }, []);

  return { toast, toasts };
} 