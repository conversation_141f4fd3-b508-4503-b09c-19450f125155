{"name": "@workspace/ui", "version": "0.0.0", "type": "module", "private": true, "sideEffects": false, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-visually-hidden": "^1.2.2", "@tanstack/react-table": "8.21.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "lucide-react": "^0.475.0", "motion": "^12.6.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "react-use-measure": "^2.1.7", "recharts": "^2.15.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.1", "tw-animate-css": "^1.2.4", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.0.8", "@turbo/gen": "^2.4.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "^10.4.21", "next": "^15.2.4", "tailwindcss": "^4.0.15", "typescript": "^5.7.3"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}, "peerDependencies": {"next": "^15.2.3"}}