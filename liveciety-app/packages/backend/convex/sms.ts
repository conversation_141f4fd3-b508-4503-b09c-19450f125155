import { v } from "convex/values";
import { internalAction, query, internalMutation } from "./_generated/server";
import { twilio } from "./integration/twilio";
import { internal } from "./_generated/api";

export const testTwilioCredentials = query({
  args: {},
  handler: async (ctx) => {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const fromNumber = process.env.TWILIO_PHONE_NUMBER;

    return {
      hasAccountSid: !!accountSid,
      accountSidLength: accountSid?.length,
      hasAuthToken: !!authToken,
      authTokenLength: authToken?.length,
      fromNumber,
      isAccountSidValid: accountSid?.startsWith("AC"),
      isAuthTokenValid: authToken?.length === 32,
    };
  },
});

export const sendSms = internalAction({
  args: {
    to: v.string(),
    body: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      let formattedTo = args.to.trim();
      if (!formattedTo.startsWith("+")) {
        formattedTo = `+1${formattedTo.replace(/\D/g, "")}`;
      }

      if (!/^\+\d{10,15}$/.test(formattedTo)) {
        throw new Error(
          "Invalid phone number format. Must be E.164 format (e.g., +**********)",
        );
      }

      if (!args.body.trim()) {
        throw new Error("Message body cannot be empty");
      }

      const status = await twilio.sendMessage(ctx, {
        to: formattedTo,
        body: args.body,
        from: process.env.TWILIO_PHONE_NUMBER!,
      });

      return {
        success: true,
        status,
        to: formattedTo,
      };
    } catch (error) {
      console.error("SMS sending failed:", {
        error:
          error instanceof Error
            ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
              }
            : "Unknown error",
        to: args.to,
        fromNumber: process.env.TWILIO_PHONE_NUMBER,
      });

      if (error instanceof Error) {
        if (error.message.includes("Authenticate")) {
          throw new Error(
            "Twilio authentication failed. Please verify your credentials and account status.",
          );
        }
        if (error.message.includes("not a valid phone number")) {
          throw new Error(
            "Invalid phone number format. Please use E.164 format (+**********).",
          );
        }
        if (error.message.includes("is not a valid message")) {
          throw new Error(
            "Invalid message content. Please check your message body.",
          );
        }
        throw new Error(`Failed to send SMS: ${error.message}`);
      }

      throw new Error("An unexpected error occurred while sending SMS");
    }
  },
});

/**
 * @name deleteVerificationCode
 * @description Delete a verification code after it expires
 */
export const deleteVerificationCode = internalAction({
  args: {
    userId: v.id("users"),
    codeId: v.id("verificationCodes"),
  },
  handler: async (ctx, args) => {
    try {
      await ctx.runMutation(internal.sms.deleteVerificationCodeMutation, {
        userId: args.userId,
        codeId: args.codeId,
      });

      return { success: true };
    } catch (error) {
      console.error("Failed to delete verification code:", {
        error:
          error instanceof Error
            ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
              }
            : "Unknown error",
        userId: args.userId,
        codeId: args.codeId,
      });

      throw new Error("Failed to delete verification code");
    }
  },
});

/**
 * @name deleteVerificationCodeMutation
 * @description Internal mutation to delete a verification code
 */
export const deleteVerificationCodeMutation = internalMutation({
  args: {
    userId: v.id("users"),
    codeId: v.id("verificationCodes"),
  },
  handler: async (ctx, args) => {
    const code = await ctx.db.get(args.codeId);
    if (code && code.userId === args.userId) {
      await ctx.db.delete(args.codeId);
    }
  },
});
