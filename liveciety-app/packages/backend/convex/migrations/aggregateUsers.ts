import { mutation, MutationCtx } from "../_generated/server";
import { aggregateUsers } from "../custom";

const aggregateUsersMigration = mutation({
  args: {},
  handler: async (ctx: MutationCtx) => {
    const users = await ctx.db.query("users").collect();

    console.log("Migrating users to aggregateUsers", users.length);

    let count = 0;

    for (const user of users) {
      await aggregateUsers.insertIfDoesNotExist(ctx, user);
      count++;
    }
    return { aggregated: count };
  }
});

export default aggregateUsersMigration; 


export const resetAggregatedUsers = mutation({
  args: {},
  handler: async (ctx) => {
    await aggregateUsers.clearAll(ctx);
    
    const users = await ctx.db.query("users").collect();
    
    console.log("Re-aggregating users", users.length);
    
    let count = 0;
    // Re-aggregate each user
    for (const user of users) {
      await aggregateUsers.insertIfDoesNotExist(ctx, user);
      count++;
    }
    
    return { 
      deleted: true,
      reAggregated: count 
    };
  }
});