import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id, Doc } from "./_generated/dataModel";
import { getCurrentUser } from "./helpers/utils";
import { api } from "./_generated/api";

interface PopulatedSavedSearch extends Doc<"savedSearches"> {
  targetUser?: Doc<"users"> & { followersCount?: number } | null;
}

/**
 * @name saveSearch
 * @description Save a search query for a user
 */
export const saveSearch = mutation({
  args: {
    searchQuery: v.string(),
    type: v.union(
      v.literal("general"),
      v.literal("user"),
      v.literal("category"),
      v.literal("product")
    ),
    targetId: v.optional(v.string()),
    isPinned: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const existingSearch = await ctx.db
      .query("savedSearches")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => 
        q.and(
          q.eq(q.field("searchQuery"), args.searchQuery),
          q.eq(q.field("type"), args.type),
          args.targetId ? q.eq(q.field("targetId"), args.targetId) : q.eq(q.field("targetId"), null)
        )
      )
      .first();

    if (existingSearch) {
      await ctx.db.patch(existingSearch._id, { 
        savedAt: Date.now(),
        isPinned: args.isPinned ?? existingSearch.isPinned
      });
      return existingSearch._id;
    }

    const savedSearchId = await ctx.db.insert("savedSearches", {
      userId: user._id,
      searchQuery: args.searchQuery,
      type: args.type,
      targetId: args.targetId,
      savedAt: Date.now(),
      isPinned: args.isPinned ?? false,
    });

    return savedSearchId;
  },
});

/**
 * @name getSavedSearches
 * @description Get all saved searches for the current user (paginated)
 */
export const getSavedSearches = query({
  args: {
    type: v.optional(
      v.union(
        v.literal("general"),
        v.literal("user"),
        v.literal("category"),
        v.literal("product")
      )
    ),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<{
    page: PopulatedSavedSearch[];
    isDone: boolean;
    continueCursor?: string;
  }> => {
    const user = await getCurrentUser(ctx);
    if (!user) return { page: [], isDone: true };

    let query = ctx.db
      .query("savedSearches")
      .withIndex("by_user", (q) => q.eq("userId", user._id));
    
    if (args.type) {
      query = query.filter((q) => q.eq(q.field("type"), args.type));
    }

    const limit = args.limit || 20;
    const { page, isDone, continueCursor } = await query.order("desc").paginate({
      cursor: args.cursor ?? null,
      numItems: limit,
    });

    const populatedPage: PopulatedSavedSearch[] = await Promise.all(
      page.map(async (search) => {
        if (search.type === "user" && search.targetId) {
          const targetUser = await ctx.db.get(search.targetId as Id<"users">);
          if (targetUser) {
            const followCounts: { followers: number; following: number } = await ctx.runQuery(api.users.getFollowCounts, { userId: targetUser._id });
            return {
              ...search,
              targetUser: {
                ...targetUser,
                followersCount: followCounts.followers,
              },
            };
          }
          return { ...search, targetUser: null };
        }
        return search;
      })
    );
    
    return { page: populatedPage, isDone, continueCursor };
  },
});

/**
 * @name getRecentSearches
 * @description Get recent searches for the current user
 */
export const getRecentSearches = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<PopulatedSavedSearch[]> => {
    const user = await getCurrentUser(ctx);
    if (!user) return [];

    const searches: Doc<"savedSearches">[] = await ctx.db
      .query("savedSearches")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .order("desc")
      .take(args.limit || 5);

    const populatedSearches: PopulatedSavedSearch[] = await Promise.all(
      searches.map(async (search) => {
        if (search.type === "user" && search.targetId) {
          const targetUser = await ctx.db.get(search.targetId as Id<"users">);
          if (targetUser) {
            const followCounts: { followers: number; following: number } = await ctx.runQuery(api.users.getFollowCounts, { userId: targetUser._id });
            return {
              ...search,
              targetUser: {
                ...targetUser,
                followersCount: followCounts.followers,
              },
            };
          }
          return { ...search, targetUser: null };
        }
        return search;
      })
    );
    
    return populatedSearches;
  },
});

/**
 * @name deleteSavedSearch
 * @description Delete a saved search
 */
export const deleteSavedSearch = mutation({
  args: {
    searchId: v.id("savedSearches"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const search = await ctx.db.get(args.searchId);
    if (!search) throw new Error("Search not found");
    
    if (search.userId !== user._id) {
      throw new Error("You can only delete your own saved searches");
    }

    await ctx.db.delete(args.searchId);
    return { success: true };
  },
});

/**
 * @name togglePinSearch
 * @description Pin or unpin a saved search
 */
export const togglePinSearch = mutation({
  args: {
    searchId: v.id("savedSearches"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const search = await ctx.db.get(args.searchId);
    if (!search) throw new Error("Search not found");
    
    if (search.userId !== user._id) {
      throw new Error("You can only modify your own saved searches");
    }

    await ctx.db.patch(args.searchId, { 
      isPinned: !(search.isPinned || false),
    });
    
    return { isPinned: !(search.isPinned || false) };
  },
});

/**
 * @name saveSuggestion
 * @description Save a search suggestion to recent searches
 */
export const saveSuggestion = mutation({
  args: {
    searchQuery: v.string(),
    type: v.union(
      v.literal("general"),
      v.literal("user"),
      v.literal("category"),
      v.literal("product")
    ),
    targetId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const savedSearchId = await ctx.db.insert("savedSearches", {
      userId: user._id,
      searchQuery: args.searchQuery,
      type: args.type,
      targetId: args.targetId,
      savedAt: Date.now(),
      isPinned: false,
    });

    return savedSearchId;
  },
}); 