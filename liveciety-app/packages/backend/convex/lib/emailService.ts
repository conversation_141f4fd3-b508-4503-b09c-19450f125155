/**
 * Email service for sending emails using Resend or Loops.so
 */
import { Resend } from 'resend';

export enum EmailProvider {
  RESEND = 'resend',
  LOOPS = 'loops'
}

/**
 * Send a transactional email using Loops.so
 */
async function sendLoopsEmail(
  to: string,
  transactionalId: string,
  userData: Record<string, any>
): Promise<boolean> {
  try {
    const apiKey = process.env.LOOPS_API_KEY || "e7e3a2a2bf976938d591a220c24ab8d6";
    
    if (!apiKey) {
      console.warn("LOOPS_API_KEY is not set. Email sending is disabled. Set this in your environment variables.");
      return true;
    }
    
    const response = await fetch('https://app.loops.so/api/v1/transactional', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        transactionalId,
        email: to,
        dataVariables: userData
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error("Failed to send Loops email:", errorData);
      return false;
    }
    
    const data = await response.json();
    return true;
  } catch (error) {
    console.error("Failed to send Loops email:", error);
    return false;
  }
}

/**
 * Send an email
 * @param to Recipient email address
 * @param subject Email subject
 * @param body Email body (text version)
 * @param replyTo Optional reply-to email address
 * @param html Optional HTML version of the email
 * @param provider Optional email provider (default: Resend)
 * @returns A promise that resolves when the email is sent
 */
export async function sendEmail(
  to: string,
  subject: string,
  body: string,
  replyTo?: string,
  html?: string,
  provider: EmailProvider = EmailProvider.RESEND
): Promise<boolean> {

  if (provider === EmailProvider.RESEND) {
    try {
      const apiKey = process.env.RESEND_API_KEY;
      
      if (!apiKey) {
        console.warn("RESEND_API_KEY is not set. Email sending is disabled. Set this in your environment variables.");
        return true;
      }
      
      const resend = new Resend(apiKey);
      
      const emailOptions = {
        from: 'Liveciety <<EMAIL>>',
        to: [to],
        subject: subject,
        text: body,
        replyTo: replyTo || '<EMAIL>',
      };
      
      if (html) {
        Object.assign(emailOptions, { html });
      }
      
      const { data, error } = await resend.emails.send(emailOptions);
      
      if (error) {
        console.error("Failed to send email:", error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error("Failed to send email:", error);
      return false;
    }
  }
  
  return false;
}

/**
 * Send a transactional email using Loops
 * @param to Recipient email address
 * @param transactionalId Loops transactional email ID
 * @param userData Data to pass to the email template
 * @returns A promise that resolves when the email is sent
 */
export async function sendTransactionalEmail(
  to: string,
  transactionalId: string,
  userData: Record<string, any>
): Promise<boolean> {
  
  return sendLoopsEmail(to, transactionalId, userData);
} 