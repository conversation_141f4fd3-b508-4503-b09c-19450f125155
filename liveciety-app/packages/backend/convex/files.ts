import {
  action,
  internalMutation,
  internalQuery,
  mutation,
  query,
  MutationCtx,
} from "./_generated/server";
import { ConvexError, v } from "convex/values";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";
import { api } from "./_generated/api";

/**
 * @name generateUploadUrl
 * @description Generate a signed URL for file uploads from _storage
 * @returns {string} The signed URL for the file upload
 */
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new ConvexError("you must be logged in to upload a file");
    }

    const result = await ctx.storage.generateUploadUrl();

    return result;
  },
});

/**
 * @name getTypeById
 * @description Get the type of an object by its ID
 * @param {string} id - The ID of the object
 * @returns {string} The type of the object
 */
export const getTypeById = internalQuery({
  args: {
    id: v.any(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

/**
 * @name deleteFileById
 * @description Delete a file by its ID from _storage
 * @param {string} storageId - The ID of the file to delete
 */
export const deleteFileById = internalMutation({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    console.log("deleteFileById mutation called with storageId:", args.storageId);
    try {
      const result = await ctx.storage.delete(args.storageId as Id<"_storage">);
      console.log("File deletion result:", result);
      return result;
    } catch (error) {
      console.error("Error deleting file:", error);
      if (error instanceof Error && error.message.includes("not found")) {
        return true;
      }
      throw error;
    }
  },
});

/**
 * @name updateTypeById
 * @description Update the type of an object by its ID and field
 * @param {string} id - The ID of the object
 * @param {string} field - The field to update (e.g., 'image' or 'coverImage')
 * @param {string} value - The value to set (usually '')
 */
export const updateTypeById = internalMutation({
  args: {
    id: v.any(),
    field: v.string(), // 'image' or 'coverImage'
    value: v.string(), // usually ''
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.id, { [args.field]: args.value });
  },
});

/**
 * @name updateFieldById
 * @description Update a specific field (image or coverImage) of a user or organization by its ID
 * @param {string} id - The ID of the object
 * @param {string} field - The field to update (e.g., 'image' or 'coverImage')
 * @param {string} value - The value to set (usually '')
 */
export const updateFieldById = internalMutation({
  args: {
    id: v.id("users"),
    field: v.string(), // 'image' or 'coverImage'
    value: v.string(), // usually ''
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.id, { [args.field]: args.value });
  },
});

/**
 * @name deleteById
 * @description Delete an object by its ID and clear the correct field
 * @param {string} id - The ID of the object
 * @param {string} storageId - The ID of the file to delete
 * @param {string} field - The field to clear (e.g., 'image' or 'coverImage')
 */
export const deleteById = action({
  args: {
    id: v.optional(v.id("users")),
    storageId: v.id("_storage"),
    field: v.optional(v.string()), // 'image' or 'coverImage'
  },
  handler: async (ctx, args) => {
    const { id, storageId, field } = args;
    console.log("deleteById action called with:", { id, storageId, field });

    if (id && field) {
      // Get the user by ID
      const user = await ctx.runQuery(internal.files.getTypeById, { id });
      console.log("Found user:", user);

      if (!user) {
        throw new Error(`User with ID ${id} not found`);
      }

      // Clear the correct field
      await ctx.runMutation(internal.files.updateFieldById, { id, field, value: "" });
      console.log("Updated user field");
    }

    if (storageId) {
      console.log("Attempting to delete file from storage:", storageId);
      await ctx.runMutation(internal.files.deleteFileById, {
        storageId: storageId as Id<"_storage">,
      });
      console.log("File deleted from storage");
    }

    return { success: true, id };
  },
});

/**
 * @name getImageUrl
 * @description Get the URL for a stored image
 * @param {Id<"_storage">} storageId - The storage ID of the image
 * @returns {string | null} The URL of the image
 */
export const getImageUrl = query({
  args: {
    storageId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    if (!args.storageId) return null;
    return await ctx.storage.getUrl(args.storageId);
  },
});

/**
 * @name deleteFile
 * @description Public action to delete a file from storage
 * @param {Id<"_storage">} storageId - The storage ID of the file to delete
 * @returns {Promise<{ success: boolean; id?: Id<"users"> }>}
 */
export const deleteFile = action({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args): Promise<{ success: boolean; id?: Id<"users"> }> => {
    const identity = await ctx.auth.getUserIdentity();
    
    if (!identity) {
      throw new ConvexError("you must be logged in to delete a file");
    }

    return await ctx.runAction(api.files.deleteById, {
      storageId: args.storageId,
    });
  },
});

/**
 * Helper function to extract storage ID from various formats
 * @param imageValue - Can be a storage ID, URL with storageId parameter, or undefined
 * @returns The extracted storage ID or null if not found
 */
export const extractStorageId = internalQuery({
  args: {
    imageValue: v.optional(v.union(v.string(), v.id("_storage"))),
  },
  handler: async (ctx, args) => {
    const { imageValue } = args;
    
    if (!imageValue) return null;
    
    // If it's already a storage ID object, return it directly
    if (typeof imageValue !== 'string') {
      return imageValue;
    }
    
    // If it's an empty string, return null
    if (imageValue.trim() === '') {
      return null;
    }
    
    // If it's already a storage ID (not a URL), return it
    // Storage IDs are UUIDs that look like: "kg2ar4by4v8qg3w65nhn2bk8g17jwmz2"
    if (!imageValue.includes('http') && !imageValue.includes('/getImage')) {
      return imageValue as Id<"_storage">;
    }
    
    // Extract storage ID from URL with storageId query parameter
    const storageIdMatch = imageValue.match(/storageId=([^&]+)/);
    if (storageIdMatch && storageIdMatch[1]) {
      return storageIdMatch[1] as Id<"_storage">;
    }
    
    // If we can't extract a storage ID, return null
    console.warn("Could not extract storage ID from image value:", imageValue);
    return null;
  },
});

/**
 * @name updateProfileImage
 * @description Update only the profile image field of a user
 * @param {Id<"users">} userId - The ID of the user
 * @param {Id<"_storage">} storageId - The storage ID of the new image
 */
export const updateProfileImage = mutation({
  args: {
    userId: v.id("users"),
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const { userId, storageId } = args;
    
    // Get the current user
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }
    
    // If there's an existing image, delete it
    if (user.image) {
      const oldStorageId = await ctx.runQuery(internal.files.extractStorageId, { imageValue: user.image });
      if (oldStorageId) {
        try {
          await ctx.storage.delete(oldStorageId);
          console.log("Successfully deleted old profile image:", oldStorageId);
        } catch (error) {
          console.error("Failed to delete old profile image:", error);
        }
      }
    }
    
    // Convert storage ID to URL
    const imageUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/getImage?storageId=${storageId}`;
    
    // Update the user with the new image
    await ctx.db.patch(userId, { image: imageUrl });
    
    return { success: true };
  },
});

/**
 * @name updateCoverImage
 * @description Update only the cover image field of a user
 * @param {Id<"users">} userId - The ID of the user
 * @param {Id<"_storage">} storageId - The storage ID of the new cover image
 */
export const updateCoverImage = mutation({
  args: {
    userId: v.id("users"),
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const { userId, storageId } = args;
    
    // Get the current user
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }
    
    // If there's an existing cover image, delete it
    if (user.coverImage) {
      const oldStorageId = await ctx.runQuery(internal.files.extractStorageId, { imageValue: user.coverImage });
      if (oldStorageId) {
        try {
          await ctx.storage.delete(oldStorageId);
          console.log("Successfully deleted old cover image:", oldStorageId);
        } catch (error) {
          console.error("Failed to delete old cover image:", error);
        }
      }
    }
    
    // Convert storage ID to URL
    const coverImageUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/getImage?storageId=${storageId}`;
    
    // Update the user with the new cover image
    await ctx.db.patch(userId, { coverImage: coverImageUrl });
    
    return { success: true };
  },
});

/**
 * @name removeProfileImage
 * @description Remove the profile image of a user
 * @param {Id<"users">} userId - The ID of the user
 */
export const removeProfileImage = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const { userId } = args;
    
    // Get the current user
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }
    
    // If there's an existing image, delete it
    if (user.image) {
      const storageId = await ctx.runQuery(internal.files.extractStorageId, { imageValue: user.image });
      if (storageId) {
        try {
          await ctx.storage.delete(storageId);
          console.log("Successfully deleted profile image:", storageId);
        } catch (error) {
          console.error("Failed to delete profile image:", error);
          // Continue with the update even if deletion fails
        }
      }
    }
    
    // Update the user to remove the image
    await ctx.db.patch(userId, { image: undefined });
    
    return { success: true };
  },
});

/**
 * @name removeCoverImage
 * @description Remove the cover image of a user
 * @param {Id<"users">} userId - The ID of the user
 */
export const removeCoverImage = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const { userId } = args;
    
    // Get the current user
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }
    
    // If there's an existing cover image, delete it
    if (user.coverImage) {
      const storageId = await ctx.runQuery(internal.files.extractStorageId, { imageValue: user.coverImage });
      if (storageId) {
        try {
          await ctx.storage.delete(storageId);
          console.log("Successfully deleted cover image:", storageId);
        } catch (error) {
          console.error("Failed to delete cover image:", error);
          // Continue with the update even if deletion fails
        }
      }
    }
    
    // Update the user to remove the cover image
    await ctx.db.patch(userId, { coverImage: undefined });
    
    return { success: true };
  },
});
