import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";

/**
 * @name createNotification
 * @description Create a notification for a user
 */
export const createNotification = mutation({
  args: {
    userId: v.id("users"),
    type: v.string(),
    data: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new Error("Not authenticated");
    }

    const notification = await ctx.db.insert("notifications", {
      userId: args.userId,
      type: args.type,
      actorId: user._id,
      read: false,
      ...(args.data !== undefined ? { data: args.data } : {}),
    });

    return notification;
  }
});

/**
 * @name getNotifications
 * @description Get notifications for the current user with pagination
 */
export const getNotifications = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      return {
        page: [],
        isDone: true,
        continueCursor: null,
      };
    }

    const notificationsQuery = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .order("desc");

    const MAX_NOTIFICATIONS_PER_PAGE = 20;
    let currentNumItems = args.paginationOpts?.numItems ?? 10;

    if (currentNumItems > MAX_NOTIFICATIONS_PER_PAGE) {
      currentNumItems = MAX_NOTIFICATIONS_PER_PAGE;
    }

    const resolvedPaginationOpts = {
      numItems: currentNumItems,
      cursor: args.paginationOpts?.cursor || null,
    };

    const paginatedNotifications = await notificationsQuery.paginate(resolvedPaginationOpts);

    if (paginatedNotifications.page.length === 0) {
      return {
        page: [],
        isDone: paginatedNotifications.isDone,
        continueCursor: paginatedNotifications.continueCursor,
      };
    }

    // Process notifications in smaller batches to avoid hitting document limits
    const batchSize = 5;
    const enrichedNotifications = [];

    for (let i = 0; i < paginatedNotifications.page.length; i += batchSize) {
      const batchOfNotifications = paginatedNotifications.page.slice(i, i + batchSize);
      const batchDetails = await Promise.all(
        batchOfNotifications.map(async (notification) => {
          const actor = await ctx.db.get(notification.actorId as Id<"users"> || notification.userId as Id<"users">);
          if (!actor) return null;

          let avatarUrl = null;
          if (actor.image) {
            if (typeof actor.image === 'string' && (actor.image.startsWith('http://') || actor.image.startsWith('https://'))) {
              avatarUrl = actor.image;
            } else {
              try {
                avatarUrl = await ctx.storage.getUrl(actor.image);
              } catch (error) {
                console.error(`Error getting avatar URL for user ${actor._id}:`, error);
                avatarUrl = null;
              }
            }
          }

          return {
            ...notification,
            actor: {
              ...actor,
              avatarUrl
            },
          };
        })
      );
      enrichedNotifications.push(...batchDetails.filter(Boolean));
    }

    return {
      page: enrichedNotifications,
      isDone: paginatedNotifications.isDone,
      continueCursor: paginatedNotifications.continueCursor,
    };
  },
});

/**
 * @name markAsRead
 * @description Mark a notification as read
 */
export const markAsRead = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) throw new Error("Notification not found");

    if (notification.userId !== user._id) {
      throw new Error("Not authorized to mark this notification as read");
    }

    await ctx.db.patch(args.notificationId, { read: true });
    return { success: true };
  },
});

/**
 * @name markAllAsRead
 * @description Mark all notifications as read for the current user
 */
export const markAllAsRead = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    // Use a conservative approach with limited batch size
    const BATCH_LIMIT = 100; // Process max 100 notifications at a time
    
    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("read"), false))
      .order("desc")
      .take(BATCH_LIMIT);

    // Process in small batches to avoid hitting limits
    const batchSize = 10;
    let totalMarked = 0;
    
    for (let i = 0; i < unreadNotifications.length; i += batchSize) {
      const batch = unreadNotifications.slice(i, i + batchSize);
      await Promise.all(
        batch.map((notification) =>
          ctx.db.patch(notification._id, { read: true }),
        ),
      );
      totalMarked += batch.length;
    }

    return { count: totalMarked };
  },
});

/**
 * @name deleteNotification
 * @description Delete a notification
 */
export const deleteNotification = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) throw new Error("Notification not found");

    if (notification.userId !== user._id) {
      throw new Error("Not authorized to delete this notification");
    }

    await ctx.db.delete(args.notificationId);
    return { success: true };
  },
}); 