
export interface Category {
  id: string;
  title: string;
  description: string;
  image: any;
}

export interface Subcategory {
  id: string;
  title: string;
  description?: string;
  image?: any;
}

export const categories: Category[] = [
  {
    id: "trading_cards",
    title: "Trading Cards",
    description:
      "Magic, Yu-Gi-Oh!, One Piece, Disney, and other trading card collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Ftrading_cards.png?alt=media&token=8aa25e7a-4f7c-475b-aeed-92ff60f31f55',
  },
  {
    id: "sports_cards",
    title: "Sports Cards",
    description: "Baseball, basketball, football, and other sports cards",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fsports_cards.png?alt=media&token=77a941d4-6a9b-4bbf-908c-237b6dfd1069',
  },
  {
    id: "sports_memorabilia",
    title: "Sports Memorabilia",
    description: "Jerseys, autographs, and other sports collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fsports_memorabilia.png?alt=media&token=e285ad36-489e-466b-86a5-cd603880f99b',
  },
  {
    id: "toys_hobbies",
    title: "Toys & Hobbies",
    description: "Action figures, models, and other collectible toys",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Ftoys_hobbies.png?alt=media&token=ea58dabc-0b69-4c8f-9cb1-8742c6009617',
  },
  {
    id: "coins_notes",
    title: "Coins & Notes",
    description: "Rare coins, currency, and numismatic items",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fcoins_notes.png?alt=media&token=87c1355d-a61d-4a00-a28f-c30054d8f06b',
  },
  {
    id: "storage_garage_sales",
    title: "Storage & Garage Sales",
    description: "Items from estate sales and storage unit auctions",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fstorage_garage_sales.png?alt=media&token=a02807af-ce1d-4234-b607-b5db6d87c08a',
  },
  {
    id: "handmade_crafts",
    title: "Handmade & Crafts",
    description: "Handcrafted items, artwork, and crafting supplies",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fhandmade_crafts.png?alt=media&token=4f673ef1-d79a-4f86-85e7-e20550de4913',
  },
  {
    id: "comics",
    title: "Comics",
    description: "Comic books, graphic novels, and related collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fcomics.png?alt=media&token=d8475574-085a-4fca-80f0-de3e1fd33923',
  },
  {
    id: "electronics",
    title: "Electronics",
    description: "Gadgets, computers, and electronic accessories",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Felectronics.png?alt=media&token=b48e1bb3-57ed-4d7d-9c41-41f71948b694',
  },
  {
    id: "mens_attire",
    title: "Men's Attire",
    description: "Men's clothing, shoes, and accessories",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fmens_attire.png?alt=media&token=26af1ac1-5a9b-4da5-86a2-c399b1b6388c',
  },
  {
    id: "kicks_urban_attire",
    title: "Kicks & Urban Attire",
    description: "Limited edition sneakers and streetwear fashion",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fkicks_urban_attire.png?alt=media&token=51188c6d-9077-4af1-85ce-ec3db26780ad',
  },
  {
    id: "luxury_bags_accessories",
    title: "Luxury Bags & Accessories",
    description: "Designer bags, wallets, and fashion accessories",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fluxury_bags_accessories.png?alt=media&token=0e508098-af46-48b3-a311-76da74f65b5e',
  },
  {
    id: "womens_attire",
    title: "Women's Attire",
    description: "Women's clothing, shoes, and accessories",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fwomens_attire.png?alt=media&token=896b4385-514c-4c00-a838-1dcc41c61d0c',
  },
  {
    id: "cosmetic_artistry",
    title: "Cosmetic Artistry",
    description: "Makeup, skincare, and beauty products",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fcosmetic_artistry.png?alt=media&token=401de394-079d-46a4-9b06-fe6de78d44cf',
  },
  {
    id: "jewels_gems",
    title: "Jewels & Gems",
    description: "Fine jewelry, watches, and accessories",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fjewels_gems.png?alt=media&token=4c4f6010-f773-4217-8f16-945ed78e39f0',
  },
  {
    id: "music",
    title: "Music",
    description: "Vinyl records, CDs, and music memorabilia",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fmusic.png?alt=media&token=6a5d7f52-02a0-4e3b-a1d2-bdc89db4f65c',
  },
  {
    id: "video_games",
    title: "Video Games",
    description: "Video games, consoles, and gaming accessories",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fvideo_games.png?alt=media&token=8e7d6912-0fa3-4e6f-b0b0-ea922b00b364',
  },
  {
    id: "heirlooms_antiquities",
    title: "Heirlooms & Antiquities",
    description: "Vintage furniture, decor, and collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fheirlooms_antiquities.png?alt=media&token=482e2251-3129-4484-bf33-3ed39763c54b',
  },
  {
    id: "baby_kids",
    title: "Baby & Kids",
    description: "Children's clothing, toys, and accessories",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fbaby_kids.png?alt=media&token=46ccc5f1-dc52-49ba-8941-abc80ac849d7',
  },
  {
    id: "anime_manga",
    title: "Anime & Manga",
    description: "Japanese animation and comic merchandise",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fanime_manga.png?alt=media&token=f9d87fbb-9a8a-4384-82b4-9ce6ed24a34c',
  },
  {
    id: "sports_equipment",
    title: "Sports Equipment",
    description: "Sports equipment and outdoor gear",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fsports_equipment.png?alt=media&token=7c047426-569c-4cc6-a29e-db66382ed73d',
  },
  {
    id: "pokemon",
    title: "Pokemon",
    description: "Pokemon trading cards and memorabilia",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fpokemon.png?alt=media&token=849c9290-0654-4195-8776-f6c1548ad42d',
  },
  {
    id: "motion_pictures",
    title: "Motion Pictures",
    description: "Movies, TV shows, and related collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fmotion_pictures.png?alt=media&token=6274cab2-4aa2-4914-9057-6f8a340e7b78',
  },
  {
    id: "knives_hunting",
    title: "Knives & Hunting",
    description: "Knives, hunting gear, and outdoor equipment",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fknives_hunting.png?alt=media&token=e14b5b08-9dad-44e9-911c-1840e9d19e26',
  },
  {
    id: "horror_genre",
    title: "Horror Genre",
    description: "Horror movies, books, and related collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fhorror_genre.png?alt=media&token=f4ecb95c-853e-4d23-93dc-c54a1a867031',
  },
  {
    id: "publications",
    title: "Publications",
    description: "Magazines, newspapers, and other publications",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fpublications.png?alt=media&token=e80a63d2-0f23-414d-92d6-195d22992f83',
  },
  {
    id: "pallet_bazaars",
    title: "Pallet Bazaars",
    description: "Pallet bazaars, and related collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fpallet_bazaars.png?alt=media&token=774f15e9-3b2e-4b0c-b27b-05ed52413efc',
  },
  {
    id: "drinks_treats",
    title: "Drinks & Treats",
    description: "Drinks, treats, and related collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fdrinks_treats.png?alt=media&token=472c3270-697c-4d80-ae3f-c8bf6d3137e8',
  },
  {
    id: "time_pieces",
    title: "Time Pieces",
    description: "Watches, clocks, and related collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Ftime_pieces.png?alt=media&token=945abe24-4ecd-4d12-9446-60a6a0bc6454',
  },
  {
    id: "funko_pop",
    title: "Funko Pop",
    description: "Funko Pop figures, and related collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Ffunko_pop.png?alt=media&token=2522b223-bace-44c0-b68a-c87241be8ee6',
  },
  {
    id: "disney",
    title: "Disney",
    description: "Disney merchandise, and related collectibles",
    image: 'https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Fdisney.png?alt=media&token=363d1ab1-5f86-4fdf-8ae5-3d4cdc7a8647',
  },
];

export const subcategories: Record<string, Subcategory[]> = {
  trading_cards: [
    { 
      id: "magic", 
      title: "Magic: The Gathering",
      description: "Magic: The Gathering cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fcategories%2Ftrading_cards.png?alt=media&token=8aa25e7a-4f7c-475b-aeed-92ff60f31f55",
    },
    { 
      id: "yugioh", 
      title: "Yu-Gi-Oh! Cards",
      description: "Yu-Gi-Oh! cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FYugioh%20.PNG?alt=media&token=35bb3f41-43ef-4c20-805c-3f7b69615ed2",
    },
    { 
      id: "one_piece", 
      title: "One Piece Cards",
      description: "One Piece cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOne%20piece.PNG?alt=media&token=d1e3cf35-08e5-4fda-8045-e1e2c52804cf",
    },
    { 
      id: "disney", 
      title: "Disney Cards",
      description: "Disney cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDisney.PNG?alt=media&token=0da3d3eb-7104-4df3-9ee1-02cd3254b34c",
    },
    { id: "weiss_schwarz", title: "Weiß Schwarz",
      description: "Weiß Schwarz cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FWeib.PNG?alt=media&token=744aff47-7d16-4dd2-9b09-7963eec2964b",
    },
    { id: "dragon_ball", title: "Dragon Ball Cards",
      description: "Dragon Ball cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDragon%20ball.WEBP?alt=media&token=8ab6c308-6f39-49e9-bf5d-465b8fa1588c",
    },
    { id: "veefriends", title: "VeeFriends",
      description: "VeeFriends cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FVeefriends.JPG?alt=media&token=f03d6f7b-a8f1-4510-aba6-73270764b568",
    },
    { id: "dc", title: "DC Cards",
      description: "DC cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDC.PNG?alt=media&token=5bdd228c-3a6c-47c2-9888-6ab0fc578a14",
    },
    { id: "digimon", title: "Digimon Cards",
      description: "Digimon cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDigimon.WEBP?alt=media&token=9c2e31b1-cf39-44e4-b8c6-a467afa6d7de",
    },
    { id: "metazoo", title: "MetaZoo",
      description: "MetaZoo cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FMetazoo.JPG?alt=media&token=2c935615-b50e-4437-b81b-94555687f6c2",
    },
    { id: "my_hero_academia", title: "My Hero Academia Cards",
      description: "My Hero Academia cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FMy%20hero.JPG?alt=media&token=c4a10606-09bf-4f35-b88e-a0d8bc1745fe",
    },
    { id: "flesh_and_blood", title: "Flesh & Blood",
      description: "Flesh & Blood cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FFlesh%20blood.WEBP?alt=media&token=d2500190-8312-4102-8dc2-b85ce45cc9d9",
    },
    { id: "union_arena", title: "Union Arena",
      description: "Union Arena cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FUnion%20arena.WEBP?alt=media&token=c6c9fbe5-13ff-452a-a5ee-6ef83c6ef73f",
    },
    { id: "sorcery", title: "Sorcery: Contested Realm",
      description: "Sorcery: Contested Realm cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FSorcery.JPG?alt=media&token=fe7b5c9c-f6aa-48b0-94be-a5bd057bfd8b",
    },
    { id: "other_tcg", title: "Other TCG",
      description: "Other TCG cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOtherTCG.WEBP?alt=media&token=7ad66c1f-47f0-411b-b5a2-1dcb68e75d51",
    },
  ],
  sports_cards: [
    { id: "football", title: "Football Cards",
      description: "Football cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FFootball%20cards.JPG?alt=media&token=2f9d9566-39aa-421a-bf9d-352503281714",
    },
    { id: "basketball", title: "Basketball Cards",
      description: "Basketball cards, and related collectibles",
      image: "",
    },
    { id: "baseball", title: "Baseball Cards",
      description: "Baseball cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FBaseball%20cards.JPG?alt=media&token=334dfceb-edc6-4e27-bee4-94b60b42a216",
    },
    { id: "soccer", title: "Soccer Cards",
      description: "Soccer cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FSoccer%20cards.PNG?alt=media&token=941d347a-6a59-4aa9-b518-41c3e67c83f6",
    },
    { id: "ufc", title: "UFC Cards",
      description: "UFC cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FUFC%20CARDS.jpg?alt=media&token=c96bc01c-6328-498e-80ea-cb2e6c369efe",
    },
    { id: "hockey", title: "Hockey Cards",
      description: "Hockey cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FHockey%20cards.PNG?alt=media&token=e02a187e-62a4-40eb-8dbc-712bb048f5d0",
    },
    { id: "wrestling", title: "Wrestling Cards",
      description: "Wrestling cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FWRESTLING%20CARDS.jpg?alt=media&token=6419b507-e530-447b-8b4b-bab78126a032",
    },
    { id: "motorsport", title: "Motorsport Cards",
      description: "Motorsport cards, and related collectibles",
      image: "",
    },
    { id: "other_sports", title: "Other Sports Cards",
      description: "Other sports cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOTHER%20SPORTS%20CARDS.jpg?alt=media&token=39c04992-dfae-4a72-882c-3f6422eb55f3",
    },
  ],
  sports_memorabilia: [
    { id: "football_mem", title: "Football Memorabilia",
      description: "Football memorabilia, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FFOOTBALL%20MEM.jpg?alt=media&token=43501e0b-f475-4ae7-ba97-a617c7664cc2",
    },
    { id: "basketball_mem", title: "Basketball Memorabilia",
      description: "Basketball memorabilia, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FBASKETBALL%20MEM.jpg?alt=media&token=38b67318-2fef-4b09-bdef-10d5d4b4d915",
    },
    { id: "baseball_mem", title: "Baseball Memorabilia",
      description: "Baseball memorabilia, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FBASEBALL%20MEM.jpg?alt=media&token=308df953-a433-4eb5-9e5d-3705663bb390",
    },
    { id: "soccer_mem", title: "Soccer Memorabilia",
      description: "Soccer memorabilia, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FSOCCER%20MEM.jpg?alt=media&token=8c03e8a0-8371-4977-8a73-8b8f8faa6ee1",
    },
    { id: "other_sports_mem", title: "Other Sports Memorabilia",
      description: "Other sports memorabilia, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOTHER%20SPORTS%20MEM.jpg?alt=media&token=51394247-1fa3-402a-84a9-154ac011e458",
    },
  ],
  toys_hobbies: [
    { id: "action_figures", title: "Action Figures",
      description: "Action figures, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FAction%20figures.JPG?alt=media&token=8493165a-6c28-4fa0-9be4-b0cd6129f33e",
    },
    { id: "lego", title: "LEGO",
      description: "LEGO, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FLego.JPG?alt=media&token=2ea18180-1cf5-4245-814b-874e501db18d",
    },
    { id: "diecast", title: "Diecast",
      description: "Diecast, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDiecast.JPG?alt=media&token=f3fe2ea3-78fc-4852-aeb3-7f907f12180b",
    },
    { id: "bearbrick", title: "Bearbrick",
      description: "Bearbrick, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FBear%20Rick.PNG?alt=media&token=cc4c5d0e-f2ae-4a60-8536-914d4c39122b",
    },
    { id: "kawaii", title: "Kawaii",
      description: "Kawaii, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FKAWAII%20TOYS.jpg?alt=media&token=485ef763-aeab-4bc4-a8d7-cbbff724e082",
    },
    { id: "sonny_angels", title: "Sonny Angels & Smiskis",
      description: "Sonny Angels & Smiskis, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FSONNY%20ANGELS%20TOYS.jpg?alt=media&token=77094390-b578-4954-b249-7b0ac1882da9",
    },
    { id: "plush", title: "Plush",
      description: "Plush, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FPlush.JPG?alt=media&token=3e367a9b-98fe-4570-b584-9ecdc1215fdc",
    },
    { id: "rc_vehicles", title: "Radio Control Vehicles & Toys",
      description: "Radio Control Vehicles & Toys, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FRC%20CARS.jpg?alt=media&token=5f52f5df-3607-4dae-a243-d5a65b580d5e",
    },
    { id: "models", title: "Models & Kits",
      description: "Models & Kits, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FModel.JPG?alt=media&token=8812a299-d808-4d01-894b-c5a775b4662b",
    },
    { id: "figpin", title: "FigPin",
      description: "FigPin, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FFigpin.PNG?alt=media&token=2aa31069-c7f7-42a1-9ee9-3d44f5856158",
    },
    { id: "fast_food_toys", title: "Fast Food & Cereal Toys",
      description: "Fast Food & Cereal Toys, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FFastfoodtoys.PNG?alt=media&token=96a9f05b-7fda-40ef-9668-ac120994f9cb",
    },
    { id: "vintage_toys", title: "Vintage Toys",
      description: "Vintage Toys, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FVintage%20toys.PNG?alt=media&token=7bc593d1-2d11-46ee-810a-10b578a16418",
    },
    { id: "other_toys", title: "Other Toys",
      description: "Other Toys, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOther%20toys%20.PNG?alt=media&token=a6e75000-f8e0-4a6c-9175-48813e5a9fa6",
    },
  ],
  coins_notes: [
    { id: "coins_bullion", title: "Coins & Bullion",
      description: "Coins & Bullion, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FCoins%26bullion.JPG?alt=media&token=c049567a-d484-477f-997d-77c6ea9ab6c2",
    },
    { id: "bnta_dealers", title: "BNTA Dealers",
      description: "BNTA Dealers, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FBNTA%20DEALERS.jpg?alt=media&token=cbb773e2-ca92-4a8e-9898-c53a13a17172",
    },
    { id: "paper_money", title: "Paper Money & Currency",
      description: "Paper Money & Currency, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FPaper%20money.JPG?alt=media&token=b099f4ae-f549-46d4-b677-d098e8088806",
    },
    { id: "exonumia", title: "Exonumia",
      description: "Exonumia, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FExonumia%20.JPG?alt=media&token=f0286405-50fe-4ee8-9f04-113f9953d39c",
    },
  ],
  storage_garage_sales: [
    { id: "estate_sales", title: "Estate Sales",
      description: "Estate Sales, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FEstate.JPG?alt=media&token=af217c52-4e74-4ed6-b2f6-b6329984bfcf",
    },
    { id: "storage_units", title: "Storage Unit Finds",
      description: "Storage Unit Finds, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FStorage%20unit.JPG?alt=media&token=38a9c450-14dd-4423-bc69-25e0aa70bc1a",
    },
    { id: "deal_hunting", title: "Deal Hunting",
      description: "Deal Hunting, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDEAL%20HUNTING.jpg?alt=media&token=b8e7bd2b-5b7f-4bf9-ab84-0fc090e14d5a",
    },
    { id: "garage_sales", title: "Garage Sales",
      description: "Garage Sales, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FGarage%20sales.JPG?alt=media&token=79b34d82-8708-47b8-9eec-f75b753b7168",
    },
    { id: "other_estate", title: "Other Estate Sales & Storage Units",
      description: "Other Estate Sales & Storage Units, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FESTATE%20SALE.jpg?alt=media&token=406fc0e3-3fe9-454e-ba7a-678254045bde",
    },
  ],
  handmade_crafts: [
    { id: "craft_supplies", title: "Craft Supplies",
      description: "Craft Supplies, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FCraft%20supplies.JPG?alt=media&token=b80b2a47-b5da-4a1b-ab7e-ac6a224d1107",
    },
    { id: "handmade", title: "Handmade Items",
      description: "Handmade Items, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FHandmadeitems.JPG?alt=media&token=7784ffc5-d292-4ae3-90cb-ca497364184d",
    },
  ],
  comics: [
    { id: "comic_books", title: "Comic Books",
      description: "Comic Books, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FComic%20books.JPG?alt=media&token=b0393c49-72e0-4f49-9310-5c72516a6078",
    },
    { id: "graphic_novels", title: "Graphic Novels",
      description: "Graphic Novels, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FGraphicnovls.PNG?alt=media&token=844f65e2-4439-45e7-a76a-8ed9cb2db19d",
    },
    { id: "comic_collectibles", title: "Comic Collectibles",
      description: "Comic Collectibles, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FComic%20collectibles.JPG?alt=media&token=7db48439-fd49-4f46-b0e0-4cb25f030b74",
    },
  ],
  electronics: [
    { id: "everyday_electronics", title: "Everyday Electronics",
      description: "Everyday Electronics, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FEveryday%20electrics.JPG?alt=media&token=2aba39cf-1d65-47eb-9333-1066a2023a5d",
    },
    { id: "cameras", title: "Cameras & Photography",
      description: "Cameras & Photography, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FCameras.JPG?alt=media&token=f25aa5b6-93cc-410f-bdc3-3b3ec5cf03a2",
    },
  ],
  mens_attire: [
    { id: "mens_vintage", title: "Men's Vintage Clothing",
      description: "Men's Vintage Clothing, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FVintage%20men%E2%80%99s.JPG?alt=media&token=dd2a123b-2357-41b2-86cc-b6f9449b4108",
    },
    { id: "mens_modern", title: "Men's Modern",
      description: "Men's Modern, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FModern.JPG?alt=media&token=6f698e1b-84e2-4a72-83d2-5d0a2b435469",
    },
    { id: "sports_jerseys", title: "Sports Jerseys",
      description: "Sports Jerseys, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FSports%20jerseys.JPG?alt=media&token=36a70043-7bd2-4861-8087-09ed7f02bf30",
    },
    { id: "other_mens", title: "Other Men's Fashion",
      description: "Other Men's Fashion, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOTHER%20MENS%20ATTIRE.jpg?alt=media&token=0f7fd371-5c78-41fd-837a-be16574e29cf",
    },
  ],
  kicks_urban_attire: [
    { id: "sneakers", title: "Sneakers",
      description: "Sneakers, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FSneakers%20.JPG?alt=media&token=3b5f949a-9238-4517-a645-ea558ff2c375",
    },
    { id: "streetwear", title: "Streetwear",
      description: "Streetwear, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FSneakers_%20street%20wear.JPG?alt=media&token=f703ae27-398b-4f97-bb97-5ce1074814ec",
    },
  ],
  luxury_bags_accessories: [
    { id: "luxury_bags", title: "Luxury Bags & Accessories",
      description: "Luxury Bags & Accessories, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FLuxury%20bags.JPG?alt=media&token=e9c7a21e-3f18-4e88-90dc-d0ebb70b3ba9",
    },
    { id: "midrange_bags", title: "Midrange & Fashion Bags",
      description: "Midrange & Fashion Bags, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FMid%20luxury%20bags.JPG?alt=media&token=7283ce40-32c8-4ce9-b62c-3e371be64041",
    },
    { id: "other_accessories", title: "Other Accessories",
      description: "Other Accessories, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOTHER%20LUX%20ACCESEORIES.jpg?alt=media&token=6b5c801d-7fd0-44ff-8097-02d7042bd3b0",
    },
  ],
  womens_attire: [
    { id: "womens_vintage", title: "Women's Vintage Clothing",
      description: "Women's Vintage Clothing, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FVintage%20women%E2%80%99s.JPG?alt=media&token=b1ec721f-718e-4993-a86c-a48beb9106e1",
    },
    { id: "womens_contemporary", title: "Women's Contemporary",
      description: "Women's Contemporary, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FContemp%20women%E2%80%99s.JPG?alt=media&token=cd5283d6-9087-4e90-b859-cd66568d9ddb",
    },
    { id: "womens_activewear", title: "Women's Activewear",
      description: "Women's Activewear, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FActive%20womens.JPG?alt=media&token=83c6ec4d-e822-4267-825a-3114853af8be",
    },
    { id: "other_womens", title: "Other Women's Fashion",
      description: "Other Women's Fashion, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOther%20women%E2%80%99s.JPG?alt=media&token=1df56211-46c6-4270-9d76-637a39abdf2d",
    },
  ],
  cosmetic_artistry: [
    { id: "makeup_skincare", title: "Makeup & Skincare",
      description: "Makeup & Skincare, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FMakeup%20skin.JPG?alt=media&token=e486d211-0035-4852-bb35-08f629ff75bb",
    },
    { id: "nails", title: "Nails",
      description: "Nails, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FNails.JPG?alt=media&token=895d98f9-22a1-4881-b016-9a77d8ab31eb",
    },
    { id: "fragrances", title: "Fragrances & Perfume",
      description: "Fragrances & Perfume, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FFragrances.JPG?alt=media&token=7d7e7e95-a000-4117-8b1a-c43bab186481",
    },
    { id: "hair", title: "Hair Products",
      description: "Hair Products, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FHair.JPG?alt=media&token=c0b6f751-dfb5-44e1-bbe2-8c5bb1bcb3ab",
    },
    { id: "other_cosmetics", title: "Other Cosmetic Products",
      description: "Other Cosmetic Products, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOther%20cosmetics.JPG?alt=media&token=a74d4b5f-f909-47da-b52f-868da097dcb7",
    },
  ],
  jewels_gems: [
    { id: "fine_jewelry", title: "Fine & Precious Metals",
      description: "Fine & Precious Metals, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FFINE%20JEWELERY.jpg?alt=media&token=a8158098-698d-4770-baca-0cf1e7c4e61d",
    },
    { id: "vintage_jewelry", title: "Vintage & Antique Jewelry",
      description: "Vintage & Antique Jewelry, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FAntique%20jewelry%20.JPG?alt=media&token=150b611d-170e-4f67-9a20-2d383d065bd6",
    },
    { id: "costume_jewelry", title: "Contemporary Costume Jewelry",
      description: "Contemporary Costume Jewelry, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FContemp%20jewelry%20.JPG?alt=media&token=5ac756f2-5b87-4535-927b-3a2b11ea150f",
    },
    { id: "artisan_jewelry", title: "Handcrafted & Artisan Jewelry",
      description: "Handcrafted & Artisan Jewelry, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FHandcrafted%20jewelry%20.JPG?alt=media&token=89a17edd-8961-4b2a-a979-97bd460c834c",
    },
    { id: "mens_jewelry", title: "Men's Jewelry",
      description: "Men's Jewelry, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FMen%E2%80%99s%20jewelry%20.JPG?alt=media&token=31357329-fd31-4746-9fe4-44f149ba0cdd",
    },
  ],
  music: [
    { id: "vinyl", title: "Vinyl Records",
      description: "Vinyl Records, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FVinyl%20music.JPG?alt=media&token=b75f79eb-75ea-4e17-bfeb-33f6bbfef504",
    },
    { id: "cds_cassettes", title: "CDs & Cassettes",
      description: "CDs & Cassettes, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FCd%20cassettes.JPG?alt=media&token=cbc161f4-9c72-434e-82ea-4281e060f502",
    },
    { id: "music_memorabilia", title: "Music Memorabilia",
      description: "Music Memorabilia, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FMusic%20mem.JPG?alt=media&token=b9628541-6649-46ca-94a8-bee9507f3e38",
    },
    { id: "instruments", title: "Instruments & Accessories",
      description: "Instruments & Accessories, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FInstruments%20.JPG?alt=media&token=3d07e401-0528-466b-9f41-fc4079367146",
    },
    { id: "other_music", title: "Other Music",
      description: "Other Music, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOther%20music.JPG?alt=media&token=e236e79b-d1f5-4f0f-a8df-1bfb6ec333f1",
    },
  ],
  video_games: [
    { id: "retro_games", title: "Retro Games",
      description: "Retro Games, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FRetro%20games.JPG?alt=media&token=0b74a3b2-ae09-466d-81ca-e5f0a6a1d74d",
    },
    { id: "modern_games", title: "Modern Games",
      description: "Modern Games, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FModern%20games.JPG?alt=media&token=1c2143fd-25b2-46a3-bb06-7440df5aecc8",
    },
    { id: "consoles", title: "Consoles & Accessories",
      description: "Consoles & Accessories, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FConsoles.JPG?alt=media&token=c53c3c03-3a82-44f6-b15c-53ba7a7a3c63",
    },
    { id: "game_guides", title: "Strategy Guides, Manuals, Replacement Cases",
      description: "Strategy Guides, Manuals, Replacement Cases, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FStrategy%20guides.JPG?alt=media&token=01a8f5ef-380c-400a-80e3-8b335e69977d",
    },
  ],
  heirlooms_antiquities: [
    { id: "vintage_decor", title: "Vintage Decor",
      description: "Vintage Decor, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FHeirlooms_%20vintage.JPG?alt=media&token=d95fd51a-c604-4768-a00b-bc6c40fc93d4",
    },
    { id: "antiques", title: "Antiques",
      description: "Antiques, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FAntiques%20heirlooms.JPG?alt=media&token=990a91e3-4cb7-4eb7-bcc5-60f9520d3e7e",
    },
  ],
  baby_kids: [
    { id: "baby_items", title: "Baby Items",
      description: "Baby Items, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FBaby.JPG?alt=media&token=eb82873e-57e2-456a-80b2-1a986b84ae5a",
    },
    { id: "kids_toys", title: "Kids Toys",
      description: "Kids Toys, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FKids%20toys.JPG?alt=media&token=207f1d60-85a3-4de6-a9a0-1ec71edccdca",
    },
    { id: "other_baby_kids", title: "Other Baby & Kids",
      description: "Other Baby & Kids, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FBaby%20other%20toys.JPG?alt=media&token=da0198a5-5836-400c-ae65-03febc2fb889",
    },
  ],
  anime_manga: [
    { id: "anime_figures", title: "Anime Figures",
      description: "Anime Figures, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FAnime%20figures.JPG?alt=media&token=85a881c5-3f9f-495c-8263-72d0ed02751f",
    },
    { id: "anime_collectibles", title: "Anime Collectibles",
      description: "Anime Collectibles, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FAnime%20collectibles.JPG?alt=media&token=1c0dcb43-aa58-4247-ba45-f2aa0220add6",
    },
    { id: "other_anime_manga", title: "Other Anime & Manga",
      description: "Other Anime & Manga, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FAnime%20manga.JPG?alt=media&token=80c083fd-eab1-4905-a4a8-e606f9cfcca8",
    },
  ],
  sports_equipment: [
    { id: "golf", title: "Golf",
      description: "Golf, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FGolf.JPG?alt=media&token=b2fb89b1-ee43-4b0e-a4a2-6945de6a78c9",
    },
    { id: "fishing", title: "Fishing",
      description: "Fishing, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FFishing.JPG?alt=media&token=96ea9152-d176-4b5b-a19a-6f655a96b64d",
    },
    { id: "disc_golf", title: "Disc Golf",
      description: "Disc Golf, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDisc%20golf.JPG?alt=media&token=8715ddb7-75ed-41cc-ac7d-95bbb325b9a6",
    },
    { id: "pickleball", title: "Pickleball",
      description: "Pickleball, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FPICKLEBALL%20EQIPMENT.jpg?alt=media&token=365197b2-ba69-4ce7-9394-234a2f99e029",
    },
    { id: "other_sporting", title: "Other Sporting Goods",
      description: "Other Sporting Goods, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FSports%20equipment%20other.JPG?alt=media&token=b2c6794b-5113-4560-a173-ac7b0560095d",
    }
  ],
  pokemon: [
    { id: "pokemon_cards", title: "Pokemon Cards",
      description: "Pokemon Cards, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FPokemon%20cards.JPG?alt=media&token=f8882f64-8854-4b6b-92f5-b7ba076693e0",
    },
    { id: "pokemon_collectibles", title: "Pokemon Collectibles",
      description: "Pokemon Collectibles, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FPokemon%20collectibles.JPG?alt=media&token=fabf0a5f-19da-40e3-92e4-bd4b90f2d92c",
    },
    { id: "other_pokemon", title: "Other Pokemon",
      description: "Other Pokemon, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FPOKEMON%20OTHER.jpg?alt=media&token=876701cc-761a-4e96-aab0-108964eedc09",
    },
  ],
  motion_pictures: [
    { id: "movies", title: "Movies",
      description: "Movies, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FMovies.JPG?alt=media&token=63501319-efa0-4feb-823a-c9a382666780",
    },
    { id: "tv_shows", title: "TV Shows",
      description: "TV Shows, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FTv.JPG?alt=media&token=57f697bc-2578-4311-9de5-6fcea02fa814",
    },
    { id: "other_motion_pictures", title: "Other Motion Pictures",
      description: "Other Motion Pictures, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FMovies%20other.JPG?alt=media&token=a86a3340-0276-46ff-bc84-c7c5e0864af7",
    },
  ],
  knives_hunting: [
    { id: "knives", title: "Knives",
      description: "Knives, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FKnives.JPG?alt=media&token=af95fedd-fd6b-4ec3-a142-0718d8555ab5",
    },
    { id: "hunting", title: "Hunting",
      description: "Hunting, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FHunting.JPG?alt=media&token=00f352fd-f913-479f-9bc4-3ecb9d68ba04",
    },
  ],
  horror_genre: [
    { id: "horror_movies", title: "Horror Movies",
      description: "Horror Movies, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FHorror.JPG?alt=media&token=3859ff3e-72e8-4995-bdd1-d97af7531c62",
    },
    { id: "horror_collectibles", title: "Horror Collectibles",
      description: "Horror Collectibles, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FHorror%20collectibles.JPG?alt=media&token=ff153c1e-4124-4e98-8e3a-7bb553423178",
    },
  ],
  publications: [
    { id: "magazines", title: "Magazines",
      description: "Magazines, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FMags%20pubs.JPG?alt=media&token=3598d894-59fc-473e-a083-f07e29369a67",
    },
    { id: "newspapers", title: "Newspapers",
      description: "Newspapers, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FNewspapers%20pub.JPG?alt=media&token=430fc54b-509d-4acd-8215-aa30d3175d70",
    },
    { id: "other_publications", title: "Other Publications",
      description: "Other Publications, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FOther%20publications%20.JPG?alt=media&token=c2250f03-d6b6-4c05-8d03-7e2d1a6d3c86",
    },
  ],
  pallet_bazaars: [
    { id: "pallet_bazaars", title: "Pallet Bazaars",
      description: "Pallet Bazaars, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FPallets.JPG?alt=media&token=3f79b04d-d0b2-47ed-a246-26fac25960c1",
    },
    { id: "other_pallet_bazaars", title: "Other Pallet Bazaars",
      description: "Other Pallet Bazaars, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FPALLETS%20BAZAAR%20OTHER.jpg?alt=media&token=0fde6780-02f2-4717-9136-409d301a1cc8",
    },
  ],
  drinks_treats: [
    { id: "drinks", title: "Drinks",
      description: "Drinks, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDrinks.JPG?alt=media&token=1bc5ba2c-6a9d-4b84-b014-884906953e09",
    },
    { id: "treats", title: "Treats",
      description: "Treats, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FTreats.JPG?alt=media&token=545a3249-998b-4bad-a423-a44f270133e6",
    },
  ],
  time_pieces: [
    { id: "watches", title: "Watches",
      description: "Watches, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FWatches.JPG?alt=media&token=d29be00f-7263-4837-9967-19c17c0323d7",
    },
    { id: "clocks", title: "Clocks",
      description: "Clocks, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FClocks.JPG?alt=media&token=5dadabf3-e699-48ae-85d2-598c9b3efbd7",
    },
    { id: "other_time_pieces", title: "Other Time Pieces",
      description: "Other Time Pieces, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FTIME%20PIECES%20OTHER.jpg?alt=media&token=9eca6b1a-a9f6-463a-981a-663594b2808c",
    },
  ],
  disney: [
    { id: "disney_collectibles", title: "Disney Collectibles",
      description: "Disney Collectibles, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDisney%20collectibles.JPG?alt=media&token=7a9ee23a-96a4-433f-a5a0-2a2161166a92",
    },
    { id: "other_disney", title: "Other Disney",
      description: "Other Disney, and related collectibles",
      image: "https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/andthings%2Fsubcategories%2FDisney%20other.JPG?alt=media&token=47a26a6a-3920-4f6c-bce4-fb9f55461777",
    },
  ]
};
